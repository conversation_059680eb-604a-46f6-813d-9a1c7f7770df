package org.easitline.console.job;

import java.sql.SQLException;
import java.util.List;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.console.base.Constants;
import org.easitline.console.base.Constants.JobState;
import org.easitline.console.vo.AppJobModel;
import org.easitline.console.vo.AppJobRowMapper;

/**
 * 调度任务处理类
 */
public class JobManager {
	
	private Logger logger = LogEngine.getLogger("easitline-job");
	
	protected EasyQuery getSysDefaultQuery() {
		return ServerContext.getAdminQuery();
	}
	
	/**
	 * 更新服务节点名称
	 */
	public void updateServerNodeName(String oldName, String newName) {
		String sql = "update EASI_JOB_STEP set SERVER_NAME=? where SERVER_NAME=?";
		Object[] params = {newName, oldName};
		try {
			this.getSysDefaultQuery().execute(sql, params);
		} catch (SQLException e) {
			logger.error("JobManager.updateServerNodeName出错，原因:" + e.getMessage(),e);
		}   
	}
	
	/**
	 * 更新成功状态
	 */
	public void updateSuccessState(String jobId, String nextTime) {
		if(null == jobId) {
			logger.error("任务更新成功状态失败，原因：传入的jobId为空");
			return;
		}
		String sql = "update EASI_JOB_STEP set OK_COUNT=OK_COUNT+1,LAST_OK_TIME=?,NEXT_TIME=? where JOB_ID=?";
		String now = EasyCalendar.newInstance().getDateTime("-");
		Object[] params = {now, nextTime, jobId};
		try {
			this.getSysDefaultQuery().execute(sql, params);
		} catch (SQLException e) {
			logger.error("JobManager.updateSuccessState出错，原因:" + e.getMessage(),e);
		}   
	}
	
	/**
	 * 更新失败状态
	 */
	public void updateFailState(String jobId, String nextTime) {
		if(null == jobId) {
			logger.error("任务更新失败状态失败，原因：传入的jobId为空");
			return;
		}
		String sql = "update EASI_JOB_STEP set FAIL_COUNT=FAIL_COUNT+1,LAST_FAIL_TIME=?,NEXT_TIME=? where JOB_ID=?";
		String now = EasyCalendar.newInstance().getDateTime("-");
		Object[] params = {now, nextTime, jobId};
		try {
			this.getSysDefaultQuery().execute(sql, params);
		} catch (SQLException e) {
			logger.error("JobManager.updateSuccessState出错，原因:" + e.getMessage(),e);
		}   
	}
	
	/**
	 * 删除调度任务
	 * @param jobId
	 * @throws SQLException
	 */
	public void deleteJob(String jobId) throws SQLException {
		Object[] params = {jobId};
		String sql = "delete from EASI_JOB_STEP where JOB_ID=?";
		this.getSysDefaultQuery().execute(sql, params);
		sql = "delete from easi_job_log where job_id=?";
		getSysDefaultQuery().execute(sql, params);
	}
	
	/**
	 * 更新调度任务状态
	 * @throws SQLException 
	 */
	public void updateJobState(String jobId, int jobState) throws SQLException {
		String sql = "update EASI_JOB_STEP set JOB_STATE=? where JOB_ID=?";
		Object[] params = {jobState, jobId};
		this.getSysDefaultQuery().execute(sql, params);   //更新调度任务的状态
	}
	
	/**
	 * 根据jobId获取调度任务
	 * @param jobId
	 * @return
	 * @throws SQLException
	 */
	public AppJobModel getJobById(String jobId) throws SQLException {
		String sql = "select * from EASI_JOB_STEP where JOB_ID=?";
		Object[] params = {jobId};
		AppJobModel job = this.getSysDefaultQuery().queryForRow(sql, params, new AppJobRowMapper());  //查询对应的调度任务
		
		return job;
	}
	
	/**
	 * 加载并启动该服务器节点正常的任务
	 */
	public void loadAndStartAllJobs() {
		
		String sql = "select T1.* from EASI_JOB_STEP T1";
		try {
			List<AppJobModel> list = this.getSysDefaultQuery().queryForList(sql, null,new AppJobRowMapper());
			for(AppJobModel job:list) {
				
				String isJobServer = ServerContext.getProperties("G_JOB_SERVER", "false");
				if("true".equals(isJobServer)){
					QuartzSchedulerEngine.addJob(job.getAppName(), job.getJobId(), job.getCron());
					if(job.getJobState().equals(String.valueOf(Constants.JobState.PAUSE.getKey()))) {
						QuartzSchedulerEngine.pauseJob(job.getJobId(), job.getAppName());
					} 
				}
			}
		} catch (Exception e) {
			logger.error("JobManager.loadAndStartAllJobs初始化加载调度任务失败，原因：" + e.getMessage(),e);
		} 

	}
		
	/**
	 * 新增调度任务信息
	 * @param jobId
	 * @param jobName
	 * @param cron
	 * @param marServerName
	 * @param esbServiceId
	 * @param esbServiceName
	 * @return
	 */
    public EasyResult addJobInfo(String jobId, String jobName, String appName, String cron, String marServerName, String esbServiceId, String esbServiceName, String jobDesc, 
    		                         String beginDate, String endDate, String beginTime, String endTime) {
    	
    	String sql = "insert into EASI_JOB_STEP(JOB_ID,JOB_NAME,APP_NAME,CRON,SERVER_NAME,JOB_STATE,SERVICE_ID,SERVICE_NAME,JOB_DESC,OK_COUNT,FAIL_COUNT,BEGIN_DATE,END_DATE,BEGIN_TIME,END_TIME) values(?,?,?,?,?,?,?,?,?,0,0,?,?,?,?)";
		Object[] params = new Object[]{jobId, jobName, appName, cron, marServerName, JobState.NORMAL.getKey(), esbServiceId, esbServiceName, jobDesc, beginDate, endDate, beginTime, endTime};
		
		try {
			this.getSysDefaultQuery().execute(sql, params);
			return EasyResult.ok(null,"保存成功");
		} catch (SQLException ex) {
			logger.error("JobManager.addJobInfo出错，原因：" + ex.getMessage(),ex);
			return EasyResult.error(501,"保存失败，原因："+ex.getMessage());
		}
    }
    
    /**
	 * 修改调度任务信息
	 * @param jobId
	 * @param jobName
	 * @param cron
	 * @param marServerName
	 * @param esbServiceId
	 * @param esbServiceName
	 * @return
	 */
    public EasyResult modifyJobInfo(String jobId, String jobName, String appName, String cron, String esbServiceId, String esbServiceName, String jobDesc) {
    	
    	String sql = "update EASI_JOB_STEP set JOB_NAME=?,APP_NAME=?,CRON=?,SERVICE_ID=?,SERVICE_NAME=?,JOB_DESC=? where JOB_ID=?";
		Object[] params = new Object[]{jobName,appName,cron,esbServiceId,esbServiceName,jobDesc,jobId};
		try {
			this.getSysDefaultQuery().execute(sql, params);
			return EasyResult.ok(null,"更新成功");
		} catch (SQLException ex) {
			logger.error("JobManager.modifyJobInfo出错，原因：" + ex.getMessage(),ex);
			return EasyResult.error(501,"更新失败，原因："+ex.getMessage());
		}
    }
    
    /**
     * 更新调度任务状态
     */
    public void updateJobInfoState(AppJobModel job) {
    	String sql = "update easi_job_step set ok_count=?,fail_count=?,last_ok_time=?,last_fail_time=? where job_id=?";
		Object[] params = new Object[]{job.getOkCount(), job.getFailCount(), job.getLastOkTime(), job.getLastFailTime(), job.getJobId()};
		try {
			getSysDefaultQuery().execute(sql, params);
		} catch(Exception e) {
			logger.error("JobManager.updateJobInfoState出错，原因：" + e.getMessage(),e);
		}
    }
    
}
