package org.easitline.console.utils;

import java.util.HashMap;
import java.util.Map;

public class LoginSessionUtils {

	private static Map<String,String> loginInfo = new HashMap<String, String>();

	public static Map<String, String> getLoginInfo() {
		return loginInfo;
	}

	public static void addLoginInfo(String sessionId,String userName) {
		loginInfo.put(sessionId, userName);
	}
	
	public static void removeLoginInfo(String sessionId) {
		loginInfo.remove(sessionId);
	}
	
	public static void removeAll() {
		loginInfo.clear();
	}
	
	public static boolean isLoginState(String userName) {
		boolean flag = false;
		for (Map.Entry<String, String> entry : loginInfo.entrySet()) {
		    String value = entry.getValue();
		    if(userName.equals(value)) {
		    	flag = true;
		    	break;
		    }
		}
		return flag;
	}
	
	
}
