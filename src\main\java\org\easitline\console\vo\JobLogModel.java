package org.easitline.console.vo;

/**
 * 任务日志对象类
 * <AUTHOR>   2017-05-23
 *
 */
public class JobLogModel {

	private String dateKey;   //date_key
	private String jobId;     //调度ID
	private String dateId;    //调度日期，格式：YYYYMMDD
	private String jobState;  //调度状态，0:调度中  1：成功  -1：失败
	private String firstTime; //首次调度时间
	private String lastTime;  //最后执行时间
	private String execCount; //执行次数
	private String exceptionInfo;  //异常信息
	
	public String getDateKey() {
		return dateKey;
	}
	public void setDateKey(String dateKey) {
		this.dateKey = dateKey;
	}
	public String getJobId() {
		return jobId;
	}
	public void setJobId(String jobId) {
		this.jobId = jobId;
	}
	public String getDateId() {
		return dateId;
	}
	public void setDateId(String dateId) {
		this.dateId = dateId;
	}
	public String getJobState() {
		return jobState;
	}
	public void setJobState(String jobState) {
		this.jobState = jobState;
	}
	public String getFirstTime() {
		return firstTime;
	}
	public void setFirstTime(String firstTime) {
		this.firstTime = firstTime;
	}
	public String getLastTime() {
		return lastTime;
	}
	public void setLastTime(String lastTime) {
		this.lastTime = lastTime;
	}
	public String getExecCount() {
		return execCount;
	}
	public void setExecCount(String execCount) {
		this.execCount = execCount;
	}
	public String getExceptionInfo() {
		return exceptionInfo;
	}
	public void setExceptionInfo(String exceptionInfo) {
		this.exceptionInfo = exceptionInfo;
	}

}
