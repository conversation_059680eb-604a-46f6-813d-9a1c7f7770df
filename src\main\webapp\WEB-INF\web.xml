<?xml version="1.0" encoding="utf-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://xmlns.jcp.org/xml/ns/javaee"
	xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_3_1.xsd"
	metadata-complete="false" version="3.1">
	<display-name><PERSON> Console</display-name>
	<description><PERSON>sole</description>

	<servlet>
		<servlet-name>DruidStatView</servlet-name>
		<servlet-class>com.alibaba.druid.support.http.StatViewServlet</servlet-class>
	</servlet>
	<servlet-mapping>
		<servlet-name>DruidStatView</servlet-name>
		<url-pattern>/servlet/monitor/druid/*</url-pattern>
	</servlet-mapping>
	 <filter>  
     	 <filter-name>XssSqlFilter</filter-name>  
          <filter-class>org.easitline.common.core.web.websecurity.XssFilter</filter-class>  
          <init-param>  
	          <param-name>urlRules</param-name>
        	  <param-value>&lt;,&gt;,script,alert(,confirm,window,onclick,onfocus,prompt,unction</param-value> 
        </init-param>  
    </filter>  
    <filter-mapping>  
	     <filter-name>XssSqlFilter</filter-name>  
	     <url-pattern>/pages/*</url-pattern>  
	     <url-pattern>/servlet/*</url-pattern>  
	     <url-pattern>/login/*</url-pattern>  
	     <url-pattern>/index/*</url-pattern>  
	     <url-pattern>/index.jsp</url-pattern>  
	     <url-pattern>/login.jsp</url-pattern>  
	     <dispatcher>REQUEST</dispatcher>  
   </filter-mapping> 	
   
	<servlet>
	    <servlet-name>EasyDaoBaseServlet</servlet-name>
	    <servlet-class>org.easitline.common.core.web.EasyDaoBaseServlet</servlet-class>
	  </servlet>
  	<servlet-mapping>
	    <servlet-name>EasyDaoBaseServlet</servlet-name>
	    <url-pattern>/webcall/*</url-pattern>
	  </servlet-mapping>
  <listener>
    <listener-class>org.easitline.common.core.dao.DaoContextListener</listener-class>
  </listener>
   <session-config>
	    <session-timeout>30</session-timeout>
	    <cookie-config>
        	<name>consoleSession</name>
    	</cookie-config>
  </session-config>
	<welcome-file-list>
		<welcome-file>/index.jsp</welcome-file>
	</welcome-file-list>
</web-app>