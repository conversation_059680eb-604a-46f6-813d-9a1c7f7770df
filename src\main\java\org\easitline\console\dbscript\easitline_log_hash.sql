-- 创建日志文件哈希值表
CREATE TABLE IF NOT EXISTS EASI_LOG_HASH (
    ID INTEGER PRIMARY KEY AUTOINCREMENT,
    FILE_PATH VARCHAR(255) NOT NULL,
    HASH_VALUE VARCHAR(64) NOT NULL,
    LAST_MODIFIED VARCHAR(20) NOT NULL,
    CREATED_AT VARCHAR(20) DEFAULT (datetime('now','localtime')),
    UPDATED_AT VARCHAR(20) DEFAULT (datetime('now','localtime')),
    CONSTRAINT UK_FILE_PATH UNIQUE (FILE_PATH)
);;

-- 创建日志文件哈希值历史表
CREATE TABLE IF NOT EXISTS EASI_LOG_HASH_HIS (
    ID INTEGER PRIMARY KEY AUTOINCREMENT,
    FILE_PATH VARCHAR(255) NOT NULL,
    HASH_VALUE VARCHAR(64) NOT NULL,
    LAST_MODIFIED VARCHAR(20) NOT NULL,
    CREATED_AT VARCHAR(20) NOT NULL
);;