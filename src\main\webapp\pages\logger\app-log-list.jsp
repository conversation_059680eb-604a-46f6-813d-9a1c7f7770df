<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
	 <blockquote class="layui-elem-quote" onclick="getGlobalConfig()">
		<a href="javascript:void(0)" onclick="loginLogPage()">登录日志</a>
		<a style="margin-left: 20px;" href="javascript:void(0)" onclick="operateLogPage()">操作日志</a>
	</blockquote>
	 <fieldset class="layui-elem-field layui-field-title">
		<legend>应用服务日志</legend>
	  </fieldset>
		<form id="easyform-log-list">
			<input type="hidden" name="appId" value="${param.appId}"/>
			<div style="padding:0 15px">
				<table class="layui-table text-left" data-mars="server.appLog" data-container="#opData" data-template="opListTemp">
					<thead>
						<tr>
							<th>序号</th>
							<th>应用ID</th>
							<th>创建时间</th>
							<th>操作时间</th>
							<th>时间间隔</th>
							<th>备注</th>
							<th>操作</th>
						</tr>
					</thead>
					<tbody id="opData">
					</tbody>
				</table>
					<script id="opListTemp" type="text/x-jsrender">
					{{for data}}
							<tr>
								<td>{{:#index+1}}</td>
								<td>{{:APP_ID}}</td>
								<td>{{:CREATE_TIME}}</td>
								<td>{{:OPERATE_TIME}}</td>
								<td>{{call:fn='calculateInterval' CREATE_TIME}}</td>
								<td>{{:MSG}}</td>
								<td><a style="display:none;" href="javascript:void(0);" onclick="delAppLog('{{:LOG_ID}}')">删除</a></td>
							</tr>
					{{/for}}
					</script>
		    </div>
		</form>

<script>

	$(function(){
		$("#easyform-log-list").render();
	});

	function loginLogPage(){
		loadPage("logger/login-log-list.jsp",{});
	}

	function operateLogPage(){
		loadPage("logger/operate-log-list.jsp",{});
	}

	function delAppLog(logId) {
		ajax.remoteCall("${ctxPath}/servlet/serverInfo?action=delAppLog",{logId:logId},function(result) {
			if(result.state==1){
				layer.msg(result.msg,{icon: 1,time:800},function(){
					layer.closeAll();
					loadPage("logger/app-log-list.jsp");
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}

	// 计算时间间隔
	function calculateInterval(createTime) {
		if (!createTime) return '';

		try {
			var createDate = new Date(createTime.replace(/-/g, '/'));
			var currentDate = new Date();
			var diffMillis = currentDate.getTime() - createDate.getTime();

			if (diffMillis < 0) {
				return '未来时间';
			}

			var seconds = Math.floor(diffMillis / 1000);
			var minutes = Math.floor(seconds / 60);
			var hours = Math.floor(minutes / 60);
			var days = Math.floor(hours / 24);
			var months = Math.floor(days / 30);
			var years = Math.floor(months / 12);

			if (years > 0) {
				return years + '年前';
			} else if (months > 0) {
				return months + '个月前';
			} else if (days > 0) {
				return days + '天前';
			} else if (hours > 0) {
				return hours + '小时前';
			} else if (minutes > 0) {
				return minutes + '分钟前';
			} else {
				return seconds + '秒前';
			}
		} catch (e) {
			return '';
		}
	}
</script>

