package org.easitline.console.command;

import java.io.Closeable;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

public class FileLogger {
    private PrintWriter out;
    private DateFormat dateFormat;

    /**
     * @param file
     * @param encoding
     * @param append
     */
    public FileLogger(File file, String encoding, boolean append) {
        this.out = this.getWriter(file, encoding, append);
        this.dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss SSS");
    }

    /**
     * @param format
     * @param args
     */
    public void write(String format, Object ... args) {
        if(this.out != null) {
            this.out.write(format(format, args));
            this.out.write("\r\n");
            this.out.flush();
        }
    }

    /**
     * @param format
     * @param args
     */
    public void info(String format, Object ... args) {
        if(this.out != null) {
            this.out.write(this.dateFormat.format(new Date()));
            this.out.write(" [INFO] ");
            this.out.write(format(format, args));
            this.out.write("\r\n");
            this.out.flush();
        }
    }

    /**
     * @param format
     * @param args
     * @return String
     */
    public String format(String format, Object ... args) {
        if(args == null || args.length < 1) {
            return format;
        }

        int s = 0;
        int p = 0;
        int i = 0;
        StringBuilder buffer = new StringBuilder();

        while((p = format.indexOf("{}", s)) > -1) {
            if(p > s) {
                buffer.append(format.substring(s, p));
            }

            if(i < args.length) {
                buffer.append(args[i]);
                i++;
            }
            s = p + 2;
        }

        if(s < format.length()) {
            buffer.append(format.substring(s));
        }
        return buffer.toString();
    }

    /**
     * @param file
     * @param encoding
     * @param append
     * @return PrintWriter
     */
    private PrintWriter getWriter(File file, String encoding, boolean append) {
        OutputStream outputStream = null;

        try {
            outputStream = new FileOutputStream(file, append);
            return new PrintWriter(new OutputStreamWriter(outputStream, encoding));
        }
        catch(Exception e) {
            this.close(outputStream);
            e.printStackTrace();
        }
        return null;
    }

    /**
     * close writer
     */
    public void close() {
        if(this.out != null) {
            this.out.flush();
            this.close(this.out);
        }
    }

    /**
     * @param closeable
     */
    private void close(Closeable closeable) {
        if(closeable != null) {
            try {
                closeable.close();
            }
            catch(Exception e) {
            }
        }
    }
}

