<%@page pageEncoding="UTF-8"%>
<style type="text/css">
	.layui-form-label{width: 250px;}
	.layui-input-block{margin-left: 280px;}
	.layui-form-item .layui-input-inline{width: 400px;}
</style>
<div>
	<fieldset class="layui-elem-field layui-field-title">
		<legend>日志配置</legend>
	</fieldset>
	<form class="layui-form" method="post" id="easyform"  autocomplete="off"> 
	   <div class="layui-form-item">
	    <label class="layui-form-label">输出DEBUG日志</label>
	    <div class="layui-input-inline">
	      <input type="radio" name="G_DEBUG" value="true" title="是">
	      <input type="radio" name="G_DEBUG" value="false" title="否" checked="checked">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">日志生成数</label>
	    <div class="layui-input-inline">
	      <select name="G_LOG_BAK_INDEX" class="layui-select">
	        <option value="1">1</option>
	        <option value="3">3</option>
	        <option value="5">5</option>
	        <option value="8" selected="selected">8</option>
	        <option value="10">10</option>
	        <option value="15">15</option>
	        <option value="20">20</option>
	        <option value="999999">不限制</option>
	      </select>
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">日志生成大小</label>
	    <div class="layui-input-inline">
	      <select name="G_LOG_BAK_SIZE" class="layui-select">
	        <option value="10485760">10M</option>
	        <option value="52428800">50M</option>
	        <option value="104857600">100M</option>
	        <option value="157286400" selected="selected">150M</option>
	        <option value="209715200">200M</option>
	        <option value="314572800">300M</option>
	        <option value="524288000">500M</option>
	      </select>
	    </div>
	     <div class="layui-form-mid layui-word-aux">
	     	 <a href="javascript:reloadLog();">重载</a>
	     </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">日志级别</label>
	    <div class="layui-input-inline">
	      <select name="G_LOG_LEVEL" class="layui-select">
	        <option value="DEBUG"  selected="selected">DEBUG</option>
	        <option value="INFO">INFO</option>
	        <option value="WARN">WARN</option>
	        <option value="ERROR">ERROR</option>
	      </select>
		</div>
	  </div>
	  <div class="layui-form-item">
		    <label class="layui-form-label">日志滚动策略</label>
		    <div class="layui-input-block">
			      <input type="radio" name="rollStrategy" value="SIZE" title="按大小" checked="checked">
			      <input type="radio" name="rollStrategy" value="DATE" title="按日期">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">输出日志定位信息</label>
		    <div class="layui-input-block">
			      <input type="radio" name="G_LOG_LOCATION_ENABLED" value="true" title="是" checked="checked">
			      <input type="radio" name="G_LOG_LOCATION_ENABLED" value="false" title="否">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">自动备份日志</label>
		    <div class="layui-input-block">
			      <input type="radio" name="bakLogFlag" value="1" title="是">
			      <input type="radio" name="bakLogFlag" value="0" title="否" checked="checked">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">启用MQ采集日志</label>
		    <div class="layui-input-block">
			      <input type="radio" name="mqCollectLog" value="1" title="是">
			      <input type="radio" name="mqCollectLog" value="0" title="否" checked="checked">
		    </div>
		  </div>
		  <!-- <div class="layui-form-item">
		    <label class="layui-form-label">间隔备份时间(小时)</label>
		    <div class="layui-input-inline">
		      <select name="logBakHourInterval" class="layui-select">
		        <option value="1" selected="selected">1天1次(20)</option>
		        <option value="2">1天2次(13,20)</option>
		        <option value="3">1天3次(10,15,20)</option>
		        <option value="4">1天4次(10,13,16,21)</option>
		      </select>
		    </div>
	      </div> -->
		  <div class="layui-form-item">
		    <label class="layui-form-label">日志备份保留天数</label>
		    <div class="layui-input-inline">
		      <input type="number" lay-verify="required" value="1" placeholder="建议跟进磁盘空间剩余评估" name="logSaveDay" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">备份指定到目录</label>
		    <div class="layui-input-inline">
		      <input type="text" placeholder="不填写默认是easyserver/logbak" name="bakToDir" class="layui-input">
		    </div>
		     <div class="layui-form-mid layui-word-aux">
	     	 <a href="javascript:bakLogPage();">查看</a>
	     </div>
		  </div>
		  <div class="layui-form-item layui-hide">
		    <label class="layui-form-label">Tomcat日志备份</label>
		    <div class="layui-input-block">
		      <input type="text" value="catalina.out." placeholder="日志开头前缀,不填写表示不备份,如catalina.out" name="tomcatLogBakPrefix" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item layui-hide">
		    <label class="layui-form-label">应用日志备份</label>
		    <div class="layui-input-block">
		      <input type="text" placeholder="日志开头前缀,不填写表示不备份,如easitline,mars逗号隔开" name="appLogBakPrefix" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <div class="layui-input-block">
		      <button type="button" lay-submit lay-filter="submitBtn" class="layui-btn layui-btn-sm">保存</button>&nbsp;&nbsp;
		    </div>
		  </div>
	</form>
</div>

<script>
	$(function(){
		layui.use('form', function(){
			getConf();
	         var layuiform = layui.form;
	         layuiform.render('select');
	         layuiform.render();
			 layuiform.on('submit(submitBtn)', function(data){
				 doExcute(data.field);
			 });
	     });
		
	});

	function getConf(){
		ajax.remoteCall("${ctxPath}/servlet/serverInfo?action=getAllConfig",{}, function(result) { 
			var data=result.data;
			if(data){
				fillRecord(data);
				layui.use('form', function(){
			         var layuiform = layui.form;
			         layuiform.render('select');
			         layuiform.render('radio');
			         layuiform.render();
			     });
			}
		});
	}

	function doExcute() {
		var data = form.getJSONObject("easyform"); 
		ajax.remoteCall("${ctxPath}/servlet/serverInfo?action=saveLoginConfig",data,function(result) { 	
			if(result.state==1){
				layer.msg(result.msg,{icon: 1,time:800},function(){
					layer.closeAll();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
	
	function bakLogPage(){
		loadPage("logger/bak-logger.jsp",{});	
	}
	
	
	function reloadLog(){
		layer.confirm('是否确认重载，如不生效请重启服务',function(index){
			layer.close(index);
			ajax.remoteCall("${ctxPath}/servlet/serverInfo?action=reloadLog",{},function(result) { 	
				if(result.state==1){
					layer.msg(result.msg,{icon: 1,time:800},function(){
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		});
	}
	
</script>