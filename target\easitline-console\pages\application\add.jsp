<%@page pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!-- 上传页面 -->
<div id="addPage">
	<blockquote class="layui-elem-quote">
		部署Mars平台应用，请上传符合Mars平台规范的应用，当前版本支持WAR和JAR，选择完成后，点击下一步完成应用的配置。<br>
		<small style="color: red;">如上传失败：请查看网络请求,优化NG配置等</small>
		<a href="javascript:;" onclick="developDoc();" style="margin-left: 20px;color:red;">模块开发规范</a>
	</blockquote>

	<fieldset class="layui-elem-field layui-field-title">
		<legend>应用部署</legend>
	</fieldset>

	<form class="layui-form" id="easyform">
	   <input type="hidden" name="appId" value="${param.appId}"/>
	 
	<c:if test="${param.type=='local'}">
	  <div class="layui-form-item">
	    <label class="layui-form-label">上传应用</label>
	    <div class="layui-input-block">
	    	<!-- <input type="file" id="appFile"  name = "appFile" value=""	class="input-xlarge"> -->
	       <button type="button" class="layui-btn layui-btn-small" name="appFile" id="upload-btn"><i class="layui-icon">&#xe67c;</i>上传应用</button>
	    </div>
	  </div>
	</c:if>
	<c:if test="${param.type=='server'}">
		  <div class="layui-form-item">
		     <label class="layui-form-label">选择应用</label>
		     <div class="layui-input-block">
		      <select name="appPath" data-mars="app.warDeployApp" lay-filter="warSelect" lay-search>
	       		<option value=""></option>
	       	  </select>
		     </div>
		   </div>
		  <div class="layui-form-item">
		     <label class="layui-form-label"></label>
		     <div class="layui-input-block">
				<button type="button" class="layui-btn layui-btn-small" id="manual-upload-btn">确定部署</button>
		     </div>
		   </div>
	</c:if>
  </form>
</div>

<!-- 应用部署页面 -->
<div id="deployPage">
	<blockquote class="layui-elem-quote">
		这里进行应用数据源和系统数据源对应关系的配置，如果应用有配置数据库脚本，则可以选择执行数据库初始化脚本，应用数据库脚本的执行通过应用的缺省数据源来完成！
	</blockquote>

	<fieldset class="layui-elem-field layui-field-title">
		<legend>应用管理</legend>
	</fieldset>
	
	<div id="renderForm">
	</div>
	
	<script id="formTemp" type="text/x-jsrender">
	<form class="layui-form" id="deployform">
		  <div class="layui-form-item">
		    <label class="layui-form-label">应用ID</label>
		    <div class="layui-input-block" style="width:360px;">
		      <input type="text" name="appId" value="{{:appInfo.appId}}" readonly class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">应用名称</label>
		    <div class="layui-input-block" style="width:360px;">
		      <input type="text" name="appName" value="{{:appInfo.appName}}" readonly class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">版本号</label>
		    <div class="layui-input-block" style="width:360px;">
		      <input type="text" name="appVersion" value="{{:appInfo.appVersion}}" readonly class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">应用文件名称</label>
		    <div class="layui-input-block" style="width:360px;">
		      <input type="text" name="appWarName" value="{{:appInfo.warName}}" readonly class="layui-input">
		    </div>
		  </div>
		{{if appInfo.fileType=='jar'}}
		  <div class="layui-form-item">
		    <label class="layui-form-label">部署目录</label>
		    <div class="layui-input-block" style="width:360px;">
		      <input type="text" name="depoyDir" value="/home/<USER>" style="border:1px solid red;" class="layui-input">
		    </div>
		  </div>
		{{/if}}
		  <div class="layui-form-item">
		    <label class="layui-form-label">应用更新描述</label>
		    <div class="layui-input-block" style="width:600px;">
		      <textarea style="hegiht:380px" placeholder="请输入版本更新内容" name="appVersionDesc" class="layui-textarea">{{:appInfo.appVersionDesc}}</textarea>
		    </div>
		  </div>

		{{if}}
		  	<div>
				<fieldset class="layui-elem-field layui-field-title">
					<legend>配置应用数据源</legend>
				</fieldset>
				{{for appInfo.dsList}}
					<div class="layui-form-item">
			    		<label class="layui-form-label">
							{{if dsName=="_default_ds"}}default-ds：{{else}}{{:dsName}}{{/if}}
						</label>
			    		<div class="layui-input-inline" style="margin-left:20px">
			    			<select id="{{:dsName}}" name="{{:dsName}}" class="layui-select">
							</select>
			    		</div>
			    		<div class="layui-form-mid layui-word-aux">{{:dsDesc}}</div>
		  			</div>
				{{/for}}

				<div class="layui-form-item">
				    <label class="layui-form-label">创建模块所需库表</label>
				    <div class="layui-input-inline" style="width:20px;">
				    	<input type="checkbox" tabIndex="1" id="dbinit" name ="dbinit" lay-skin="primary"/>
				    </div>
					<div class="layui-form-mid layui-word-aux">勾选后，部署应用的同时通过缺省数据源创建应用的库表以及进行初始化数据的操作。</div>
			  </div>
			</div>
		  {{/if}}
		  
		  <div class="layui-form-item">
		    <div class="layui-input-block">
		      <button type="button" class="layui-btn layui-btn-small layui-btn-normal" onclick="deploy()">部署应用</button>
		    </div>
		  </div>
	</form>
	</script>
</div>


<div id="developDoc" style="display: none;">
		<p style="color: red;">1、部署war必须包含下面文件就行，技术栈不受限，META-INF/appinfo.xml</p>
		<p>2、应用参数配置，META-INF/appconfig.xml</p>
		<hr>
		<p>常用工具类</p>
		<p>获取全局路径：org.easitline.common.core.Globals</p>
		<p>获取设置的全局变量：org.easitline.common.core.context.ServerContext</p>
		<p>获取部署的应用对象：org.easitline.common.core.context.AppContext</p>
		<p>Redis工具：org.easitline.common.core.log.RedisImpl</p>
		<p>日志工具：org.easitline.common.core.log.LogEngine</p>
		<hr>
		<p>数据源相关</p>
		<p>数据源连接池：org.easitline.common.core.EasyPool</p>
		<p>JDBC增删改查类：org.easitline.common.core.EasyQuery</p>
		<p>SQL拼接工具类：org.easitline.common.db.EasySQL</p>
		<hr>
		<p>web相关</p>
		<p>请求入口：org.easitline.common.core.web.EasyBaseServlet</p>
		<p>查询统一入口：org.easitline.common.core.dao.DaoContext</p>
		<p>页面渲染：org.easitline.common.core.web.render.Render</p>
		<p>页面JSON数据：org.easitline.common.utils.kit.JsonKit</p>
		
</div>

<script>
   
    var warPath = '';
	$('#easyform').render({success:function(){
		selectLoad();
		layui.use('form', function(){
		    var form = layui.form;
			form.on('select(warSelect)', function(data){
				warPath = data.value;
			});
		});
		
	}});
	
	$("#deployPage").hide();
	
	//上传文件
	layui.use('upload', function(){
	  var upload = layui.upload;
	  var appId = "${param.appId}"; 
	  //执行实例
	  var uploadInst = upload.render({
	    elem: '#upload-btn' //绑定元素
	    ,url: '${ctxPath}/servlet/application?action=upload' //上传接口
	    ,number: 1 //可上传文件数量
	    ,accept: 'file'//所有文件
	    ,field: 'appFile'//设定文件域的字段名
    	,exts:'war|jar'
	    ,size:1024*150
	    ,data: {appId:appId,action:'upload'}
	    ,done: function(res, index, upload){
	      //上传完毕回调
	      if(res&&res.state==1){
	    	  $("#addPage").hide();
	    	  $("#deployPage").show();
	    	  var appInfo = res.appInfo;
	    	  var dslist = res.dslist;
	    	  var dslistHtml = " <option value=''>--请选择--</option>";
	    	  for(var i=0;i<dslist.length;i++){
	    		  dslistHtml+="<option value='"+dslist[i].dataSourceName+"'>"+dslist[i].dataSourceName+"</option>";
	    	  }
	          var jsRenderTpl = $.templates("#formTemp");
	          var html = jsRenderTpl(res);
	    	  $("#renderForm").html(html);
	    	  
	    	  $("select").each(function(){
					var thisName = $(this)[0].id;
					$(this).html(dslistHtml);
			  });
	    	  
	    	  selectLoad();
			}else{
				layer.alert(res.msg,{icon: 5});
			}
	    }
	    ,error: function(res, index, upload){
	      //请求异常回调
	    	layer.alert("上传文件请求异常！",{icon: 5});
	    }
	  });
	});
	
	$('#manual-upload-btn').on('click',function(){
		ajax.remoteCall("${ctxPath}/servlet/application?action=selectWar",{path:warPath},function(res) { 	
			if(res&&res.state==1){
	    	  $("#addPage").hide();
	    	  $("#deployPage").show();
	    	  var appInfo = res.appInfo;
	    	  var dslist = res.dslist;
	    	  var dslistHtml = " <option value=''>--请选择--</option>";
	    	  for(var i=0;i<dslist.length;i++){
	    		  dslistHtml+="<option value='"+dslist[i].dataSourceName+"'>"+dslist[i].dataSourceName+"</option>";
	    	  }
	          var jsRenderTpl = $.templates("#formTemp");
	          var html = jsRenderTpl(res);
	    	  $("#renderForm").html(html);
	    	  
	    	  $("select").each(function(){
					var thisName = $(this)[0].id;
					$(this).html(dslistHtml);
			  });
	    	  selectLoad();
			}else{
				layer.alert(res.msg,{icon: 5});
			}
		}
	);
  });
	
	//渲染select
	function selectLoad(){
		//开启表单渲染
		layui.use('form', function(){
		    var layuiForm = layui.form;
		    layuiForm.render('select'); //刷新select选择框渲染
		    layuiForm.render('checkbox');
		});
	}
	
	//部署文件
	function deploy() {
		if(document.all.dbinit.checked){
			if($("#_default_ds").val() == ""){
				layer.alert("创建模块所需脚本必须选择缺省数据源!");
				return;
			}
		}
		var data = form.getJSONObject("deployform");
		ajax.remoteCall("${ctxPath}/servlet/application?action=deploy",data,function(result) { 	
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1,time:800},function(){
						layer.closeAll();
						loadPage("application/list.jsp");
					});
				}else{
					layer.alert(result.msg);
				}
			}
		);
	}
	
	function developDoc(){
		layer.open({title:'开发说明',content:$('#developDoc').html(),offset:'auto',area:['700px','400px'],shade:0});
	}
</script>