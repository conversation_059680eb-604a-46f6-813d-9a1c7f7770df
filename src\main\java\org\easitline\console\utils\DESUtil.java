package org.easitline.console.utils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.utils.crypt.BASE64Util;
import org.easitline.console.service.ConfigCryptorService;


public class DESUtil {
	
  /**
   * 保存密钥
   */
  private SecretKey key = null;

  /**
   * 加密对象
   */
  private Cipher  encipher  = null;
  /**
   * 加密对象
   */
  private Cipher  decipher  = null;

  /**
   * 加密密钥
   */
  private static final String CRYPT_KEY  = "rO0ABXNyAB5jb20uc3VuLmNyeXB0by5wcm92aWRlci5ERVNLZXlrNJw12hVomAIAAVsAA2tleXQAAltCeHB1cgACW0Ks8xf4BghU4AIAAHhwAAAACCyDuaGUv2jy";


  public DESUtil(){
     init(null);
  }
  public DESUtil(String base64Key){
     init(base64Key);
  }

  private static class Holder{
	  private static DESUtil desUtil=new DESUtil();
  }

  /**
   * 初始化加密引擎
   * @param base64Key  BASE64编码的密钥（可为null或空，表示使用默认密钥）
   */
  private void init(String base64Key){
    try {
      String keyStr = StringUtils.isBlank(base64Key) ? CRYPT_KEY : base64Key;
      ObjectInputStream objIn = new ObjectInputStream(new ByteArrayInputStream(BASE64Util.decode(keyStr)));
      key = (SecretKey) objIn.readObject();
      objIn.close();
      objIn = null;
    }
    catch (Exception ex) {
    	CoreLogger.getLogger().error(ex.getMessage(),ex);
    }
    try {
	     encipher = Cipher.getInstance("DES");
	     encipher.init(Cipher.ENCRYPT_MODE,key);
	     decipher = Cipher.getInstance("DES");
	     decipher.init(Cipher.DECRYPT_MODE,key);
    }
    catch (Exception ex) {
     	CoreLogger.getLogger().error("生成加密引擎失败："+ex.getMessage(),ex);
    }
  }

  /**
   * 用返回Dec加密的Base64编码
   * @param data byte[]
   * @return String
   */
  public String encrypt(byte[] data){
    try {
      return  BASE64Util.encode(encipher.doFinal(data));
    }
    catch (Exception ex) {
       CoreLogger.getLogger().error("encrypt error:"+ex.getMessage(),ex);
    }
    return null;
  }
  public String encryptStr(String data){
	  return encrypt(data.getBytes());
  }
  
  
  /**
   * 用返回Dec解密后内容
   * @param data String
   * @return String
   */
  public String decryptStr(String data){
	   return new String(decrypt(data));
  }
  
  public  byte[] decrypt(String data){
	  if(StringUtils.isBlank(data))return null;
	  try {
		  byte[] obj=this.decipher.doFinal(BASE64Util.decode(data));
		  return obj;
	  }
	  catch (Exception ex) {
		 CoreLogger.getLogger().error(ex.getMessage(),ex);
	  }
	  return null;
  }
  
  public static DESUtil getInstance(){
	  return Holder.desUtil;
  }
  
  /**
   * 获取DESUtil实例，支持自定义密钥
   * @param base64Key BASE64编码的密钥（可为null或空，表示使用默认密钥）
   * @return DESUtil实例
   */
  public static DESUtil getInstance(String base64Key){
      if (StringUtils.isBlank(base64Key)) {
          return getInstance();
      } else {
          return new DESUtil(base64Key);
      }
  }
  
  public static DESUtil getDsInstance(){
	  String sk = ConfigCryptorService.getLocalSk();
	  if(StringUtils.isNotBlank(sk)) {
		  return getInstance(sk);
	  }
	  return getInstance(CRYPT_KEY);
  }
  
  public static void main(String[] args) throws Exception {
      // 1. 生成DES密钥
      KeyGenerator keyGen = KeyGenerator.getInstance("DES");
      SecretKey secretKey = keyGen.generateKey();

      // 2. 序列化SecretKey对象
      ByteArrayOutputStream bos = new ByteArrayOutputStream();
      ObjectOutputStream oos = new ObjectOutputStream(bos);
      oos.writeObject(secretKey);
      oos.close();
      byte[] keyBytes = bos.toByteArray();

      // 3. BASE64编码
      String base64Key = Base64.getEncoder().encodeToString(keyBytes);

      // 4. 输出密钥字符串
      System.out.println("你的DES密钥（可直接用于DESUtil）:");
      System.out.println(base64Key);

  }
  
}
