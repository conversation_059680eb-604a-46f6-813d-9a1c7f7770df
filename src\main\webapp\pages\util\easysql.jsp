<%@ page contentType="text/html; charset=utf-8" %>
<%@page import="org.easitline.common.db.*"%>
<%@page import="org.easitline.common.core.*"%> 
<%@page import="org.easitline.common.core.context.*"%> 
<%@ page import  = "java.sql.*" %>
<%@ page import  = "java.util.*" %>
<%@ page import  = "javax.naming.*"%>
<%
/**
 * 作者：汤湛成
 * 日期：2016-8-18 最后更新
 * 描述：系统远程数据维护工作，运行于tomcat服务器
 */
class DB{  
    private Set dataType;
    public DB(){
        dataType = new HashSet();
    }

    private String getColumnValueFormat(String value, int type) {
      boolean isString = true;
      if(type == Types.BIGINT    || type == Types.BOOLEAN || type == Types.DECIMAL
        ||type == Types.DOUBLE   || type == Types.FLOAT   || type == Types.INTEGER
        ||type == Types.NUMERIC  || type == Types.REAL
        ||type == Types.SMALLINT || type == Types.TINYINT ){
         isString = false;
      }
      if(type == Types.NULL) return "NULL";
      if (isString) {
        return "'\" + " + value + " + \"'\" + ";
      }
      return "\" + " + value +" + ";
    }
    //生成表的
    public  String  createInsertSQL(String datasource,String tableName) {
      Connection conn = null;
      ResultSet rs_column = null;
      int locate = 1;
      String columnSet = " \"insert into " + tableName + " ("; //生成插入语句
      String valueSet  = " values(" ; //生成插入语句
      try {
     
      conn = this.getConnection(datasource);
      DatabaseMetaData mdm= conn.getMetaData();
      String[] str_tableType={"TABLE"};
      rs_column=mdm.getColumns(null,null,tableName,"%");
      while(rs_column.next()){
           if(locate == 1){
               columnSet += rs_column.getString("COLUMN_NAME");
               valueSet  += getColumnValueFormat(rs_column.getString("COLUMN_NAME"),rs_column.getInt("DATA_TYPE"));
           }else{
               columnSet += ","+ rs_column.getString("COLUMN_NAME");
               valueSet += "\","+getColumnValueFormat(rs_column.getString("COLUMN_NAME"),rs_column.getInt("DATA_TYPE"));
           }
           locate++;
      }
      columnSet+=")";
      valueSet+="\")\"";
      return columnSet+valueSet;
   }catch(Exception e){
      return e.getMessage();
   }finally{
      try {
        conn.close();
      }
      catch (Exception ex) {

      }
   }
 }

   public ArrayList getColumnInfo(String datasource,String tableName){
      Connection conn = null;
      ResultSet rs_column = null;
      ArrayList list = new ArrayList();

      try {
      conn = org.easitline.common.core.context.ServerContext.getConnection("derby");
      DatabaseMetaData mdm= conn.getMetaData();
      rs_column=mdm.getColumns(null,null,tableName,"%");
      String[] str = null;
      while(rs_column.next()){
           str = new String[5];
           str[0] = rs_column.getString("COLUMN_NAME");
           str[1] = rs_column.getString("TYPE_NAME");
           str[2] = rs_column.getString("COLUMN_SIZE");
           str[3] = rs_column.getString("IS_NULLABLE");
           str[4] = rs_column.getString("COLUMN_DEF");
           if(str[4]==null) str[4] = "";
           list.add(str);
      }
      return list;
   }catch(Exception e){
      return list;
   }finally{
      try {
        conn.close();
      }
      catch (Exception ex) {

      }
   }
   }
   
   public  Connection getConnection(String dsname) throws Exception{

			String url    = "jdbc:derby:" + Globals.DB_DIR + ";create=true";
			return java.sql.DriverManager.getConnection(url, "", "");
		
   }

   public  ArrayList getAllTables(String datasource){
      ArrayList table = new ArrayList();
      Connection conn = null;
      try {
      
      conn = this.getConnection(datasource);
      DatabaseMetaData mdm= conn.getMetaData();
      String[] str_tableType={"TABLE"};
      ResultSet rs_tableName=mdm.getTables(null,null,"%",str_tableType);
          while(rs_tableName.next()){
            //if(tableName.equals("")) this.tableName = rs_tableName.getString("TABLE_NAME");
            table.add(rs_tableName.getString("TABLE_NAME"));
     }
   }catch(Exception e){
   }finally{
      try {
        conn.close();
      }
      catch (Exception ex) {

      }
   }
   return table;
}
}
%>

<%

  String history = request.getParameter("history");
  if(history==null) history = "";

  String sql = request.getParameter("sql");
  if(sql==null) sql = "";

  String datasource  = request.getParameter("datasource");
  if(datasource == null) datasource = "";
  datasource = datasource.trim();

  ArrayList table = new DB().getAllTables(datasource);

  Properties pro = (Properties)session.getAttribute("sql");

  String oper = request.getParameter("operator");
  if(oper == null) oper = "";
  if(sql==null) sql = "";
  String tableName = request.getParameter("tableName");
  String runSql = sql.trim();
  String info = "";
  List<EasyRow> list = new ArrayList();
  if(oper.equals("executeSQL")){
   if(!runSql.equals("")){
      try {
    	  
    	  list = ServerContext.getConsoleQuery().queryForList(runSql,null);
      }catch (Exception ex) {
         info = ex.getMessage();
      }
    }
  }else if(oper.equals("createInsert")){
      info = new DB().createInsertSQL(datasource,tableName);
  }else if(oper.equals("createUpdate")){

  }else if(oper.equals("createSelect")){

  }
  EasyRow row;



%>



<html>

<head>
<meta http-equiv="Content-Language" content="zh-cn">
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">

<title>远程数据库维护终端</title>

<style>
table{
	font-size: 11pt;
	line-height: 18pt;

}
.coolscrollbar{
font:13px;
}

</style>

</head>



<script language = "javascript">


    function execute(){
   
      var history_data  = document.all.history.value;
   
      var str = document.selection.createRange().text;

      if(str == ""){
       alert("没有要执行的sql语句");
        document.all.history.value = history_data;
       return false;
      }

      if(document.all.history.value.indexOf(str)==-1){
       alert("没有要执行的sql语句");
       document.all.history.value = history_data;
       return false;
      }
      document.all.sql.value = str;
      document.all.operator.value = "executeSQL";
      document.all.form1.submit();
      return false;
    }

    function quickpost()
    {
      if((event.ctrlKey && window.event.keyCode == 68))
	{
          execute();
	  return true;
	}
    }
    function createInsert(){
       if(document.all.tableName.value == ""){
         alert("请从表选择框中选择要生成INSERT语句的表名！");
         return;
       }
       document.all.operator.value = "createInsert";
       document.all.form1.submit();
    }

    function seeTableInfo(){
       if(document.all.tableName.value == ""){
         alert("请从表选择框中选择表名！");
         return;
       }
       document.all.operator.value = "seeTableInfo";
       document.all.form1.submit();
   }

</script>
<body  onkeyup="quickpost()" topmargin="2" leftmargin="2" rightmargin="0" bottommargin="0" marginwidth="0" marginheight="0">

<table border="0" width="100%" id="table1" cellspacing="0" cellpadding="0"  bordercolordark="#FFFFFF" bordercolorlight="#C0C0C0" height="344">
  <form method = "post" name = "form1">
  <input type="hidden" name="operator" value = ""/>
<tr>
		<td width="94%" align="center" colspan="2">
		<table border="1" width="100%" id="table3" cellspacing="0" cellpadding="0"  bordercolordark="#FFFFFF" bordercolorlight="#C0C0C0" >
			<tr>
				<td width="14%">DataSource Name：</td>
				<td width="16%">

				<select name = "datasource" onchange="this.form.submit()">
				<option value = "derby-ds">Default DataSource</option>
        
    	 <select>

         		<td width="*">&nbsp;
                           <input type="button"   style="width:130"  value="Execute Query"        onclick = "execute()" >
                           <input type = "button" style="width:130"  value = "Create Insert SQL"  onclick = "createInsert()">
                           <input type = "button" style="width:130"  value = "create Update SQL"  onclick = "createUpdate()">
                           <input type = "button" style="width:130"  value = "Create Select SQL"  onclick = "createSelect()">
                           <input type = "button" style="width:130"  value = "See Table Info"     onclick = "seeTableInfo()">
                         </td>

			</tr>
		</table>
		</td>
	</tr>
	<tr>
		<td width="100%" align="center" height="227">
		<textarea rows="25" name="history"   class="coolscrollbar" style = "width:100%"><%=history%></textarea>

                </td>
                <td><select size = "25" name ="tableName" style = "width:180">
                  <%for (int i = 0; i < table.size(); i++) {
                   %>
                   <option value="<%=table.get(i)%>" <%if(table.get(i).equals(tableName)) out.print("selected");%>><%=table.get(i)%></option>
                   <%
                    }%>

                </select>

                </td>

		<input type = "hidden"  name="sql" value ="">
	</tr>
	</form>
	</table>

        <div><br><pre><%=info%></pre><div>

    <table border="1" id="table2" cellspacing="0" cellpadding="0" bordercolordark="#FFFFFF" bordercolorlight="#C0C0C0" >
      <%if(list.size()>0){
        row  = list.get(0);
        int colLen = row.getColumnCount();%>

         <tr>
        <%
        for (int i = 0; i < colLen; i++) {

        %>

             <td height="22" bgcolor="#cccccc" align = "center"><%=row.getColumnName(i+1)%>&nbsp;</td>

       <%}%>

        </tr>
         <%}%>
	<%for (int i = 0; i < list.size(); i++) {
        row  = list.get(i);
        int colLen = row.getColumnCount();
        %> <tr><%
        for (int j = 0; j  < colLen; j ++) {
        %>

		<td height="22" > <%=row.getColumnValue(j+1)%>&nbsp;</td>
	 <%}%>
        </tr>
        <%}%>

   </table>
   <%if(oper.equals("seeTableInfo")){
     ArrayList col = new DB().getColumnInfo(datasource,tableName);%>
  <table border="1" id="table2" cellspacing="0" cellpadding="0" bordercolordark="#FFFFFF" bordercolorlight="#C0C0C0" >

      <tr>
        <td height="22"  width="50" bgcolor="#cccccc" align = "center">序号</td>
        <td height="22"  width="150" bgcolor="#cccccc" align = "center">列名</td>
        <td height="22"  width="120" bgcolor="#cccccc" align = "center">类型</td>
        <td height="22"  width="120" bgcolor="#cccccc" align = "center">类型长度</td>
        <td height="22"  width="150" bgcolor="#cccccc" align = "center">是否允许为空</td>
        <td height="22"  width="120" bgcolor="#cccccc" align = "center">缺省值</td>
      </tr>
      <%String[] str;
       for (int i = 0; i < col.size(); i++) {
         str = (String[])col.get(i);
      %>
       <tr>
        <td height="22"  width="50"   align = "center"><%=i+1%></td>
        <td height="22"  ><%=str[0]%></td>
        <td height="22"  ><%=str[1]%></td>
        <td height="22"  align = "center"><%=str[2]%></td>
        <td height="22"  align = "center"><%=str[3]%></td>
        <td height="22"  align = "center"><%=str[4]%>&nbsp;</td>
       </tr>
      <%
       }
     %>
  </table>
   <%}%>

</body>

</html>
