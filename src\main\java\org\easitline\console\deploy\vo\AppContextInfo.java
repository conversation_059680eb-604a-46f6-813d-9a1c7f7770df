package org.easitline.console.deploy.vo;

/**
 * 上下文
 * <AUTHOR>
 * @date   2016-09-18
 */
public class AppContextInfo {

	private String appName;      //应用名称
	private String contextPath;  //上下文
	private Boolean state;        //状态
	private Integer sessionCount;      //session数
	
	private int sessionTimeout;   //版本号
	private String warName;   //war包名称
	private String stateName;  //部署状态
	private String deployTime;//部署时间
	
	private String startUrl;   //启动url
	private String stopUrl;    //停止url
	private String reloadUrl;  //重启url
	private String undeployUrl;  //卸载url
	private String updateUrl; //更新url
	
	private String appId;       //应用标识
	
	public String getContextPath() {
		return contextPath;
	}
	public void setContextPath(String contextPath) {
		this.contextPath = contextPath;
	}
	public Boolean getState() {
		return state;
	}
	public void setState(Boolean state) {
		this.state = state;
	}
	public Integer getSessionCount() {
		return sessionCount;
	}
	public void setSessionCount(Integer sessionCount) {
		this.sessionCount = sessionCount;
	}
	
	
	public int getSessionTimeout() {
		return sessionTimeout;
	}
	public void setSessionTimeout(int sessionTimeout) {
		this.sessionTimeout = sessionTimeout;
	}
	public String getWarName() {
		return warName;
	}
	public void setWarName(String warName) {
		this.warName = warName;
	}
	
	public String getStateName() {
		return stateName;
	}
	public void setStateName(String stateName) {
		this.stateName = stateName;
	}
	public String getAppName() {
		return appName;
	}
	public void setAppName(String appName) {
		this.appName = appName;
	}
	public String getStartUrl() {
		return startUrl;
	}
	public void setStartUrl(String startUrl) {
		this.startUrl = startUrl;
	}
	public String getStopUrl() {
		return stopUrl;
	}
	public void setStopUrl(String stopUrl) {
		this.stopUrl = stopUrl;
	}
	public String getReloadUrl() {
		return reloadUrl;
	}
	public void setReloadUrl(String reloadUrl) {
		this.reloadUrl = reloadUrl;
	}
	public String getUndeployUrl() {
		return undeployUrl;
	}
	public void setUndeployUrl(String undeployUrl) {
		this.undeployUrl = undeployUrl;
	}
	public String getAppId() {
		return appId;
	}
	public void setAppId(String appId) {
		this.appId = appId;
	}
	public String getUpdateUrl() {
		return updateUrl;
	}
	public void setUpdateUrl(String updateUrl) {
		this.updateUrl = updateUrl;
	}
	public String getDeployTime() {
		return deployTime;
	}
	public void setDeployTime(String deployTime) {
		this.deployTime = deployTime;
	}
	
}
