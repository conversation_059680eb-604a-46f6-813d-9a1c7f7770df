package org.easitline.console.servlet;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpSession;

import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.RandomKit.SMSAuthCodeType;
import org.apache.commons.lang3.StringUtils;
import org.easitline.console.base.ConsoleBaseServlet;

import com.alibaba.fastjson.JSONObject;


@WebServlet("/servlet/node/*")
public class NodeServlet extends ConsoleBaseServlet {

	private static final long serialVersionUID = 1L;
	
	public JSONObject actionForGetKey() {
		return EasyResult.ok(RandomKit.smsAuthCode(32,SMSAuthCodeType.CharAndNumbers));
	}
	
	
	public JSONObject actionForAdd() {
		EasyRecord record = new EasyRecord("EASI_NODE","NODE_ID");
		try {
			record.setColumns(getJSONObject("node"));
			record.set("CREATE_TIME", EasyDate.getCurrentDateString());
			record.setPrimaryValues(RandomKit.uniqueStr());
			this.getConsoleQuery().save(record);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public JSONObject actionForDel() {
		String nodeId = getJsonPara("nodeId");
		try {
			this.getConsoleQuery().executeUpdate("DELETE FROM EASI_NODE WHERE NODE_ID = ?", nodeId);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public JSONObject actionForUpdate() {
		EasyRecord record = new EasyRecord("EASI_NODE","NODE_ID");
		try {
			record.setColumns(getJSONObject("node"));
			this.getConsoleQuery().update(record);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForQuerySmsSwitch() {
		JSONObject object = new JSONObject();
//		String mobile = PropKit.get("mobile","");
//		String smsSwitch = PropKit.get("smsSwitch","0");// 0 关闭 1开启
		String mobile = appContext.getProperty("updateConfMobile", "");
		if(StringUtils.isNotBlank(mobile)) {
			object.put("smsSwitch","1");
		}else {
			String smsSwitch= appContext.getProperty("updateConfSmsSwitch", "0");
			object.put("smsSwitch",smsSwitch);
		}
		object.put("mobile",mobile );
		return EasyResult.ok(object);
	}
	
	public EasyResult actionForCheckSms() {
		JSONObject params = getJSONObject();
		Object smsCodeObj = this.getRequest().getSession().getAttribute("smsCode");
		if(smsCodeObj!=null) {
			String smsCode = smsCodeObj.toString();
			String _smsCode = params.getString("smsCode");
			if(StringUtils.isBlank(_smsCode)) {
				return EasyResult.fail("验证码不能为空.");
			}
			if(smsCode.equals(_smsCode)) {
				this.getRequest().getSession().removeAttribute("smsCode");
				return EasyResult.ok();
			}else {
				return EasyResult.fail("验证码错误");
			}
		}else {
			return EasyResult.fail("验证码已失效,请重新获取验证码。");
		}
	}
	
	public EasyResult actionForSendSms() {
		String smsCode=RandomKit.smsAuthCode(6, SMSAuthCodeType.Numbers);
		JSONObject params = new JSONObject();
//		String mobile = PropKit.get("mobile");
		String mobile = appContext.getProperty("updateConfMobile", "");;
		params.put("mobile",mobile );
		params.put("smsId", RandomKit.uniqueStr(1, false));
		params.put("subcode", "");
		params.put("smsCode", smsCode);
		params.put("userId", this.getRequest().getSession().getAttribute("MARS_CONSOLE_USER"));
		String message = "您正在验证操作,验证码"+smsCode+",切勿将验证码泄露于他人,本条验证码有效期10分钟。";
		params.put("content", message);
		try {
			this.info(message, null);
			IService service = ServiceContext.getService("YC-SMS-SEND-SMS-SERVICE");
			if(service==null) {
				this.error("YC-SMS-SEND-SMS-SERVICE not exsit.", null);
				return EasyResult.fail("短信接口不存在.");
			}else {
				JSONObject responeJson =service.invoke(params);
				this.info("sms:"+responeJson,null);
				
				HttpSession session = this.getRequest().getSession();
				session.setMaxInactiveInterval(60*10);
				session.setAttribute("smsCode", smsCode);
			}
		} catch (ServiceException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok("成功发送短信至"+mobile);
	}
	
	
}
