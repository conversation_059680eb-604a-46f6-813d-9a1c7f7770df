package org.easitline.console.utils;

import java.io.BufferedOutputStream;
import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.easitline.common.core.log.LogEngine;
import org.easitline.console.base.Constants;
import org.easitline.console.command.OS;

public class FileUtil {
	
	public static void info(Object message,Throwable t) {
		LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info(message, t);
	}
	
	public static void warn(Object message,Throwable t) {
		LogEngine.getLogger(Constants.EASITLINE_CONSOLE).warn(message, t);
	}
	
	public static void error(Object message,Throwable t) {
		LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(message, t);
	}
	  
    public static String getShellPath(String path) {
    	File file = null;
    	if(OS.WINDOWS) {
    		file = new File(path);
    	}
    	else {
    		file = new File(path);
    	}
    	if(!file.exists()) {
    		try {
				file.createNewFile();
			} catch (IOException e) {
				info("shell create bad "+e.getMessage(),e);
			}
    	}
    	if(file.exists() && file.isFile()) {
    		return file.getAbsolutePath();
    	}else{
    		return null;
    	}
    }

    
    
	/**
	 * 获得应用war文件的资源文件
	 * @param jarFile
	 * @param resourcePath
	 * @return
	 */
	public static Document getResource(File jarFile, String resourcePath) {
		Document doc = null;
		try {
			byte[]  returnBytes = getAppResource(jarFile, resourcePath);
			//this.debug(resourcePath+"->"+new String(returnBytes),null);
			if(null != returnBytes && returnBytes.length>0) {
				String xml = new String(returnBytes, "UTF-8");
				doc = DocumentHelper.parseText(xml);
			}
			return doc;
		} catch (Exception ex) {
			error("从文件[" + jarFile.getName() + "]中读取文件["+ resourcePath + "]失败，原因：" + ex.getMessage(),ex);
		}
		return null;
	}
	
	
	public static byte[] getAppResource(File jarFile, String resourcePath) {
		JarFile jar = null;
		byte[] returnBytes  = null;
		try {
			if (!jarFile.isFile()) {
				warn("目标文件[" + jarFile.getName() + "]不存在!",null);
				return null;
			}
			jar = new JarFile(jarFile);
			JarEntry entry = null;
			entry = jar.getJarEntry(resourcePath);
			if(entry == null) return null;
			ByteArrayOutputStream out = new ByteArrayOutputStream();
			InputStream in = jar.getInputStream(entry);
			byte[] b = new byte[1024];
			int size = -1;
			while (true) {
				size = in.read(b);
				if (size == -1)break;
				out.write(b, 0, size);
			}
			in.close();
			returnBytes = out.toByteArray();
			out.close();
			jar.close();
		} catch (Exception ex) {
			error("获得应用["+jarFile.getName()+"]资源["+resourcePath+"]信息失败，原因："+ex.getMessage(), ex);
		}finally{
			try { jar.close(); } catch (IOException e) {}
		}
		
		return returnBytes;
	
	}
	
	
	
	/**
              * 根据byte数组，生成文件
    */
    public static void getFile(byte[] bfile, String filePath,String fileName) {
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        File file = null;
        try {
            File dir = new File(filePath);
            if(!dir.exists()&&dir.isDirectory()){//判断文件目录是否存在
                dir.mkdirs();
            }
            file = new File(filePath+File.separator+fileName);
            if(file.exists()) {
            	file.deleteOnExit();
            }
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bfile);
        } catch (Exception e) {
            error(e.getMessage(),e);
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException ex) {
                	 error(ex.getMessage(),ex);
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException ex) {
                	error(ex.getMessage(),ex);
                }
            }
        }
    }
    
    public static void writeTextToFile(String text, String filePath) {
        try {
            BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(filePath), "UTF-8"));
            writer.write(text);
            writer.close();
            info(filePath+">写入文件>"+text,null);
        } catch (IOException e) {
        	error("写入文件时出现错误: " + e.getMessage(),e);
        }
    }

    public static String getParentDirectory(String path) {
        File file = new File(path);
        File parent = file.getParentFile();
        return (parent != null) ? parent.getPath() : null;
    }
    
    
}
