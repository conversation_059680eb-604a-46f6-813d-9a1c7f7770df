<%@ page language="java" pageEncoding="utf-8" isELIgnored="false"%>
<%@ page trimDirectiveWhitespaces="true" session="false"%>
<%@ taglib prefix="c"   uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn"  uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="ctxPath" value="${pageContext.request.contextPath}" />
<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<title>运维工具</title>
		 <LINK rel="Shortcut Icon" href="${ctxPath}/favicon.ico" />
		<link rel="stylesheet" href="${staticPath}/lib/layui/css/layui.css">
		<script type="text/javascript" src="${staticPath}/js/jquery.min.js"></script> 
		<style type="text/css">
			 body{padding: 10px 30px;margin: 10px 30px;}
			 .layui-input-block{width: 100%;}
			 .layui-textarea{height: 130px;}
			 .layui-input-block{margin-left: 0px;margin-top: 12px;}
			 .fastSelectSql2 a{color: blue;}
		</style>
	</head>
<body>
	<form id="easyform" class="layui-form">
		 <fieldset class="layui-elem-field site-demo-button" style="margin-top: 10px;">
			  <legend>操作应用</legend>
			  <div style="padding: 15px;">
			      <input type="text" id="appId" class="layui-input" style="width: 120px;display: inline-block;" placeholder="请输入应用ID">
				  <button style="margin-left: 30px;display: inline-block;" class="layui-btn layui-btn-sm" type="button" onclick="reloadParams()">重载参数</button>
				  <button style="margin-left: 30px;display: inline-block;" class="layui-btn layui-btn-sm" type="button" onclick="queryAppParams(1)">查询参数</button>
			  </div>
		 </fieldset>
		 <fieldset class="layui-elem-field site-demo-button" style="margin-top: 10px;">
			  <legend>SQL工具</legend>
		     <textarea id="sqlContent" style="width: 90%;margin: 10px;" class="layui-textarea"></textarea>
		    	  <div class="fastSelectSql" style="line-height: 26px;padding: 8px;color: #999;">快捷操作
		    		 <a href="javascript:void(0)" class="layui-badge-rim" onclick="fastSelectSql('EASI_USER')">用户表</a>
		    		 <a href="javascript:void(0)" class="layui-badge-rim" onclick="fastSelectSql('EASI_APP_INFO')">应用表</a>
		    		 <a href="javascript:void(0)" class="layui-badge-rim" onclick="fastSelectSql('EASI_APP_CONF')">应用参数表</a>
		    		 <a href="javascript:void(0)" class="layui-badge-rim" onclick="fastSelectSql('EASI_DS_INFO')">数据源表</a>
					  <button style="margin-left: 30px;display: inline-block;" class="layui-btn layui-btn-sm" type="button" onclick="doSelect()">查询</button>
					  <button style="margin-left: 30px;display: inline-block;" class="layui-btn layui-btn-sm" type="button" onclick="doUpdate()">执行</button>
		 		</div>
		 </fieldset>
		 <div class="layui-tab layui-tab-brief" style="margin-top: 10px;" lay-filter="resultTabBrief">
		  <ul class="layui-tab-title">
		    <li class="layui-this">result</li>
		  </ul>
		  <div class="layui-tab-content">
		    <div class="layui-tab-item layui-show">
		    	  <textarea  class="layui-textarea" class="layui-input-block" id="sqlToJson"  style="height: 350px;color:#666;"></textarea>
		    </div>
		  </div>
		</div> 
	</form>
	<script type="text/javascript" src="${staticPath}/js/jsrender.min.js"></script>
	<script type="text/javascript" src="${staticPath}/lib/layer/layer.js"></script>
	<script type="text/javascript" src="${staticPath}/lib/layui/layui.js"></script>
	<script type="text/javascript" src="${staticPath}/js/easitline.core-2.0.0.js"></script>
	<script type="text/javascript" src="${staticPath}/js/requreLib.js"></script>
	<script type="text/javascript">
	
	String.prototype.startWith=function(str){     
		var reg=new RegExp("^"+str);     
		return reg.test(this);        
	}  

    String.prototype.endWith=function(str){     
	     var reg=new RegExp(str+"$");     
		 return reg.test(this);        
	}
    
    String.prototype.replaceAll = function(s1,s2){ 
    	return this.replace(new RegExp(s1,"gm"),s2); 
    }
    $(document).keydown(function (event) {
		 if (event.keyCode == 27) {
	         if(layer!=null){
	        	 layer.closeAll();
	         }
	     }
		 if(event.keyCode==13){ 
			// reqExcute();
		 }
	 });
	
	function fastSelectSql(tableName){
		$("#sqlContent").val("SELECT * FROM "+tableName);
	}
	
	
	function queryAppParams(){
		var appId = $('#appId').val();
		$.post("${ctxPath}/tools",{op:'queryAppParams',appId:appId},function(result) { 
			layer.msg('操作成功');
			$('#sqlToJson').val(JSON.stringify(result.data));
		});
	}
	
	function reloadParams(){
		var appId = $('#appId').val();
		$.post("${ctxPath}/tools",{op:'reloadParams',appId:appId},function(result) { 
			layer.msg('操作成功');
		});
	}
	
	function doSelect(){
		var sqlContent = $('#sqlContent').val();
		sqlContent = encodeURIComponent(sqlContent);
		$.post("${ctxPath}/tools",{op:'querySql',sqlContent:sqlContent},function(result) { 
			$('#sqlToJson').val(JSON.stringify(result.data));
		});
	}
	
	function doUpdate(){
		var sqlContent = $('#sqlContent').val();
		sqlContent = encodeURIComponent(sqlContent);
		$.post("${ctxPath}/tools",{op:'excuteSql',sqlContent:sqlContent},function(result) { 
			layer.msg('操作成功');
			$('#sqlToJson').val(JSON.stringify(result.data));
		});
	}
	
</script>
</body>
</html>
