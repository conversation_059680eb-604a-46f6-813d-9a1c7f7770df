package org.easitline.console.vo;


public class DatasourceModel {

	private String dataSourceName;//数据源名称
	private String dataBaseType;  //数据库类型
	private String dataBaseName;  //数据库名称
	private String ipAddress;     //ip地址
	private String port;     	  //端口
	private String maxConnection; //最大连接数
	private String minConnection; //最小连接数
	private String userName;      //用户名
	private String password;      //密码
	public String getDataSourceName() {
		return dataSourceName;
	}
	public void setDataSourceName(String dataSourceName) {
		this.dataSourceName = dataSourceName;
	}
	public String getDataBaseType() {
		return dataBaseType;
	}
	public void setDataBaseType(String dataBaseType) {
		this.dataBaseType = dataBaseType;
	}
	public String getDataBaseName() {
		return dataBaseName;
	}
	public void setDataBaseName(String dataBaseName) {
		this.dataBaseName = dataBaseName;
	}
	public String getIpAddress() {
		return ipAddress;
	}
	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}
	public String getPort() {
		return port;
	}
	public void setPort(String port) {
		this.port = port;
	}
	public String getMaxConnection() {
		return maxConnection;
	}
	public void setMaxConnection(String maxConnection) {
		this.maxConnection = maxConnection;
	}
	public String getMinConnection() {
		return minConnection;
	}
	public void setMinConnection(String minConnection) {
		this.minConnection = minConnection;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
	
	
	
}
