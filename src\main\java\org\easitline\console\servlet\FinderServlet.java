package org.easitline.console.servlet;


import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.kit.HttpKit;
import org.apache.commons.lang3.StringUtils;
import org.easitline.console.base.ConsoleBaseServlet;
import org.easitline.console.utils.PropKit;


@WebServlet(urlPatterns = { "/finder/*" })
public class FinderServlet extends ConsoleBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForGetKey(){
		String key=PropKit.get("SecretKey","");
		if(StringUtils.isBlank(key)){
			return EasyResult.fail("SecretKey is null");
		}else{
			return EasyResult.ok(key);
		}
	}
	/**
	 * 
	 * @return
	 */
	public EasyResult actionForSynConfig(){
		boolean nodeCenter=PropKit.getBoolean("NodeCenter",false);
		String centerKey=PropKit.get("SecretKey","");
		if(nodeCenter){
			for(int i=1;i<10;i++){
				String url=PropKit.get("node"+i+".url");
				if(StringUtils.isBlank(url))break;
				String key=HttpKit.get(url+"/finder/getKey");
				if(centerKey.equals(key)){
					//to do
					HttpKit.get(url+"/finder/synSystemConfig");
					HttpKit.get(url+"/finder/synAppConfig");
					HttpKit.get(url+"/finder/synAppConfig");
				}
			}
		}
		return EasyResult.ok();
	}
	
	
	
	
	
	
	
	
	
	
	@Override
	protected String getResId() {
		return null;
	}
	
}
