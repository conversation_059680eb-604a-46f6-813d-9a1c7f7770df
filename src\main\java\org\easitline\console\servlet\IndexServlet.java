package org.easitline.console.servlet;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.EasyServer;
import org.easitline.common.core.Globals;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.console.base.ConsoleBaseServlet;
import org.easitline.console.base.Constants;
import org.easitline.console.dbscript.DBHelper;
import org.easitline.console.utils.AesUtils;
import org.easitline.console.utils.MenuDataUtil;
import org.easitline.console.utils.MenuDataUtil.MenuObj;
import org.easitline.console.utils.ScriptHelper;
import org.jsoup.Connection;
import org.jsoup.Connection.Method;
import org.jsoup.Connection.Response;
import org.jsoup.Jsoup;

import com.alibaba.fastjson.JSONObject;

@WebServlet("/index/*")
public class IndexServlet extends ConsoleBaseServlet {

	private static final long serialVersionUID = 1L;

	public void actionForIndex() {
		setAttr("machineCode", ServerContext.getProperties("MACHINE_CODE",""));
		setAttr("profileActive", ServerContext.getProperties("PROFILE_ACTIVE",""));
		setAttr("platform", ServerContext.getServerName()+"&"+ServerContext.getNodeName());
		setAttr("versionInfo", "Mars"+EasyServer.getVersion()+"_"+EasyServer.getVersionDate());
		setAttr("consoleUpdateConfTime", getConsoleUpdateConfTime());
		
	    HttpSession session = getRequest().getSession();
	    String userAcct = session.getAttribute("MARS_CONSOLE_USER").toString();
	    String roleId = session.getAttribute("MARS_CONSOLE_ROLE").toString();
	    setAttr("userAcct", userAcct);
	    setAttr("roleId", roleId);
	    String _page = getPara("_page");
	    if(_page == null || "".equals(_page)){
			 _page = "/pages/server/summary.jsp";
		}
		setAttr("page", _page);
		
		if(!checkState()) {
			renderHtml("加密服务不可用，请检查配置和服务是否正常");
			return;
		}
		forward("/pages/index.jsp");
	}

	public EasyResult actionForGetMenu() {
		String roleType = getRoleType();
		List<MenuObj> list = MenuDataUtil.getService().getRoleRes(roleType);
		return EasyResult.ok(list);
	}
	
	public EasyResult actionForCheckField() {
		try {
			ScriptHelper.getService().checkField();
			ScriptHelper.getService().dbPwdEncrypt();
		} catch (Exception e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	
	private static long prevAccessTs = 0;
	public void publishAccessInfo() {
		if(System.currentTimeMillis()-prevAccessTs > 30000) {
			prevAccessTs = System.currentTimeMillis();
			String url = ServerContext.getProperties("G_VERSION_CENTER_URL", "");
			if(StringUtils.isNotBlank(url)) {
				Connection connection = Jsoup.connect(url+"/yq-work/accessLog");
				try {
					JSONObject result = new JSONObject();
					JSONObject object = new JSONObject();
					object.put("req-user",this.getRequest().getSession().getAttribute("MARS_CONSOLE_USER"));
					object.put("req-version",EasyServer.getVersion()+"_"+EasyServer.getVersionDate());
					object.put("req-serverName",ServerContext.getServerName());
					object.put("req-ip",WebKit.getIP(getRequest()));
					object.put("req-node",ServerContext.getNodeName());
					object.put("req-url",getRequest().getRequestURL().toString());
					object.put("user-agent",getRequest().getHeader("user-agent"));
					result.put("data", object);
					connection.header("Content-Type", "application/json;charset=UTF-8");
					connection.requestBody(result.toJSONString());
					connection.method(Method.POST).timeout(5000).ignoreContentType(true).execute();
				} catch (IOException e) {
					this.error("上报失败", null);
				}
			}
		}
	}
	
	
	private static long prevGetRemoteAppTime = 0;
	public void actionForGetRemoteAppList() {
		if(System.currentTimeMillis()-prevGetRemoteAppTime > 2000) {
			prevGetRemoteAppTime = System.currentTimeMillis();
			JSONObject params = getJSONObject();
			String url = params.getString("reqUrl");
			if(StringUtils.isNotBlank(url)) {
				Connection connection = Jsoup.connect(url+"/easitline-jk/servlet/app");
				try {
					String reqIp = WebKit.getIP(getRequest());
					String token =  String.valueOf(System.currentTimeMillis());
					String uuid = RandomKit.uuid();
					System.setProperty("mars_app_list_token",token+"_"+uuid);
					JSONObject result = new JSONObject();
					JSONObject object = new JSONObject();
					connection.data("uuid",uuid);
					object.put("req-user",this.getRequest().getSession().getAttribute("MARS_CONSOLE_USER"));
					object.put("req-version",EasyServer.getVersion()+"_"+EasyServer.getVersionDate());
					object.put("req-serverName",ServerContext.getServerName());
					object.put("req-ip",reqIp);
					object.put("req-node",ServerContext.getNodeName());
					object.put("req-url",getRequest().getRequestURL().toString());
					object.put("user-agent",getRequest().getHeader("user-agent"));
					result.put("data", object);
					connection.header("Content-Type", "application/json;charset=UTF-8");
					connection.requestBody(result.toJSONString());
					
					getLogger().info(result.toJSONString());
					
					Response response = connection.method(Method.POST).timeout(5000).ignoreContentType(true).execute();
					String str = response.body();
					JSONObject rs = JSONObject.parseObject(str);
					rs.put("uuid",uuid);
					renderJson(rs);
				} catch (IOException e) {
					this.error("获取失败:"+e.getMessage(), e);
					renderJson(EasyResult.fail(e.getMessage()));
				}
			}
			return;
		}
		renderJson(EasyResult.fail("请求太频繁稍后再试"));
	}
	
	
	public EasyResult actionForAddUserField() {
		try {
			String sql="ALTER TABLE EASI_USER ADD COLUMN USER_ID  varchar(100)";
			Constants.getDb().execute(sql.toString(),new Object[]{});
			
			Constants.getDb().executeUpdate("update EASI_USER set USER_ID =  RANDOM()", new Object[]{});
			
			sql="ALTER TABLE EASI_USER ADD COLUMN MOBILE  varchar(100)";
			Constants.getDb().execute(sql.toString(),new Object[]{});
			
			sql="ALTER TABLE EASI_USER ADD COLUMN AUTH_CODE  varchar(100)";
			Constants.getDb().execute(sql.toString(),new Object[]{});
			
			sql="ALTER TABLE EASI_USER ADD COLUMN AUTH_CODE_TIME  varchar(100)";
			Constants.getDb().execute(sql.toString(),new Object[]{});
			
			sql="ALTER TABLE EASI_USER ADD COLUMN CREATE_TIME  varchar(20)";
			Constants.getDb().execute(sql.toString(),new Object[]{});
			
			sql="ALTER TABLE EASI_USER ADD COLUMN LAST_LOGIN_TIME  varchar(20)";
			Constants.getDb().execute(sql.toString(),new Object[]{});
			
			sql="ALTER TABLE EASI_USER ADD COLUMN LAST_LOGIN_IP  varchar(255)";
			Constants.getDb().execute(sql.toString(),new Object[]{});
			
		} catch (SQLException e) {
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForCheckUserInfo() {
		String userAcct = getRequest().getSession().getAttribute("MARS_CONSOLE_USER").toString();
		String key  = ServerContext.getProperties("SECURITY_KEY", "");
		String flag = ServerContext.getProperties("SECURITY_ENTER", "");
		String profileActive = ServerContext.getProperties("PROFILE_ACTIVE", "");
		StringBuffer sb = new StringBuffer();
		
		try {
			this.updateUserPwd();
			String password = this.getConsoleQuery().queryForString("select LOGIN_PWD from easi_user where login_acct = ?", userAcct);
			if("admin#mars".equalsIgnoreCase(password)||"admin".equalsIgnoreCase(password)||"admin1234".equalsIgnoreCase(password)||"admin#console".equalsIgnoreCase(password)) {
				sb.append("检测到您的密码是弱密码,请立即修改密码.<br>");
			}else if("3DES_ddxpsCG0pWEZAmoGdEMQlA==".equalsIgnoreCase(password)||"3DES_qZH/7IaJ1/s=".equalsIgnoreCase(password)||"3DES_TmaTdIg3TwM+7kTBcpWMzQ==".equalsIgnoreCase(password)) {
				sb.append("检测到您的密码是弱密码,请立即修改密码.<br>");
			}
		} catch (SQLException e) {
			this.getLogger().error(null,e);
		}
		if(StringUtils.isBlank(profileActive)) {
			sb.append("系统检测到您没设置系统环境类型,请立即设置.<br>");
		}
		if(StringUtils.isBlank(key)) {
			sb.append("系统检测到您没设置安全KEY,请立即设置.<br>");
		}
		if(!"1".equals(flag)) {
			sb.append("系统检测到您未设置安全访问入口,请在平台参数设置>启用安全入口<br>");
		}
		return EasyResult.ok(sb.toString());
	}
	
	
	public void updateUserPwd() {
		try {
			EasyQuery query = Constants.getDb();
			EasyRecord record = new EasyRecord("EASI_USER","LOGIN_ACCT");
			List<JSONObject> list= query.queryForList("select LOGIN_ACCT,LOGIN_PWD from EASI_USER",null,new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					record.setPrimaryValues(jsonObject.getString("LOGIN_ACCT"));
					String pw = jsonObject.getString("LOGIN_PWD");
					if(!pw.startsWith("AES")) {
						record.set("LOGIN_PWD","AES_"+AesUtils.encrypt(pw, AesUtils.USER_KEY));
						query.update(record);
					}
				}
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
	
	public EasyResult actionForResetAccountAesPwd() {
		String oldKey = getPara("oldKey");
		String newKey = getPara("newKey");
		DBHelper dbHelper = new DBHelper();
		dbHelper.resetAccountAesPwd(oldKey,newKey);
		return EasyResult.ok();
	}
	
	
	public void actionForDownloadTpl() {
		String path = Globals.WEBAPPS_DIR+"\\easitline-console\\pages\\application\\example";
		String type = getPara("type");
		if("appinfo".equals(type)) {
			path = path + File.separator + "path";
		}else if("".equals(type)) {
			
		}
		File file = new File(path);
		renderFile(file, "appinfo.xml", false);
	}
	
}
