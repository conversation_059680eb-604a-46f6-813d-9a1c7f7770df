package org.easitline.console.filter;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.utils.kit.WebKit;
import org.apache.commons.lang3.StringUtils;
import org.easitline.console.base.Constants;

@WebFilter("/*")
public class GlobalFilter implements Filter{
	
	@Override
	public void destroy() {
		
	}
	

	@Override
	public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain)throws IOException, ServletException {
		 HttpServletRequest request = (HttpServletRequest)servletRequest;  
		 HttpServletResponse response = (HttpServletResponse)servletResponse;  
		
		 String acct = (String)((HttpServletRequest) request).getSession().getAttribute("MARS_CONSOLE_USER");
		 
		 String url = request.getRequestURL().toString();
		 
		 String data = request.getParameter("data");
		 if(StringUtils.isNotBlank(data)) {
			 LogEngine.getLogger(Constants.EASITLINE_CONSOLE,Constants.EASITLINE_CONSOLE).info(acct+">"+WebKit.getIP(request)+">"+url+">"+request.getQueryString()+",data>"+data);
		 }
		 
		 LogEngine.getLogger(Constants.EASITLINE_CONSOLE,Constants.EASITLINE_CONSOLE+"-access").info(acct+">"+WebKit.getIP(request)+">"+url+">"+request.getQueryString()+",data>"+data);
		 
		 String method = request.getMethod();
		 if(!"get".equalsIgnoreCase(method)&&!"post".equalsIgnoreCase(method)) {
			Render.renderHtml(request, response, method+"不支持");
			return;
		 }
		 response.addHeader("X-Content-Type-Options", "nosniff");
		 response.addHeader("Content-Security-Policy", "none");
		 response.addHeader("X-XSS-Protection", "1; mode=block");
		 response.setHeader("Access-Control-Allow-Methods","GET,POST");
		 
		 request.setAttribute("ctxPath",Constants.getContextPath());
		 request.setAttribute("staticPath",Constants.getContextPrefix()+"/easitline-static");
		 
		 chain.doFilter(request, response);
	}

	@Override
	public void init(FilterConfig arg0) throws ServletException {
		
	}
   
}
