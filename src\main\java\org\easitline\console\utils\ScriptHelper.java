package org.easitline.console.utils;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.log4j.Logger;
import org.easitline.common.core.Globals;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.RandomKit.SMSAuthCodeType;
import org.easitline.console.base.Constants;
import org.easitline.console.service.ConfigCryptorService;

import com.alibaba.fastjson.JSONObject;

/**
 * 通过标准库来生成ycmain,ycbusi,stat的表信息。
 * --这里需要注意，和现场进行比较，oracle  mysql   的 数据库的命名 需要从表和配置中进行获取。
 * <AUTHOR>
 *
 */
public class ScriptHelper {

	private static class Holder{
		private static ScriptHelper service=new ScriptHelper();
	}
	public static ScriptHelper getService(){
		return Holder.service;
	}
	
	private static boolean isExcuteSql = false;
	
	private EasyQuery query = null;
	
	public ScriptHelper() {
		
	}
	
	public ScriptHelper(EasyQuery query) {
		this.query=query;
	}
	
	public JSONObject getColumnInfo( String catalog,String tableName) {
		ResultSet columnRs = null;
		JSONObject cols=new JSONObject();
		Connection conn = null ;
		try {
			conn = query.getConnection();
			DatabaseMetaData mdm = conn.getMetaData();
			columnRs = mdm.getColumns(catalog, catalog, tableName, "%");
			while (columnRs.next()) {
				JSONObject columnJson = new JSONObject();
				columnJson.put("TABLE_NAME", columnRs.getString("TABLE_NAME").toUpperCase());
				columnJson.put("COLUMN_NAME", columnRs.getString("COLUMN_NAME").toUpperCase());
				columnJson.put("TYPE_NAME", columnRs.getString("TYPE_NAME"));
				columnJson.put("COLUMN_SIZE", columnRs.getString("COLUMN_SIZE"));
				columnJson.put("IS_NULLABLE", columnRs.getString("IS_NULLABLE"));
				columnJson.put("REMARKS", columnRs.getString("REMARKS"));
				
				cols.put(columnRs.getString("COLUMN_NAME").toUpperCase(),columnJson);
				
				//LogEngine.getLogger("easitline-console", "easitline-console").info(columnJson);
			}
			columnRs.close();
		} catch (Exception e) {
			e.printStackTrace();
		}finally {
			try {if(conn!=null)conn.close();} catch (SQLException e) {e.printStackTrace();}
		}
		return cols;
	}
	
	public Connection getConnection() {
		EasyQuery query = EasyQuery.getQuery("com.mysql.jdbc.Driver","**************************************************************************************", "root", "root");
		try {
			return query.getConnection();
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return null;
	}
	/**
	 * 获得数据库下的所有表
	 * @param catalog
	 * @return
	 */
	public ArrayList<String> getAllTables(String catalog) {
		ArrayList<String> table = new ArrayList<String>();
		Connection conn = null ;
		try {
			conn = getConnection();
			DatabaseMetaData mdm = conn.getMetaData();
			String[] str_tableType = { "TABLE" ,"VIEW"};
			ResultSet rs_tableName = mdm.getTables(catalog.toLowerCase(), catalog, "%", str_tableType);
			while (rs_tableName.next()) {
				table.add(rs_tableName.getString("TABLE_NAME").toUpperCase());
			}
			LogEngine.getLogger("easitline-console").info("table count >"+table.size());
			
			rs_tableName.close();
		} catch (Exception e) {
			e.printStackTrace();
		}finally {
			try {if(conn!=null)conn.close();} catch (SQLException e) {e.printStackTrace();}
		}
		return table;
	}
	
	public JSONObject  buildTableJson(String catalog){
		 List<String> catalogs= getAllTables(catalog);
		 JSONObject array=new JSONObject();
		 for(String tableName:catalogs){
			 array.put(tableName.toUpperCase(), getColumnInfo(catalog,tableName));
		 }
		 return array;
		 
	}
	public Map<String,String> getSchemaFileMap(){
		String path=Globals.DB_DIR+File.separator+"schema";
		File files=new File(path);
		Map<String,String>  dict = new LinkedHashMap<String,String>();
		if(files.exists()){
			File[] fileArray=files.listFiles();
			for(File file:fileArray){
				dict.put(file.getAbsolutePath(),file.getName());
			}
		}
		return dict;
	}
	
	/**
	 * 获得指定数据库下的所有表信息。
	 * @param catalog  数据库
	 * @return
	 */
	public JSONObject  getSchemaInfo(String catalog){
		JSONObject tableObjects  = new JSONObject();
		List<String> tableNames  =  this.getAllTables(catalog);
		for(String tableName:tableNames){
			tableObjects.put(tableName.toUpperCase(), getColumnInfo(catalog,tableName));
		}
		return tableObjects;
	}
	public JSONObject matchTable(String catalog,String path){
		Logger logger = LogEngine.getLogger("scriptMatch");
		String jsonStr = null ;
		try {
			jsonStr = FileKit.readToString(path);
		} catch (IOException e) {
			e.printStackTrace();
			return null;
		}
		JSONObject result=new JSONObject();

		//版本库数据库
		JSONObject source=JSONObject.parseObject(jsonStr);
		
		List<String> tableMsg=new ArrayList<String>();
		
		List<JSONObject> tableColMsg=new ArrayList<JSONObject>();
		
		Set<String> tableNames=source.keySet();
		 
		for(String tableName:tableNames){
			try {
				query.queryForList("select * from "+catalog+"."+tableName+" where 1 = 2");
			} catch (SQLException e) {
				tableMsg.add(tableName);
				logger.error(e);
				continue;
			}

			//获取版本库表所有栏目
			JSONObject colunms=source.getJSONObject(tableName);
			
			//生产库表所有栏目
			JSONObject cols=getColumnInfo(catalog,tableName);
			
			JSONObject x=new JSONObject();
			for(String key:colunms.keySet()){
				if(!cols.containsKey(key)){
					//缺字段
					x.put(key, colunms.getJSONObject(key));
					logger.warn(tableName+" not exsit field "+key);
					logger.warn(colunms.getJSONObject(key));
				}
			}
			if(x.size()>0){
				JSONObject y=new JSONObject();
				y.put(tableName,x);
				tableColMsg.add(y);
			}
		}
		result.put("tableNames", tableMsg);
		result.put("tableCols",tableColMsg);
		return result;
		
	}
	
	public String excuteSqlUpdate(){
		EasyQuery query = Constants.getDb();
		return excuteSqlUpdate(query);
	}
	
	public String excuteSqlUpdate(EasyQuery query){
		try {
			return checkField();
		} catch (SQLException e) {
			return e.getMessage();
		}
	}

	public static boolean addMachineCode(boolean reloadCache){
		try {
			EasyQuery query = Constants.getDb();
			Map<String,String> keys = ServerContext.getConfig();
			if(!keys.containsKey("MACHINE_CODE")){
				String machineCode = MachineCodeGenerator.generateMachineCode();
				String sql = "insert into  EASI_CONF(CONF_KEY,CONF_VALUE,CAN_DELETE) values(?,?,3)";
				query.execute(sql, new Object[]{"MACHINE_CODE",machineCode});
				FileUtil.writeTextToFile("机器码："+machineCode+"\n生成时间："+EasyDate.getCurrentDateString(),FileUtil.getParentDirectory(Globals.BASE_DIR)+File.separator+"machineCode.txt");
				if(reloadCache) {
					ServerContext.reload();
				}
				return true;
			}
		} catch (SQLException e) {
			LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(e.getMessage(),e);
		} catch (Exception e) {
			LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(e.getMessage(),e);
		}
		return false;
	}
	
	public EasyResult addSecurityKey() {
		try {
			EasyQuery query = Constants.getDb();
			Map<String,String> keys = ServerContext.getConfig();
			String securityKey = RandomKit.smsAuthCode(8, SMSAuthCodeType.CharAndNumbers);
			ScriptHelper.addMachineCode(false);
			if(keys.containsKey("SECURITY_KEY")){
				String sql = "update EASI_CONF set CONF_VALUE = ? where CONF_KEY = ?";
				query.executeUpdate(sql, new Object[]{securityKey,"SECURITY_KEY"});
			}else{
				String sql = "insert into  EASI_CONF(CONF_KEY,CONF_VALUE,CAN_DELETE) values(?,?,1)";
				query.execute(sql, new Object[]{"SECURITY_KEY",securityKey});
			}
			if(keys.containsKey("SECURITY_ENTER")){
				String sql = "update EASI_CONF set CONF_VALUE = ? where CONF_KEY = ?";
				query.executeUpdate(sql, new Object[]{1,"SECURITY_ENTER"});
			}else{
				String sql = "insert into  EASI_CONF(CONF_KEY,CONF_VALUE,CAN_DELETE) values(?,?,1)";
				query.execute(sql, new Object[]{"SECURITY_ENTER",1});
			}
			ServerContext.reload();
			return EasyResult.ok(securityKey);
		} catch (Exception ex) {
			Logger logger=LogEngine.getLogger("scriptMatch");
			logger.error(ex.getMessage(), ex);
			return EasyResult.fail(ex.getMessage());
		}
	}
	public EasyResult dbPwdEncrypt() {
		try {
			EasyQuery query = Constants.getDb();
			EasyRecord record = new EasyRecord("EASI_DS_INFO","SYS_DS_NAME");
			List<JSONObject> list= query.queryForList("select SYS_DS_NAME,PASSWORD from EASI_DS_INFO",null,new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					record.setPrimaryValues(jsonObject.getString("SYS_DS_NAME"));
					String pw = jsonObject.getString("PASSWORD");
					if(!pw.startsWith("3DES_")&&!pw.startsWith(ConfigCryptorService.prefixFlag)) {
						record.set("PASSWORD",ConfigCryptorService.encryptString(pw, "DS"));
						query.update(record);
					}
				}
			}
			return EasyResult.ok(null,"更新成功");
		} catch (Exception ex) {
			Logger logger=LogEngine.getLogger("scriptMatch");
			logger.error(ex.getMessage(), ex);
			try {
				Constants.getDb().execute("ALTER TABLE EASI_DS_INFO ADD COLUMN URL_PROPERTIES  varchar(255)");
				Constants.getDb().execute("ALTER TABLE EASI_DS_INFO ADD COLUMN JDBC_URL  varchar(1000)");
			} catch (SQLException e) {
				logger.error(ex.getMessage(), e);
			}
			return EasyResult.error(0, "请再重试提交。更新数据源失败，原因："+ex.getMessage());
		}
	}
	
	private boolean safeExecuteUpdate(EasyQuery query, String sql, Object[] params, StringBuilder result, String successMessage) {
		try {
			if (params != null) {
				query.executeUpdate(sql, params);
			} else {
				query.executeUpdate(sql);
			}
			if (successMessage != null) {
				result.append(successMessage);
			}
			return true;
		} catch (SQLException e) {
			LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("执行SQL失败: " + sql + ", 错误: " + e.getMessage(), e);
			return false;
		}
	}

	public String checkField() throws SQLException {
		return checkField(Constants.getDb());
	}
	
	public String checkField(EasyQuery query) throws SQLException {
		if(isExcuteSql) {
			return "已执行，如需再次执行请升级console或重载";
		}
		Logger logger = LogEngine.getLogger(Constants.EASITLINE_CONSOLE);
		StringBuilder result = new StringBuilder();
		
		try {
			// 处理 EASI_USER 表
			EasyRow userRow = query.queryForRow("select * from EASI_USER");
			if(userRow != null) {
				if(!hasFiled(userRow, "USER_ID")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_USER ADD COLUMN USER_ID varchar(100)", null, result, null);
					safeExecuteUpdate(query, "update EASI_USER set USER_ID = RANDOM()", new Object[]{}, result, null);
					safeExecuteUpdate(query, "ALTER TABLE EASI_USER ADD COLUMN AUTH_CODE varchar(100)", null, result, "EASI_USER表: 添加USER_ID字段成功<br>");
				}
				if(!hasFiled(userRow, "MOBILE")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_USER ADD COLUMN MOBILE varchar(100)", null, result, "EASI_USER表: 添加MOBILE字段");
				}
				if(!hasFiled(userRow, "AUTH_CODE")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_USER ADD COLUMN AUTH_CODE varchar(100)", null, result, "EASI_USER表: 添加AUTH_CODE字段");
					safeExecuteUpdate(query, "ALTER TABLE EASI_USER ADD COLUMN AUTH_CODE_TIME varchar(30)", null, result, "EASI_USER表: 添加AUTH_CODE_TIME字段");
				}
				if(!hasFiled(userRow, "LAST_PWD_CHANGE_TIME")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_USER ADD COLUMN LAST_PWD_CHANGE_TIME varchar(20)", null, result,"EASI_USER表: 添加LAST_PWD_CHANGE_TIME字段成功<br>");	
					safeExecuteUpdate(query, "update EASI_USER set LAST_PWD_CHANGE_TIME = ?", new Object[]{EasyDate.getCurrentDateString()}, result, null);
				}
				
				if(!hasFiled(userRow, "CREATE_TIME")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_USER ADD COLUMN CREATE_TIME varchar(20)", null, result, null);
					safeExecuteUpdate(query, "update EASI_USER set CREATE_TIME = ?", new Object[]{EasyDate.getCurrentDateString()}, result, null);
					safeExecuteUpdate(query, "ALTER TABLE EASI_USER ADD COLUMN LAST_LOGIN_TIME varchar(20)", null, result, null);
					safeExecuteUpdate(query, "ALTER TABLE EASI_USER ADD COLUMN LAST_LOGIN_IP varchar(255)", null, result,
						"EASI_USER表: 添加CREATE_TIME,LAST_LOGIN_TIME,LAST_LOGIN_IP字段成功<br>");
				}
				
				if(!hasFiled(userRow, "OTP_STATE")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_USER ADD COLUMN OTP_SK varchar(255)", null, result, null);
					safeExecuteUpdate(query, "update EASI_USER set OTP_SK = ?", new Object[]{RandomKit.smsAuthCode(16,SMSAuthCodeType.CharAndNumbers)}, result, null);
					safeExecuteUpdate(query, "ALTER TABLE EASI_USER ADD COLUMN OTP_STATE INT DEFAULT 0", null, result,
						"EASI_USER表: 添加OTP_SK,OTP_STATE字段成功<br>");
				}
			}
			
			// 处理 EASI_DS_INFO 表
			EasyRow dsRow = query.queryForRow("select * from EASI_DS_INFO");
			if(dsRow != null) {
				if(!hasFiled(dsRow, "CONF_TYPE")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_DS_INFO ADD COLUMN CONF_TYPE varchar(20) DEFAULT 'simple'", null, result, null);
					safeExecuteUpdate(query, "ALTER TABLE EASI_DS_INFO ADD COLUMN DRIVER_NAME varchar(50)", null, result,
						"EASI_DS_INFO表: 添加CONF_TYPE,DRIVER_NAME字段成功<br>");
				}
				
				if(!hasFiled(dsRow, "DB_TYPE_NAME")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_DS_INFO ADD COLUMN DB_TYPE_NAME varchar(50) DEFAULT ''", null, result, null);
					safeExecuteUpdate(query, "update EASI_DS_INFO set DB_TYPE_NAME = DB_TYPE", null, result,
						"EASI_DS_INFO表: 添加DB_TYPE_NAME字段成功<br>");
				}
				
				if(!hasFiled(dsRow, "DRIVER_CLASS")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_DS_INFO ADD COLUMN DRIVER_CLASS varchar(100) DEFAULT 'no'", null, result, null);
					safeExecuteUpdate(query, "update EASI_DS_INFO set DRIVER_CLASS = 'no'", null, result,
						"EASI_DS_INFO表: 添加DRIVER_CLASS字段成功<br>");
				}
				
				if(!hasFiled(dsRow, "DB_MASTER")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_DS_INFO ADD COLUMN DB_MASTER varchar(50) DEFAULT 'true'", null, result,
						"EASI_DS_INFO表: 添加DB_MASTER字段成功<br>");
				}
				
				if(!hasFiled(dsRow, "URL_PROPERTIES")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_DS_INFO ADD COLUMN URL_PROPERTIES varchar(255)", null, result,
						"EASI_DS_INFO表: 添加URL_PROPERTIES字段成功<br>");
				}
				
				if(!hasFiled(dsRow, "JDBC_URL")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_DS_INFO ADD COLUMN JDBC_URL varchar(1000)", null, result,
						"EASI_DS_INFO表: 添加JDBC_URL字段成功<br>");
				}
				
				// 执行密码加密
				EasyResult pwdResult = dbPwdEncrypt();
				if(pwdResult.isOk()) {
					result.append("EASI_DS_INFO表: 数据源密码加密成功<br>");
				}
			}
			
			// 处理 EASI_APP_CONF 表
			EasyRow appConfRow = query.queryForRow("select * from EASI_APP_CONF");
			if(appConfRow != null) {
				if(!hasFiled(appConfRow, "P_ITEM_KEY")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_APP_CONF ADD COLUMN P_ITEM_KEY varchar(50) DEFAULT '0'", null, result, null);
					safeExecuteUpdate(query, "ALTER TABLE EASI_APP_CONF_HIS ADD COLUMN P_ITEM_KEY varchar(50) DEFAULT '0'", null, result,"EASI_APP_CONF表: 添加P_ITEM_KEY字段成功<br>");
				}
				
				if(!hasFiled(appConfRow, "ORDER_INDEX")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_APP_CONF ADD COLUMN ORDER_INDEX INT DEFAULT 0", null, result,"EASI_APP_CONF表: 添加ORDER_INDEX字段成功<br>");
				}
				if(!hasFiled(appConfRow, "UPDATE_TIME")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_APP_CONF ADD COLUMN UPDATE_TIME varchar(30) DEFAULT ''", null, result,"EASI_APP_CONF表: 添加UPDATE_TIME字段成功<br>");
					safeExecuteUpdate(query, "ALTER TABLE EASI_APP_CONF ADD COLUMN BASE_FLAG INT DEFAULT 1", null, result,"EASI_APP_CONF表: 添加BASE_FLAG字段成功<br>");
				}
			}
			
			// 处理 EASI_APP_INFO 表
			EasyRow appInfoRow = query.queryForRow("select * from EASI_APP_INFO");
			if(appInfoRow != null) {
				if(!hasFiled(appInfoRow, "LAST_EDIT_CONF_TIME")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_APP_INFO ADD COLUMN LAST_EDIT_CONF_TIME varchar(30) DEFAULT '0'", null, result,
						"EASI_APP_INFO表: 添加LAST_EDIT_CONF_TIME字段成功<br>");
				}
				
				if(!hasFiled(appInfoRow, "VERSION_DATE")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_APP_INFO ADD COLUMN VERSION_DATE varchar(50) DEFAULT '0'", null, result,
						"EASI_APP_INFO表: 添加VERSION_DATE字段成功<br>");
				}
				
				if(!hasFiled(appInfoRow, "DEPOY_DIR")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_APP_INFO ADD COLUMN DEPOY_DIR varchar(255)", null, result,
						"EASI_APP_INFO表: 添加DEPOY_DIR字段成功<br>");
				}
				
				if(!hasFiled(appInfoRow, "APP_VERSION_DESC")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_APP_INFO ADD COLUMN APP_VERSION_DESC varchar(1000)", null, result,
						"EASI_APP_INFO表: 添加APP_VERSION_DESC字段成功<br>");
				}
				
				if(!hasFiled(appInfoRow, "LAST_APP_VERSION")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_APP_INFO ADD COLUMN LAST_APP_VERSION varchar(100)", null, result,
						"EASI_APP_INFO表: 添加LAST_APP_VERSION字段成功<br>");
				}
				
				if(!hasFiled(appInfoRow, "LAST_MODIFIED")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_APP_INFO ADD COLUMN LAST_MODIFIED varchar(30)", null, result,
						"EASI_APP_INFO表: 添加LAST_MODIFIED字段成功<br>");
				}
				
				if(!hasFiled(appInfoRow, "WAR_SIZE")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_APP_INFO ADD COLUMN WAR_SIZE varchar(30)", null, result,
						"EASI_APP_INFO表: 添加WAR_SIZE字段成功<br>");
				}
			}
			
			// 处理 EASI_APP_INFO_DEPLOY_HIS 表
			EasyRow deployHisRow = query.queryForRow("select * from EASI_APP_INFO_DEPLOY_HIS");
			if(deployHisRow != null) {
				if(!hasFiled(deployHisRow, "APP_VERSION_DESC")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_APP_INFO_DEPLOY_HIS ADD COLUMN APP_VERSION_DESC varchar(1000)", null, result,
						"EASI_APP_INFO_DEPLOY_HIS表: 添加APP_VERSION_DESC字段成功<br>");
				}
				
				if(!hasFiled(deployHisRow, "LAST_MODIFIED")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_APP_INFO_DEPLOY_HIS ADD COLUMN LAST_MODIFIED varchar(30)", null, result,
						"EASI_APP_INFO_DEPLOY_HIS表: 添加LAST_MODIFIED字段成功<br>");
				}
				
				if(!hasFiled(deployHisRow, "WAR_SIZE")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_APP_INFO_DEPLOY_HIS ADD COLUMN WAR_SIZE varchar(30)", null, result,
						"EASI_APP_INFO_DEPLOY_HIS表: 添加WAR_SIZE字段成功<br>");
				}
			}
			
			// 处理 EASI_CONF 表
			EasyRow confRow = query.queryForRow("select * from EASI_CONF");
			if(confRow != null) {
				if(!hasFiled(confRow, "ORDER_INDEX")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_CONF ADD COLUMN ORDER_INDEX INT DEFAULT 0", null, result,
						"EASI_CONF表: 添加ORDER_INDEX字段成功<br>");
				}
				
				if(!hasFiled(confRow, "CONF_TITLE")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_CONF ADD COLUMN CONF_TITLE varchar(100)", null, result,
						"EASI_CONF表: 添加CONF_TITLE字段成功<br>");
				}
				
				if(!hasFiled(confRow, "CONF_DESC")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_CONF ADD COLUMN CONF_DESC varchar(500)", null, result,
						"EASI_CONF表: 添加CONF_DESC字段成功<br>");
				}
				
				if(!hasFiled(confRow, "CAN_DELETE")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_CONF ADD COLUMN CAN_DELETE INT", null, result,
						"EASI_CONF表: 添加CAN_DELETE字段成功<br>");
				}
				
				if(!hasFiled(confRow, "CONF_KEY")) {
					safeExecuteUpdate(query, "ALTER TABLE EASI_CONF ALTER COLUMN CONF_KEY NOT NULL", null, result, null);
				}
//				safeExecuteUpdate(query, "ALTER TABLE EASI_CONF ADD PRIMARY KEY(CONF_KEY)", null, result,"EASI_CONF表: 设置CONF_KEY为主键成功<br>");
			
				
				// 更新配置项
				setConf(query);
				result.append("EASI_CONF表: 更新配置项成功<br>");
				
				// 添加机器码
				if(addMachineCode(true)) {
					result.append("系统: 添加机器码成功<br>");
				}
			}
			isExcuteSql = true;
			logger.info(result.toString(), null);
			return result.length() > 0 ? result.toString() : "没有需要更新的字段";
		} catch(Exception e) {
			logger.error("数据库升级失败: " + e.getMessage(), e);
			throw e;
		}finally {
			ServerContext.reload();
		}
	}
	
	private void setConf(EasyQuery query){
		String[][] configs = {
			{"MQ_ADDR","MQ服务地址", "3"},
			{"verifyLogin","登录校验", "3"},
			{"hostBlankList","HOST白名单", "3"},
			{"firstLoginUpdatePwd","首次登录修改密码", "3"},
			{"G_LOGIN_AUTH","登录认证", "3"},
			{"updatePwdLimit","密码修改限制", "3"},
			{"unLockTime","解锁时间", "3"},
			{"SECURITY_ENTER", "安全入口", "1"}
		};
		for(String[] config : configs) {
			EasyRecord record = new EasyRecord("EASI_CONF", "CONF_KEY");
			record.set("CONF_KEY", config[0]);
			record.set("CONF_TITLE", config[1]);
			record.set("CAN_DELETE", Integer.parseInt(config[2]));
			try {
				if(!query.update(record)) {
				   query.save(record);
				}
			}catch (Exception e) {
				ConsoleUtils.getLogger().error("setConf失败: " + e.getMessage(), e);
			}
		}
	}
	
	private boolean hasFiled(EasyRow row, String filedName) {
		if(row==null) {
			return false;
		}
	    String[] fields = row.getColumnNames();
	    for (String field : fields) {
	        if (field.equalsIgnoreCase(filedName)) {
	            return true;
	        }
	    }
	    return false;
	}

}
