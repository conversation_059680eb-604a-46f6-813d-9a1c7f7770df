<%@ page language="java" contentType="text/html; charset=UTF-8"  pageEncoding="UTF-8"%>
 <div>
 	<blockquote class="layui-elem-quote">
		<a style="cursor: pointer;" onclick="logBak('')">立即备份</a>&nbsp;&nbsp;
		<span id="allSize" style="float: right;"></span>
	</blockquote>
	<fieldset class="layui-elem-field layui-field-title">
		<legend>应用日志</legend>
	</fieldset>
	
	<div class="layui-form" style="padding-left:15px">
		<table class="layui-table text-center" lay-even lay-size="sm">
			<thead>
				<tr>
					<th>序号</th>
					<th style="cursor: pointer;text-align: left;" title="排序" onclick="orderType('1')">日志名称<span style="float: right;font-size: 12px;">排序</span></th>
					<th style="cursor: pointer;" title="排序" onclick="orderType('2')">日志大小<span style="float: right;font-size: 12px;">排序</span></th>
					<th style="cursor: pointer;" title="排序" onclick="orderType('4')">创建时间<span style="float: right;font-size: 12px;">排序</span></th>
					<th style="cursor: pointer;" title="排序" onclick="orderType('5')">更新时间<span style="float: right;font-size: 12px;">排序</span></th>
					<th style="cursor: pointer;" title="排序" onclick="orderType('3')">创建间隔<span style="float: right;font-size: 12px;">排序</span></th>
					<th>操作</th>
				</tr>
			</thead>
			<tbody id="data-area">
		    </tbody>
		</table>
    </div>
</div>
<script id="list-template" type="text/x-jsrender">
				{{for}}
					<tr>
	                 <td>{{:#getIndex()+1}}</td>
	                 <td style="text-align:left;">{{:name}}</td>
	                 <td>{{call:size fn='sizeTostr'}}</td>
	                 <td>{{:createTime}}</td>
		                 <td>{{:updateTime}}</td>
		                 <td>{{:createInterval}}</td>
	                 <td>
						<a {{call:fn='showTailFn' name}} href = "${ctxPath}/servlet/pages?action=loggerTail&source=app&name={{:name}}" target="_blank">tail</a>
						<a href = "${ctxPath}/servlet/logger?action=SystemDownload&name={{:name}}" target="_blank">下载</a>
						<a onclick="logBak('{{:name}}')" href = "javascript:void(0);">备份</a>
					</td>
	               </tr>
				{{/for}}
</script>

  <!-- 额外的js -->
	<script type="text/javascript" >
	var currentOrderType = '';
	var currentOrderDirection = 'asc';

	$(function(){
		getData();
	});

	function orderType(orderType){
		// 如果点击的是同一列，切换排序方向
		if(currentOrderType === orderType) {
			currentOrderDirection = currentOrderDirection === 'asc' ? 'desc' : 'asc';
		} else {
			currentOrderType = orderType;
			currentOrderDirection = 'asc';
		}
		getData(orderType, currentOrderDirection);
	}

	function getData(orderType, orderDirection){
		if(orderType===undefined){orderType=''}
		if(orderDirection===undefined){orderDirection='asc'}
		ajax.remoteCall("${ctxPath}/servlet/logger?action=systemList", {orderType:orderType, orderDirection:orderDirection},function(result){
			if(result.state==1){
				//模板渲染数据
				var jsRenderTpl = $.templates("#list-template");
	          	var html = jsRenderTpl(result.data);
				$("#data-area").html(html);
				$('#allSize').text("日志总大小："+sizeTostr(result.allSize));
			}
		});
	}
	
	function isCompressedFilename(filename) {
	    // 正则表达式用于匹配压缩文件后缀，确保是最后一个扩展名
	    var compressedFileRegex = /\.(zip|tar\.gz|gz)$/i;
	    // 使用test方法检查文件名是否匹配正则表达式
	    return compressedFileRegex.test(filename);
	}
	
	function showTailFn(filename){
		if(isCompressedFilename(filename)){
			return "style='display:none'";
		}
		return '';
	}
	
	
	function logBak(name){
		layer.confirm('请勿在系统繁忙时段备份,是否继续',function(index){
			layer.close(index);
			ajax.remoteCall("${ctxPath}/servlet/logger?action=bakApp",{name:name}, function(result) {
				if(result.state==1){
					layer.msg('备份成功');
				}else{
					layer.alert(result.msg);
				}
			});
		});
	}
	
	function sizeTostr(size) {
		try{
		    var data = "";
		    size = Number(size);
		    if(size=='0'){
		    	data = 0 + "B";
		    }else if (size < 0.1 * 1024) { //如果小于0.1KB转化成B  
		        data = size.toFixed(2) + "B";
		    } else if (size < 0.1 * 1024 * 1024) {//如果小于0.1MB转化成KB  
		        data = (size / 1024).toFixed(2) + "KB";
		    } else if (size < 0.1 * 1024 * 1024 * 1024) { //如果小于0.1GB转化成MB  
		        data = (size / (1024 * 1024)).toFixed(2) + "MB";
		    } else { //其他转化成GB  
		        data = (size / (1024 * 1024 * 1024)).toFixed(2) + "GB";
		    }
		    var sizestr = data + "";
		    var len = sizestr.indexOf("\.");
		    var dec = sizestr.substr(len + 1, 2);
		    if (dec == "00") {//当小数点后为00时 去掉小数部分  
		        return sizestr.substring(0, len) + sizestr.substr(len + 3, 2);
		    }
		    return sizestr;
		}catch(e){
			console.error(e);
			return size;
		}
	}  

	</script>
