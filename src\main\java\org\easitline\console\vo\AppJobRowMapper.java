package org.easitline.console.vo;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.utils.lang.ResultSetUtil;
import org.easitline.console.base.Constants;

public class AppJobRowMapper implements EasyRowMapper<AppJobModel> {

	@Override
	public AppJobModel mapRow(ResultSet rs, int rowNum) {
		
		AppJobModel vo = new AppJobModel();
		
		try {
			
			vo.setJobId(ResultSetUtil.getString(rs, "JOB_ID", ""));
			vo.setJobName(ResultSetUtil.getString(rs, "JOB_NAME", ""));
			vo.setAppName(ResultSetUtil.getString(rs, "APP_NAME", ""));
			vo.setJobDesc(ResultSetUtil.getString(rs, "JOB_DESC", ""));
			vo.setJobType(ResultSetUtil.getString(rs, "JOB_TYPE", ""));
			//vo.setJobTypeName(Constants.JobType.getValue(rs.getInt("JOB_TYPE")));
			vo.setCron(ResultSetUtil.getString(rs, "CRON", ""));
			vo.setServerName(ResultSetUtil.getString(rs, "SERVER_NAME", ""));
			vo.setJobState(ResultSetUtil.getString(rs, "JOB_STATE", ""));
			vo.setJobStateName(Constants.JobState.getValue(rs.getInt("JOB_STATE")));
			vo.setServiceId(ResultSetUtil.getString(rs, "SERVICE_ID", ""));
			vo.setServiceName(ResultSetUtil.getString(rs, "SERVICE_NAME", ""));
			vo.setOkCount(ResultSetUtil.getString(rs, "OK_COUNT", ""));
			vo.setFailCount(ResultSetUtil.getString(rs, "FAIL_COUNT", ""));
			vo.setLastOkTime(ResultSetUtil.getString(rs, "LAST_OK_TIME", ""));
			vo.setLastFailTime(ResultSetUtil.getString(rs, "LAST_FAIL_TIME", ""));
			vo.setDependJobs(ResultSetUtil.getString(rs, "DEPEND_JOBS", ""));
			vo.setBeginDate(ResultSetUtil.getString(rs, "BEGIN_DATE", ""));
			vo.setEndDate(ResultSetUtil.getString(rs, "END_DATE", ""));
			vo.setBeginTime(ResultSetUtil.getString(rs, "BEGIN_TIME", ""));
			vo.setEndTime(ResultSetUtil.getString(rs, "END_TIME", ""));
			vo.setNextTime(ResultSetUtil.getString(rs, "NEXT_TIME", ""));
		
		} catch (SQLException ex) {
			ex.printStackTrace();
		}
		return vo;
		
	}

}
