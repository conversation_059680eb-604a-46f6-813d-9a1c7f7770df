package org.easitline.console.listener;

import javax.servlet.annotation.WebListener;
import javax.servlet.http.HttpSessionEvent;
import javax.servlet.http.HttpSessionListener;

import org.easitline.console.utils.LoginSessionUtils;

@WebListener
public class UserSessionListener implements HttpSessionListener {


	/**
	 * Session被创建时
     * @see HttpSessionListener#sessionCreated(HttpSessionEvent)
     */
    public void sessionCreated(HttpSessionEvent event)  { 
         System.out.println("创建用户session，SessionID:"+event.getSession().getId());
    }

	/**
	 * Session被删除时
     * @see HttpSessionListener#sessionDestroyed(HttpSessionEvent)
     */
    public void sessionDestroyed(HttpSessionEvent event)  { 
    	LoginSessionUtils.removeLoginInfo(event.getSession().getId());
    }
	
}
