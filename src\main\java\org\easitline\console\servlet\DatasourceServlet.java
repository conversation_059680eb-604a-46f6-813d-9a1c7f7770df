package org.easitline.console.servlet;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.EasyPool;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.console.base.ConsoleBaseServlet;
import org.easitline.console.service.ConfigCryptorService;
import org.easitline.console.utils.AesUtils;
import org.easitline.console.utils.ScriptHelper;

import com.alibaba.fastjson.JSONObject;

/**
 * 系统数据源管理类
 *
 */
@WebServlet("/servlet/datasource/*")
public class DatasourceServlet extends ConsoleBaseServlet {
	
	private static final long serialVersionUID = 1L;
	
	public void actionForReloadAll()throws Exception{
		if(!checkOperateTime().isOk()) {
			 renderJson(checkOperateTime());
			 return;
		}
		EasyPool.getInstance().update(null);
		this.addOperateLog("重载所有数据源","");
		renderText("重载成功！");
	}
	
	public EasyResult actionForUpdateAllPwd(){
		return ScriptHelper.getService().dbPwdEncrypt();
	}
	
	public EasyResult actionForUpdateDbField()throws Exception{
		return EasyResult.ok();
	}
	
	private EasyResult  verifyData(JSONObject params) {
		String[] keys = {"JDBC_URL","PASSWORD","USERNAME","DB_NAME"};
		for(String key:keys) {
			String value = params.getString(key);
			if(StringUtils.isNotBlank(value)) {
				if(value.contains("_curl")||value.contains("/etc")||value.contains("http:")) {
					this.error("DatasourceServlet.verifyData error:"+value,null);
					return EasyResult.fail("包含非法字符");
				}
			}
		}
		return EasyResult.ok();
	}
	
	/**
	 * 保存
	 * @throws Exception 
	 */
	public EasyResult actionForSave()throws Exception{
		try {
			if(!checkOperateTime().isOk()) {
				return checkOperateTime();
			}
			JSONObject params = getJSONObject();
			String data = params.getString("encryptStr");
			data = AesUtils.getInstance().decrypt(data);
			
			this.info("DatasourceServlet>actionForSave>data"+data, null);
			JSONObject jsonObject = JsonKit.getJSONObject(data,null);
			if(!checkData(jsonObject)) {
				return EasyResult.fail("数据格式不合法");
			}
			JSONObject dsInfo = JsonKit.getJSONObject(jsonObject, "ds");
			
			EasyQuery query = this.getConsoleQuery();
			String pkId = jsonObject.getString("pkId");
			
			EasyRecord record = new EasyRecord("EASI_DS_INFO","SYS_DS_NAME");
			record.setColumns(dsInfo);
			String driverName = record.getString("DRIVER_NAME");
			if(StringUtils.isNotBlank(driverName)) {
				record.set("DRIVER_CLASS",driverName);
			}
			record.set("PASSWORD",ConfigCryptorService.encryptString(jsonObject.getString("PASSWORD"), "DS"));
			
			EasyResult verifyResult = verifyData(record);
			if(!verifyResult.isOk()) {
				return verifyResult;
			}
			
			if(StringUtils.isBlank(pkId)) {
				//新增
				query.save(record);
				this.addOperateLog("新增数据源"+record.getString("SYS_DS_NAME"),record.toJSONString());
			}else {
				query.update(record);
				this.addOperateLog("修改数据源"+record.getString("SYS_DS_NAME"),record.toJSONString());
			}
			EasyPool.getInstance().update(record.getString("SYS_DS_NAME"));
			return EasyResult.ok(null,"保存成功");
		} catch (Exception ex) {
			this.error(ex.getMessage(), ex);
			getConsoleQuery().execute("ALTER TABLE EASI_DS_INFO ADD COLUMN URL_PROPERTIES  varchar(255)");
			getConsoleQuery().execute("ALTER TABLE EASI_DS_INFO ADD COLUMN JDBC_URL  varchar(1000)");
			return EasyResult.error(0, "请再重试提交。保存数据源失败，原因："+ex.getMessage());
		}
	}
	
	
	private boolean checkData(JSONObject params) {
		String[] fieldNames = {"pkId","ds.SYS_DS_NAME"};
		for(String name:fieldNames) {
			String value = params.getString(name);
			if(StringUtils.isNotBlank(value)) {
				if(value.indexOf("(")>-1||value.indexOf("\\")>-1||value.indexOf(":")>-1||value.indexOf("<")>-1||value.indexOf("&")>-1) {
					return false;
				}
			}
		}
		return true;
	}
	
	
	/**
	 * 删除
	 * @param req
	 * @param resp
	 * @throws ServletException
	 * @throws IOException
	 */
	public EasyResult actionForDelete() throws Exception{
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		String sql="delete from  EASI_DS_INFO  where  SYS_DS_NAME=?";
		JSONObject jsonObject = this.getJSONObject();
		if(!checkData(jsonObject)) {
			return EasyResult.fail("数据格式不合法");
		}
		Object[]  params = new Object[]{jsonObject.getString("pkId")};
		try {
			this.getConsoleQuery().execute(sql, params);
			EasyPool.getInstance().remove(jsonObject.getString("pkId")); //删除数据源
			this.addOperateLog("删除数据源"+jsonObject.getString("pkId"),jsonObject.toJSONString());
			return EasyResult.ok(null,"删除成功");
		} catch (SQLException ex) {
			this.error(ex.getMessage(), ex);
			return EasyResult.error(0,"删除数据源失败，原因："+ex.getMessage());
		}
	}
	
	/**
	 * @param req
	 * @param resp
	 * @throws ServletException
	 * @throws IOException
	 */
	public EasyResult actionForTest()throws Exception{
		JSONObject params = getJSONObject();
		String data = params.getString("encryptStr");
		data = AesUtils.getInstance().decrypt(data);
		this.info("DatasourceServlet>actionForTest>data"+data, null);
		
		JSONObject jsonObject2 = JsonKit.getJSONObject(data,null);
		
		JSONObject jsonObject = JsonKit.getJSONObject(jsonObject2, "ds");
		
		Connection conn = null;
		String url="";
		try {
			String driverName = "";
			String confType = jsonObject.getString("CONF_TYPE");
			String dbType = jsonObject.getString("DB_TYPE");
			String username = jsonObject.getString("USERNAME");
			String password = jsonObject2.getString("PASSWORD");
			String ip = jsonObject.getString("IP_ADDR");
			String port = jsonObject.getString("IP_PORT");
			String dbName = jsonObject.getString("DB_NAME");
			String dsProperties = jsonObject.getString("DS_PROPERTIES");
			String jdbcUrl = jsonObject.getString("JDBC_URL");
			
			EasyResult verifyResult = verifyData(jsonObject);
			if(!verifyResult.isOk()) {
				return verifyResult;
			}
			
			if(StringUtils.isNotBlank(dsProperties)){
				try {
					JSONObject.parseObject(dsProperties);
				} catch (Exception e) {
					this.error(e.getMessage(), e);
					return EasyResult.fail("连接池参数格式错误:"+e.getMessage());
				}
			}
			if("advanced".equalsIgnoreCase(confType)) {
				driverName = jsonObject.getString("DRIVER_NAME");
				url = jdbcUrl;
			}else {
				if (dbType.equals("MySql")) {
					driverName = "com.mysql.jdbc.Driver";
					url="jdbc:mysql://"+ip+":"+port+"/"+dbName+"?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Hongkong&useSSL=false";
				}else if (dbType.equals("MySql8")) {
					driverName = "com.mysql.cj.jdbc.Driver";
					url="jdbc:mysql://"+ip+":"+port+"/"+dbName+"?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Hongkong&useSSL=false";
				}else if (dbType.equals("Oracle")){
					driverName = "oracle.jdbc.driver.OracleDriver";
					if(ip.indexOf(".")==-1){
						url = "jdbc:oracle:oci8:@"+ip;
					}else if(ip.indexOf(",")>0){
						String[] ips = ip.split(",");
						url="********************************************= (address=(host="+ips[0]+") (protocol=tcp)(port="+port+"))(address=(host="+ips[1]+")(protocol=tcp) (port="+port+")) (load_balance=yes)(failover=yes))(connect_data=(service_name="+dbName+")))";
					}else{
						url="jdbc:oracle:thin:@//"+ip+":"+port+"/"+dbName;
					}
				}else if(dbType.equals("oscar")){
					driverName = "com.oscar.Driver";
					url="jdbc:oscar://"+ip+":"+port+"/"+dbName;
				}else if (dbType.equals("SqlServer")){
					driverName = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
					url="jdbc:sqlserver://"+ip+":"+port+";DatabaseName="+dbName;
				}else if (dbType.equals("Sybase")){
					driverName = "com.sybase.jdbc4.jdbc.SybDriver";
					url="jdbc:sybase:Tds:"+ip+":"+port+"/"+dbName;
				}else if(dbType.equals("Db2")){
					driverName = "com.ibm.db2.jcc.DB2Driver";
					url="jdbc:db2://"+ip+":"+port+"/"+dbName;
				}else if (dbType.equals("PostgreSql")){
					driverName = "org.postgresql.Driver";
					String[] ips = ip.split(",");
					String _ips = "";
					for(String str:ips){
						_ips = _ips+str+":"+port+",";
					}
					_ips = _ips.substring(0,_ips.length()-1);
					url="jdbc:postgresql://"+_ips+"/"+dbName+"?targetServerType=any&loadBalanceHosts=false";
				}else if (dbType.equals("OpenGauss")){
					driverName = "org.postgresql.Driver";
					url = "jdbc:postgresql://"+ip+":"+port+"/"+dbName;
				}else if (dbType.equals("dameng")){
					driverName = "dm.jdbc.driver.DmDriver";
					if(port.equals("0")){
						url="jdbc:dm://"+ip+"?schema="+dbName;
					}else{
						url="jdbc:dm://"+ip+":"+port+"?schema="+dbName;
					}
				}else if(dbType.equals("clickhouse")){
					driverName = "ru.yandex.clickhouse.ClickHouseDriver";
					url="jdbc:clickhouse://"+ip+":"+port+"/"+dbName;
				}
				if(ip.startsWith("jdbc")){
					url=ip;
				}
				if(StringUtils.isNotBlank(jdbcUrl)){
					url=jdbcUrl;
				}
				if(StringUtils.isBlank(url)) {
					return EasyResult.fail(dbType+">请选择高级模式");
				}
			}
			
			long startTime = System.currentTimeMillis();
			
			Properties props = new Properties();
            props.setProperty("user", username);
            props.setProperty("password", password);
            props.setProperty("connectTimeout", String.valueOf(10 * 1000));
            props.setProperty("socketTimeout", String.valueOf(10 * 1000));
			
			this.info("jdbcUrl:"+url+",driverName:"+driverName, null);
			
			Class.forName(driverName);
			DriverManager.setLoginTimeout(10);
//			conn = DriverManager.getConnection(url, props);
			conn = DriverManager.getConnection(url, username,password);
			
		    if (!conn.isValid(10)) {
			  return EasyResult.error(0, jdbcUrl+">连接失败");
		    }
		    
		    DatabaseMetaData metaData = conn.getMetaData();
            long endTime = System.currentTimeMillis();

            JSONObject result = new JSONObject();
            result.put("connected", true);
            result.put("databaseProductName", metaData.getDatabaseProductName());
            result.put("databaseProductVersion", metaData.getDatabaseProductVersion());
            result.put("driverName", metaData.getDriverName());
            result.put("driverVersion", metaData.getDriverVersion());
            result.put("url", metaData.getURL());
            result.put("userName", metaData.getUserName());
            result.put("connectionTime", endTime - startTime);
            result.put("message", "数据库连接成功");
            
			return EasyResult.ok(result,"连接成功！");
		} catch (Exception ex) {
			this.error(ex.getMessage(), ex);
			return EasyResult.error(0, url+">连接失败，原因："+ex.getMessage());
		}finally {
			if(conn!=null) {
				conn.close();
			}
		}
	}
	
	/**
	 * 初始化数据
	 * @throws ServletException
	 * @throws IOException
	 */
	public EasyResult actionForInit()throws Exception{
		JSONObject jsonObject = this.getJSONObject("ds");
		try {
			this.initData(jsonObject);
			return EasyResult.ok(null,"初始化数据源成功！");
		} catch (Exception ex) {
			error(ex.getMessage(), ex);	
			return EasyResult.error(0,"执行数据初始化失败，原因："+ex.getMessage());
		}
	}
	@Override
	protected String getResId() {
		return null;
	}
	public boolean initData(JSONObject jsonObject) throws Exception{
		
		String dbType = jsonObject.getString("DB_TYPE");
		String scriptFile = null;
		if (dbType.equals("MySql")) {
			scriptFile ="/org/easitline/console/dbscript/easitline_admin_mysql.sql";
		}else if (dbType.equals("Oracle")){
			scriptFile="/org/easitline/console/dbscript/easitline_admin_oracle.sql";
		}else if (dbType.equals("SqlServer")){
			scriptFile="/org/easitline/console/dbscript/easitline_admin_sqlserver.sql";
		}
		String[] sqls = {};
		try {
			InputStream in =  this.getClass().getResourceAsStream(scriptFile);
			java.io.ByteArrayOutputStream bos = new ByteArrayOutputStream();
		    int size = 0;
		    byte[] content = new byte[10240];
		    while(true){
		       size= in.read(content);
		       if(size ==  -1) break;
		       bos.write(content,0,size);
		    }
		    in.close();
		    byte[] script = bos.toByteArray();
			String sqlStr = new String(script,"UTF-8");
			sqls =  sqlStr.split(";");
		} catch (Exception ex) {
			throw new Exception("获取Easitline admin初始化脚本失败，请检查脚本文件[easitline_admin_mysql.sql]是否存在!",ex);
		}
		
		//执行数据库初始化操作
		for(String sql:sqls){
			try {
				this.info("执行初始化SQL："+sql, null);
			 	this.getQuery().execute(sql);
			} catch (Exception ex) {
				this.error(ex.getMessage(), ex);
			}
		}
		return true;
	}
	
	public void actionForConsole(){
		renderJsp("/pages/datasource/tools.jsp?dsName=consoleDs");
	}
	
	public EasyResult actionForQuerySchemFile(){
		JSONObject jsonObject = getJSONObject();
		String dsName=jsonObject.getString("dsName");
		ScriptHelper scriptHelper=new ScriptHelper(EasyQuery.getQuery(dsName));
		return EasyResult.ok(scriptHelper.getSchemaFileMap());
	}
	public EasyResult actionForMatchTable(){
		JSONObject jsonObject = getJSONObject();
		String dsName=jsonObject.getString("dsName");
		String dbName=jsonObject.getString("dbName");
		String path=jsonObject.getString("path");
		ScriptHelper scriptHelper=new ScriptHelper(EasyQuery.getQuery(dsName));
		JSONObject result=scriptHelper.matchTable(dbName, path);
		return EasyResult.ok(result);
	}
	
	
}
