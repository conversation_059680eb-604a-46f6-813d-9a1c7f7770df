package org.easitline.console.service;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.Globals;
import org.easitline.common.core.context.AppContext;
import org.easitline.console.base.Constants;
import org.easitline.console.deploy.vo.AppConfig;
import org.easitline.console.utils.ConsoleUtils;
import org.easitline.console.vo.ApplicationModel;
import org.easitline.console.vo.DictModel;
import org.easitline.console.vo.ResourceModel;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.sql.SQLException;
import java.util.List;

public class TomcatWarWatcher implements Runnable {
	
	private final Path autoDeployPath;
	private boolean running = true;
    private WatchService watchService;

    public TomcatWarWatcher(String autoDeployPath) {
        this.autoDeployPath = Paths.get(autoDeployPath);
    }

    public void stop() {
        running = false;
        if (watchService != null) {
            try {
                watchService.close();
            } catch (IOException e) {
                ConsoleUtils.getLogger().error("关闭监控服务时发生错误: " + e.getMessage(), e);
            }
        }
    }

    @Override
    public void run() {
        if (!Files.exists(autoDeployPath) || !Files.isDirectory(autoDeployPath)) {
            ConsoleUtils.getLogger().info("Tomcat autoDeployPath 目录不存在: " + autoDeployPath);
            try {
                Files.createDirectories(autoDeployPath);
            } catch (IOException e) {
                ConsoleUtils.getLogger().error("创建目录失败: " + e.getMessage(), e);
                return;
            } 
        }

        try {
            watchService = FileSystems.getDefault().newWatchService();
            autoDeployPath.register(watchService, StandardWatchEventKinds.ENTRY_CREATE, StandardWatchEventKinds.ENTRY_MODIFY);

            ConsoleUtils.getLogger().info("正在监控 Tomcat autoDeployPath 目录: " + autoDeployPath);

            while (running) {
            	Thread.sleep(2000);

            	WatchKey key = watchService.take(); // 阻塞等待事件
                for (WatchEvent<?> event : key.pollEvents()) {
                    WatchEvent.Kind<?> kind = event.kind();
                    Path filePath = (Path) event.context();
                    Path fullPath = autoDeployPath.resolve(filePath);

                    if(!filePath.toString().contains(Constants.EASITLINE_CONSOLE)){
                        if (Files.isRegularFile(fullPath) && filePath.toString().endsWith(".war")) {
                            if (kind == StandardWatchEventKinds.ENTRY_CREATE) {
                                ConsoleUtils.getLogger().info("检测到新 WAR 部署: "+autoDeployPath +":"+ filePath);
                                this.upgradeWar(filePath.toString());
                            } else if (kind == StandardWatchEventKinds.ENTRY_MODIFY) {
                                ConsoleUtils.getLogger().info("检测到 WAR 文件更新: " +autoDeployPath+":"+filePath);
                                this.upgradeWar(filePath.toString());
                            }
                        }
                    }else{
                        ConsoleUtils.getLogger().info("忽略Easitline Console相关文件: " + filePath);
                    }
                }

                boolean valid = key.reset();
                if (!valid) {
                    ConsoleUtils.getLogger().info("监控键失效，监控终止");
                    break;
                }
            }
        } catch (IOException | InterruptedException e) {
            ConsoleUtils.getLogger().error("监控过程中发生异常: " + e.getMessage(),e);
            Thread.currentThread().interrupt();
        }
    }
    
    private void upgradeWar(String warName) {
        ConsoleUtils.getLogger().info("开始部署war文件: " + autoDeployPath +">" + warName);
        try {
            // 创建war文件对象
            File warFile = new File(autoDeployPath.toString() + File.separator + warName);
            
            // 使用ApplicationService解析war包获取应用信息
            ApplicationModel appInfo = ApplicationService.getAppInfo(warFile, "META-INF/appinfo.xml");
            if (appInfo == null || StringUtils.isBlank(appInfo.getAppId())) {
                ConsoleUtils.getLogger().error("获取应用信息失败，无法识别的war包: " + warName);
                return;
            }
            
            String appId = appInfo.getAppId();
            
            boolean isAddWar = true;
            String sql = "select count(*) as tcount from EASI_APP_INFO where APP_ID = ?";
            if (Constants.getDb().queryForExist(sql, new Object[] { appId})) {
            	isAddWar = false;
            	ConsoleUtils.addOperateLog(null,"升级应用:"+autoDeployPath+">"+appInfo.getWarName()+"#"+appInfo.getAppVersion(),JSONObject.toJSONString(appInfo));
            	ConsoleUtils.getLogger().info("正在升级应用: "+autoDeployPath+">" + appInfo.getAppName() + " (" + appInfo.getAppId() + ") 版本: " + appInfo.getAppVersion());
            }else {
            	ConsoleUtils.addOperateLog(null,"部署应用"+autoDeployPath+">"+appInfo.getWarName()+"#"+appInfo.getAppVersion(),JSONObject.toJSONString(appInfo));
            	ConsoleUtils.getLogger().info("正在部署应用: "+autoDeployPath+">"+ appInfo.getAppName() + " (" + appInfo.getAppId() + ") 版本: " + appInfo.getAppVersion());
            }
            
            // 更新应用配置信息
            try {
            	if(isAddWar) {
            		ApplicationService.saveAppInfo(appInfo);
            	}
            	
                // 解析应用配置文件
                List<AppConfig> appConfigs = ApplicationService.getAppConfig(appInfo.getAppId(), appInfo.getAppVersion(), warFile, "META-INF/appconfig.xml");
                ConsoleUtils.getLogger().info("应用配置信息解析完成");
                
                // 更新配置信息
                try {
                    ApplicationService.updateAppConfig(appInfo, appConfigs);
                    ConsoleUtils.getLogger().info("应用配置信息更新完成");
                } catch (SQLException e) {
                    ConsoleUtils.getLogger().error("更新应用配置信息失败: " + e.getMessage(), e);
                }
                
                // 更新资源信息
                List<ResourceModel> appResources = ApplicationService.getResourceInfo(appInfo.getAppId(), appInfo.getAppVersion(), warFile, "META-INF/appresource.xml");
                ConsoleUtils.getLogger().info("应用资源信息解析完成");
                
                // 更新资源表
                try {
                    ResUpdateService.updateAppResource(appInfo, appResources);
                    ConsoleUtils.getLogger().info("应用资源信息更新完成");
                } catch (Exception e) {
                    ConsoleUtils.getLogger().error("更新应用资源信息失败: " + e.getMessage(), e);
                }
                
                // 更新数据字典
                List<DictModel> appDicts = ApplicationService.getDictInfo(appInfo.getAppId(), appInfo.getAppVersion(), warFile, "META-INF/appdict.xml");
                ConsoleUtils.getLogger().info("应用数据字典信息解析完成");
                
                // 更新数据字典表
                try {
                    ApplicationService.updateAppDict(appDicts);
                    ConsoleUtils.getLogger().info("应用数据字典信息更新完成");
                } catch (SQLException e) {
                    ConsoleUtils.getLogger().error("更新应用数据字典信息失败: " + e.getMessage(), e);
                }
                
                
                // 备份war包
                ApplicationService.addHis(warFile, appInfo);
                
                // 处理.deployed文件
                String deployedFileName = warName.replace(".war",".deployed");
                File deployedFile = new java.io.File(deployDir() + File.separator + deployedFileName);
                if(deployedFile.exists()) {
                    deployedFile.delete();
                    Thread.sleep(1000);
                } else {
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException e) {
                        ConsoleUtils.getLogger().error(e.getMessage(), e);
                    }
                }
                
                // 复制war到目标目录
                String destPath = deployDir() + File.separator + warName;
                File destFile = new java.io.File(destPath);
                if(destFile.isFile()) {
                    destFile.delete();
                }
                ConsoleUtils.getLogger().info("开始更新WAR应用目标文件：" + destFile);
                org.apache.commons.io.FileUtils.copyFile(warFile, destFile);
                
                if(!isAddWar) {
                	// 更新应用信息记录
                	ApplicationService.updateAppInfo(appInfo, destFile);
                }
                // 重新加载应用上下文
                AppContext.getContext(appId, true);
                
                ConsoleUtils.getLogger().info("应用 " + appInfo.getAppName() + " (" + appInfo.getAppId() + ") 升级完成");
                
                // 删除autoDeploy目录下的原始war文件
                if (warFile.exists()) {
                    if (warFile.delete()) {
                        ConsoleUtils.getLogger().info("已删除autoDeploy目录下的war文件: " + warFile.getName());
                    } else {
                        ConsoleUtils.getLogger().warn("无法删除autoDeploy目录下的war文件: " + warFile.getName());
                    }
                }
            } catch (Exception e) {
                ConsoleUtils.getLogger().error("应用升级过程中发生异常: " + e.getMessage(), e);
            }
            
        } catch (Exception e) {
            ConsoleUtils.getLogger().error("升级应用失败: " + e.getMessage(), e);
        }
    }
    
    private String deployDir() {
        return System.getProperty("deployDir", Globals.WEBAPPS_DIR);
    }
}
