package org.easitline.console.listener;

import java.util.ArrayList;
import java.util.List;
import javax.servlet.annotation.WebListener;
import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceContextListener;

@WebListener
public class NotifyContextListener extends ServiceContextListener {

	@Override
	protected List<ServiceResource> serviceResourceCatalog() {
		List<ServiceResource> list = new ArrayList<ServiceResource>();
		ServiceResource  resource = new ServiceResource();
		resource.appName     =  "easitline-console"; //服务所在的WAR应用名   
		resource.className   =  "org.easitline.console.service.MarsAlarmService";//服务实现类，类必须实现IService接口
		resource.description =  "服务参数：{\"eventCode\":\"事件代码，格式：平台名称-错误代码，NGSP-XXXXX,MARS-XXXXX,YC-XXXXX,AI-XXXXX,CC-XXXXX，针对平台级别的错误代码，提供统一的错误说明和描述\",\"exception\":\"异常信息，用于定位问题的详细日志，如：JAVA的堆栈日志 JSONObject.toJSONString(ex)\",\"eventLevel\":\"事件级别，取值：warn 告警、 fatal 致命\",\"eventDesc\":\"告警详细描述，可包括接口地址、参数等，便于定位问题的说明。\",\"appName\":\"应用名称，war包名字，如：smsgw.war\",\"moduleName\":\"业务名称，如：短信网关\",\"eventName\":\"事件标题，字数不能超过30个字，如：微信中控服务层ActiveMq队列重连告警\"}";//服务描述
		resource.serviceId   =  "MARS-ALARM-SERVICE";//服务ID，必须唯一，服务是通过serviceId继续查找并调用
		resource.serviceName =  "MARS告警服务";//服务名称
		list.add(resource);
		
		
		ServiceResource  resource1 = new ServiceResource();
		resource1.appName     =  "easitline-console"; //服务所在的WAR应用名   
		resource1.className   =  "org.easitline.console.service.MarsLogService";//服务实现类，类必须实现IService接口
		resource1.description =  "MARS日志收集服务，主要用于收集在线、话务及接口相关的系统日志。";//服务描述
		resource1.serviceId   =  "MARS-LOG-SERVICE";//服务ID，必须唯一，服务是通过serviceId继续查找并调用
		resource1.serviceName =  "MARS日志收集服务";//服务名称
		list.add(resource1);
		return list;
	}

}
