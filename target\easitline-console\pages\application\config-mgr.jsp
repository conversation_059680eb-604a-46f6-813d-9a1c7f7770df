<%@page pageEncoding="UTF-8"%>
<style>
	#config-table {
		height: calc(100vh - 220px);
		overflow-y: scroll;
		display: block;
		width: 100%;
	}
</style>
<div>
	<blockquote class="layui-elem-quote">
		动态修改应用的配置信息，修改完成后立即生效无需重启，注意：修改应用的配置信息有可能直接影响应用的正常运行，请谨慎修改！
		<br><span>配置支持类型：string,select,radio,textarea,json</span>
	</blockquote>

	<form class="layui-form" id="easyform" action="" lay-filter="form-val-filter" autocomplete="off">
		<table  class="layui-table text-l">
			<tr>
				<td>应用名称</td>
				<td>
					<input name="appName" class="layui-input"/>
				</td>
				<td>配置项</td>
				<td>
					<input name="itemName" placeholder="比如redis,助手" class="layui-input"/>
				</td>
				<td>配置项Key</td>
				<td>
					<input name="itemKey" class="layui-input"/>
				</td>
				<td>
					<button type="button" onclick="searchData();" class="layui-btn layui-btn-small">搜索</button>
				</td>
			</tr>
		</table>
		<div id="pageContainer"></div>
  		<table id="config-table" class="layui-table text-l">
  			<thead>
	  			<tr>
	  				<th>序号</th>
	  				<th>所属应用</th>
	  				<th>配置项</th>
	  				<th>配置项KEY</th>
	  				<th>配置值</th>
	  				<th>操作</th>
	  				<th>配置描述</th>
	  			</tr>
  			</thead>
  			<tbody id="dataList" data-mars="app.appConfList"></tbody>
  		</table>
	  <br><br><br>
	  <script id="list-template" type="text/x-jsrender">
		 {{for list}}
			{{if P_ITEM_KEY =='1'}}
				<tr>
				 <td colspan="7">
					<fieldset class="layui-elem-field layui-field-title">
						<legend style="text-align:center;">{{:ITEM_NAME}} {{if DESCRIPTION}}_{{:DESCRIPTION}}{{/if}}</legend>
					</fieldset>
				 </td>
				</tr>
			{{/if}}	
			<tr>
			   <td>{{:#index+1}}</td>	
			   <td style="min-width:160px;">{{:APP_NAME}}-{{:APP_ID}}</td>		
			   <td style="min-width:100px;">{{:ITEM_NAME}}</td>		
			   <td style="min-width:100px;">{{:ITEM_KEY}}</td>		
			   <td style="min-width:400px;" title="最近修改{{:UPDATE_TIME}}">
 					{{if ITEM_TYPE=='radio'&&JSON_STRING}}
			      		{{call: fn='renderRadio'}}
				    {{else ITEM_TYPE=='select'&&JSON_STRING}}
			      		{{call: fn='renderSelect'}}
				    {{else ITEM_TYPE=='number'}}
						<input type="number" name="conf_{{:APP_ID}}_{{:ITEM_KEY}}" value="{{:ITEM_VALUE}}" placeholder="{{:ITEM_NAME}}" class="layui-input">
				    {{else ITEM_TYPE=='textarea'}}
						<textarea style="min-height:60px;" name="conf_{{:APP_ID}}_{{:ITEM_KEY}}" placeholder="{{:ITEM_NAME}}" class="layui-textarea">{{:ITEM_VALUE}}</textarea>
				    {{else ITEM_TYPE=='json'}}
						<textarea style="min-height:60px;" readonly="readonly" name="conf_{{:APP_ID}}_{{:ITEM_KEY}}" placeholder="{{:ITEM_NAME}}" class="layui-textarea">{{:ITEM_VALUE}}</textarea>
				    	<button type="button" class="layui-btn layui-btn-xs layui-btn-warm mt-5" onclick="jsonConf(this)">配置json</button>
					{{else}}
						<input type="text" name="conf_{{:APP_ID}}_{{:ITEM_KEY}}" value="{{:ITEM_VALUE}}" placeholder="{{:ITEM_NAME}}" class="layui-input">
				    {{/if}}
				</td>		
			    <td>
					<button type="button" onclick="updateRowData('{{:APP_ID}}','{{:ITEM_KEY}}','{{:ITEM_VALUE}}');" class="layui-btn layui-btn-xs  layui-btn-primary">保存</button>
				</td>		
			    <td>
					<div class="layui-form-mid layui-word-aux">{{:ITEM_DESC}}</div>
				</td>		
		  </tr>
		{{/for}}
	  </script>
	</form>
</div>
<script type="text/javascript">

	$(function(){		
		initTable({});		
	});
	
	function initTable(data){
		var reqData = {pageType:3,pageIndex:1,pageSize:30,isFirstLoad:true};
		var _data = $.extend({},reqData,data);
		
		$("#easyform").render({data:_data,success:function(result){
			var appConfList = result["app.appConfList"];
			if(_data.isFirstLoad){
				  layui.use(['laypage'], function(){
					  var laypage = layui.laypage;
					  laypage.render({
					    elem: 'pageContainer',
					    limit:30,
						limits:[10,30,50,100,200],
					    count: appConfList.totalRow,
					    layout:['prev','page','next','limit','count'],
					    jump:function(obj, first){
					        if(!first){
								var newJson = $.extend({},{pageIndex:obj.curr,pageType:3,pageSize:obj.limit,isFirstLoad:false});
								initTable(newJson);
					        }
					    }
					});
				  });
			}
			layui.use(['form'], function(){
		         var layuiform = layui.form;
		         layuiform.render('select');
		         layuiform.render('radio'); 
		     });
    	}});
	}
	
	function searchData(){
		var data = form.getJSONObject("easyform");
		initTable({data:data});
	}

	var oldValues = {};
	 
	function updateRowData(appId,itemKey,itemValue){
		var url = '${ctxPath}/servlet/application?action=updateConfigRow';
		if(oldValues[appId+"_"+itemKey]){
			itemValue = oldValues[appId+"_"+itemKey];
		}
		var newValue = '';
		var data = {appId:appId,itemKey:itemKey,itemValue:itemValue,beforeItemValue:itemValue};
		var el = $('input[name="conf_'+appId+'_'+itemKey+'"]');
		if(el.attr('type')=='radio'){
			newValue =  $('input[name="conf_'+appId+'_'+itemKey+'"]:checked').val();
		}else{
			newValue = el.val();
		}
		if(newValue == itemValue){
			layer.msg("未修改配置信息",{icon: 2,time:800});
			return;
		}
		data['itemValue'] = newValue;
		ajax.remoteCall(url,data, function(result) {
			if(result.state == 1){
				oldValues[appId+"_"+itemKey] = newValue;
				layer.msg(result.msg,{icon: 1,time:800});
			}else{
				layer.alert(result.msg);
			}
		})	
	}
	
	String.prototype.replaceAll = function(s1,s2){ 
		return this.replace(new RegExp(s1,"gm"),s2); 
	}
	
	function renderRadio(row){
		try{
			var v1=row['JSON_STRING'];
			var v2=row['ITEM_KEY'];
			var v3=row['ITEM_VALUE'];
			var v4=row['ITEM_NAME'];
			var v5=row['APP_ID'];
			var html="";
			v1=v1.replaceAll("{","");
			v1=v1.replaceAll("}","");
			var array=v1.split(",");
			for(var index in array){
				var item=array[index];
				var itemArray=item.split(":");
				var val=itemArray[0];
				var checked="";
				if(val==v3)checked="checked";
				if(itemArray.length==1){
					html+="<input type='radio' name='conf_"+v5+"_"+v2+"' title='"+itemArray[0]+"' "+checked+" value='"+itemArray[0]+"'/>";
				}
				if(itemArray.length==2){
					html+="<input type='radio' name='conf_"+v5+"_"+v2+"' title='"+itemArray[1]+"' "+checked+" value='"+itemArray[0]+"'/>";
				}
			}
			return html;
		}catch(error){
			console.error(error);
	        return '<input type="number" name="conf_'+v5+'_'+v2+'" value="'+v3+'" placeholder="'+v4+'" class="layui-input">';
		}
	}
	
   function renderSelect(row){
		var v1=row['JSON_STRING'];
		var v2=row['ITEM_KEY'];
		var v3=row['ITEM_VALUE'];
		var v4=row['ITEM_NAME'];
		var v5=row['APP_ID'];
		var html="<select name='conf_"+v2+"'>";
		v1=v1.replaceAll("{","");
		v1=v1.replaceAll("}","");
		var array=v1.split(",");
	   try{
			for(var index in array){
				var item=array[index];
				var itemArray=item.split(":");
				var val=itemArray[0];
				var selected="";
				if(val==v3)selected="selected";
				if(itemArray.length==1){
					html+="<option "+selected+" value='"+itemArray[0]+"'>"+itemArray[0]+"</option>";
				}
				if(itemArray.length==2){
					html+="<option "+selected+" value='"+itemArray[0]+"'>"+itemArray[1]+"</option>";
				}
			}
			html+="</select>";
			return html;
		}catch(error){
			console.error(error);
	        return '<input type="number" name="conf_'+v5+'_'+v2+'" value="'+v3+'" placeholder="'+v4+'" class="layui-input">';
		}
   }
   
	function jsonConf(el){
		var jsonStr = $(el).prev().val();
		var html = [];
		var json = eval("("+jsonStr+")");
		html.push("<table id='param-config-json' class='layui-table'><thead><tr><th style='width:45%;'>键</th><th>值</th></tr></thead>");
		for(var key in json){
			html.push("<tr><td>"+key+"</td>");
			html.push("<td><input class='layui-input' name='"+key+"' value='"+json[key]+"'></td></tr>");
		}
		html.push("</table>");
		layer.open({id:'jsonConf',title:'修改配置',content:html.join(''),area:['500px','500px'],yes:function(index){
			var postdata = form.getJSONObject("#param-config-json");
			$(el).prev().val(JSON.stringify(postdata));
			layer.close(index);
		}});
	}
	
</script>

