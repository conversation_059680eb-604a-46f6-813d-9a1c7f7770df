package org.easitline.console.monitor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.impl.RedisImpl;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.console.service.MarsAlarmService;
import org.easitline.console.vo.ServerInfoModel;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;

import java.util.HashSet;
import java.util.Set;


public class MarsStatService {

	private static MarsStatService service = new MarsStatService();
	
	private static Logger logger = LogEngine.getLogger("easitline-console","easitline-alarm");
	
	private  int  dateId =  0 ;
	
	private static Set<String> dayAlarmCodeSet = new HashSet<String>();
	
	public static  MarsStatService getInstance(){
		return service;
	}
	
	public String  servcie() throws Exception{
		
		EasyCalendar cal = EasyCalendar.newInstance();
		int _dateId = cal.getDateInt();
		
		if(_dateId != dateId){
			dayAlarmCodeSet.clear();
			dateId = _dateId;
		}
		
		logger.info("run MarsAlarmService.checkRedis() ...");
		
		MarsAlarmService.checkRedis();
		
		JSONObject json = new JSONObject();
		ServerInfoModel serverInfo = new ServerInfoModel();
		String serverString = JSON.toJSONString(serverInfo);
		JSONObject serverObj = JSONObject.parseObject(serverString);
		serverObj.put("serverName", ServerContext.getServerName());
		serverObj.put("nodeName", ServerContext.getNodeName());
		serverObj.put("redisAddr", ServerContext.getCacheAddr());
		serverObj.put("MQAddr", ServerContext.getProperties("MQ_ADDR",""));
		serverObj.put("redisState", 0);
		serverObj.put("MQState", 0);
		String nodeId = ServerContext.getNodeName()+"["+serverInfo.getAddress()+"]";
		serverObj.put("nodeId", nodeId); 
		json.put("server", serverObj);
		
		//mars配置内存过少告警
		this.doMarsConfMemAlarm();
		
		json.put("updateTime", EasyCalendar.newInstance().getDateTime("-"));
		json.put("timestamp", System.currentTimeMillis());
//		logger.info("#mars->"+json);
		hset("#mars",nodeId,json.toJSONString());
		return json.toJSONString();
	}
	
	
	
	/**
	 * JVM配置内存过少告警
	 */
	public  void doMarsConfMemAlarm(){
		try {
			if(dayAlarmCodeSet.contains("MARS-00016")) return;
			ServerInfoModel  serverInfo = new ServerInfoModel();
			long   marsConfMaxMemory = Runtime.getRuntime().maxMemory();
			double marsMem = marsConfMaxMemory/1024/1024;
			long totalMem =  serverInfo.getTotalMemoryForLong();
			//总内存少于10不做告警处理。
			if(totalMem < 10) return;
			double per = marsMem/totalMem;
			if(per<0.4){
				dayAlarmCodeSet.add("MARS-00016");
				MarsAlarm.memoryAlarm(serverInfo.getTotalMemory(), serverInfo.getMaxMemory());
			}
		} catch (Exception ex) {
			logger.error(ex,ex);
		}
	}
	
	
	/**
	 * mars进程线程数超过3000
	 */
	public static void doThreadAlarm(){
		ServerInfoModel  serverInfo = new ServerInfoModel();
		try{
			int threadCount = serverInfo.getTotalThread();
			if(threadCount >3000){
				MarsAlarm.threadAlarm(threadCount);
			}
		}catch (Exception ex) {
			logger.error(ex,ex);
		}
	}
	
	
	public static void  hset(String key,String item,String value){
		RedisImpl redis = (RedisImpl)CacheManager.getMemcache();
		Jedis jedis = redis.getJedis();
		if(jedis !=null){
			try {
				jedis.hset(key,item,value);
			} catch (Exception ex) {
				ex.printStackTrace();
			}finally{
				if(jedis!=null) jedis.close();
			}
		}else{
			JedisCluster cluster = redis.getCluster();
			cluster.hset(key,item,value);
		}
	}
	
	
}
