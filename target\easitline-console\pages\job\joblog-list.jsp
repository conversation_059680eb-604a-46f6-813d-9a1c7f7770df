

<%@page pageEncoding="UTF-8"%>
	<form id="easyform-job-log">
		<input type="hidden" name="jobId" value="${param.jobId}">
		<div class="layui-form" style="padding: 0 15px;">
			<table class="layui-table text-center" data-mars="job.jobLogList" data-container="#jobLog-data" data-template="jobLog-template">
				<thead>
					<tr>
						<th>调度日期</th>
	                 	<th>最新状态</th>
	                 	<th>首次调度时间</th>
	                 	<th>最后执行时间</th>
	                 	<th>总执行次数</th>
	                 	<th>最新异常信息</th>
					</tr>
				</thead>
				<tbody id="jobLog-data">
			    </tbody>
			</table>
	    </div>
	</form> 
<script id="jobLog-template" type="text/x-jsrender">
				{{for list}}
				<tr>
					<td class="td-textcut">{{:dateId}}</a></td>
					<td class="td-textcut">{{:jobState}}</td>
					<td class="td-textcut">{{:firstTime}}</td>
					<td class="td-textcut">{{:lastTime}}</td>
					<td class="td-textcut">{{:execCount}}</td>
					<td>{{:exceptionInfo}}</td>
				</tr>
				{{/for}}
</script>
<script type="text/javascript" >
	$(function(){
		$("#easyform-job-log").render();
	});

</script>