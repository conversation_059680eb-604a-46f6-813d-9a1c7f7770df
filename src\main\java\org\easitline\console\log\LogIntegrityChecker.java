package org.easitline.console.log;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.SQLException;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.console.base.Constants;

import com.alibaba.fastjson.JSONObject;

public class LogIntegrityChecker {
    private static final Map<String, String> FILE_HASH_MAP = new ConcurrentHashMap<>();
    private static ScheduledExecutorService scheduler;
    private static final Object lock = new Object();
    
    /**
     * 初始化日志完整性检查器
     */
    public static void init() {
        try {
            // 确保不会重复初始化
            if (scheduler != null && !scheduler.isShutdown()) {
                return;
            }
            
            // 初始化调度器
            scheduler = Executors.newScheduledThreadPool(1);
            
            // 初始化内存缓存
            initializeHashMap();
            
            // 每小时检查一次文件完整性
            scheduler.scheduleAtFixedRate(() -> {
                try {
                    checkAllFilesIntegrity();
                } catch (Exception e) {
                    LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("文件完整性检查失败", e);
                }
            }, 1, 1, TimeUnit.HOURS);
            
            // 每天清理一次已删除文件的记录
            scheduler.scheduleAtFixedRate(() -> {
                try {
                    cleanupDeletedFiles();
                } catch (Exception e) {
                    LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("清理已删除文件记录失败", e);
                }
            }, 1, 24, TimeUnit.HOURS);
            
        } catch (Exception e) {
            LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("初始化日志完整性检查器失败", e);
        }
    }

    /**
     * 销毁日志完整性检查器
     */
    public static void destroy() {
        try {
            if (scheduler != null && !scheduler.isShutdown()) {
                // 尝试优雅关闭调度器
                scheduler.shutdown();
                if (!scheduler.awaitTermination(60, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
                scheduler = null;  // 清空引用
            }
            
            // 清理内存缓存
            synchronized (lock) {
                FILE_HASH_MAP.clear();
            }
            
            LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info("日志完整性检查器已关闭");
        } catch (InterruptedException e) {
            // 如果等待被中断，则强制关闭
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
            LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("关闭日志完整性检查器时被中断", e);
        } catch (Exception e) {
            LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("关闭日志完整性检查器失败", e);
        }
    }

    /**
     * 检查所有文件的完整性
     */
    private static void checkAllFilesIntegrity() {
        FILE_HASH_MAP.keySet().forEach(filePath -> {
            if (isFileTampered(filePath)) {
                LogEngine.getLogger(Constants.EASITLINE_CONSOLE).warn("检测到文件被篡改: " + filePath);
            }
        });
    }

    /**
     * 从数据库加载哈希值到内存
     */
    public static void initializeHashMap() {
        try {
            String sql = "SELECT FILE_PATH, HASH_VALUE FROM EASI_LOG_HASH";
            List<EasyRow> rows = Constants.getDb().queryForList(sql);
            
            synchronized (lock) {
                FILE_HASH_MAP.clear();
                for (EasyRow row : rows) {
                    FILE_HASH_MAP.put(row.getColumnValue("FILE_PATH"),row.getColumnValue("HASH_VALUE")
                 );
                }
            }
        } catch (SQLException e) {
            LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("初始化哈希值缓存失败", e);
        }
    }

    /**
     * 保存或更新日志文件哈希值
     */
    public static String saveOrUpdateLogHash(String filePath) {
        String hash = calculateFileHash(filePath);
        if (hash != null) {
            synchronized (lock) {
                String previousHash = FILE_HASH_MAP.get(filePath);
                
                // 更新内存缓存
                FILE_HASH_MAP.put(filePath, hash);
                
                try {
                    // 更新数据库
                    EasyRecord record = new EasyRecord("EASI_LOG_HASH", "FILE_PATH");
                    record.setPrimaryValues(filePath);
                    record.set("HASH_VALUE", hash);
                    record.set("LAST_MODIFIED", EasyDate.getCurrentDateString());
                    
                    if (previousHash != null) {
                        Constants.getDb().update(record);
                    } else {
                        Constants.getDb().save(record);
                    }
                    
                    // 如果哈希值发生变化,记录历史
                    if (previousHash != null && !previousHash.equals(hash)) {
                        LogEngine.getLogger(Constants.EASITLINE_CONSOLE).warn("日志文件内容已更改: " + filePath);
                            
                        // 保存历史记录
                        EasyRecord historyRecord = new EasyRecord("EASI_LOG_HASH_HIS");
                        historyRecord.set("FILE_PATH", filePath);
                        historyRecord.set("HASH_VALUE", previousHash);
                        historyRecord.set("LAST_MODIFIED", EasyDate.getCurrentDateString());
                        historyRecord.set("CREATED_AT", EasyDate.getCurrentDateString());
                        Constants.getDb().save(historyRecord);
                    }
                    
                } catch (SQLException e) {
                    // 数据库操作失败,回滚内存缓存
                    if (previousHash != null) {
                        FILE_HASH_MAP.put(filePath, previousHash);
                    } else {
                        FILE_HASH_MAP.remove(filePath);
                    }
                    LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("更新哈希值失败: " + filePath, e);
                    return null;
                }
            }
        }
        return hash;
    }

    /**
     * 检查文件是否被篡改
     */
    public static boolean isFileTampered(String filePath) {
    	if(!Constants.fileTamperedCheck()) {
    		return false;
    	}
        String currentHash = calculateFileHash(filePath);
        String storedHash = FILE_HASH_MAP.get(filePath);
        
        if (currentHash == null || storedHash == null) {
            return false;
        }
        
        // 如果内存中的哈希值与数据库不一致,重新加载
        try {
            String sql = "SELECT HASH_VALUE FROM EASI_LOG_HASH WHERE FILE_PATH = ?";
            String dbHash = Constants.getDb().queryForString(sql, filePath);
            if (!storedHash.equals(dbHash)) {
                synchronized (lock) {
                    FILE_HASH_MAP.put(filePath, dbHash);
                }
                storedHash = dbHash;
            }
        } catch (SQLException e) {
            LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("获取数据库哈希值失败: " + filePath, e);
        }
        
        return !currentHash.equals(storedHash);
    }

    /**
     * 清理已删除文件的哈希值
     */
    public static void cleanupDeletedFiles() {
        Set<String> filesToRemove = new HashSet<>();
        
        // 检查哪些文件已被删除
        FILE_HASH_MAP.keySet().forEach(filePath -> {
            if (!Files.exists(Paths.get(filePath))) {
                filesToRemove.add(filePath);
            }
        });
        
        // 批量删除
        synchronized (lock) {
            for (String filePath : filesToRemove) {
                try {
                    Constants.getDb().executeUpdate("DELETE FROM EASI_LOG_HASH WHERE FILE_PATH = ?",filePath);
                    FILE_HASH_MAP.remove(filePath);
                } catch (SQLException e) {
                    LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("删除哈希值记录失败: " + filePath, e);
                }
            }
        }
    }

    /**
     * 计算文件的SHA-256哈希值
     * @param filePath 文件路径
     * @return 文件哈希值
     */
    public static String calculateFileHash(String filePath) {
        try {
            Path path = Paths.get(filePath);
            byte[] fileBytes = Files.readAllBytes(path);
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(fileBytes);
            String hash = bytesToHex(hashBytes);
            
            JSONObject params = new JSONObject();
            params.put("type", "hmac");
            params.put("data", hash);
            try {
				IService iService = ServiceContext.getService("JY_SECRET_SERVICE");
				if (iService!=null) {
                    JSONObject result = iService.invoke(params);
                    if(result!=null && result.containsKey("result")&&"200".equals(result.getString("code"))){
                        hash = result.getString("result");
                    }else{
                        LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("JY_SECRET_SERVICE服务调用失败: " + result);
                    }
                }else{
                    LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("JY_SECRET_SERVICE服务未找到");
                }
			} catch (ServiceException e) {
                LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("计算文件哈希失败: " + filePath, e);
			}
            
            return hash;
        } catch (IOException | NoSuchAlgorithmException e) {
            LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("计算文件哈希失败: " + filePath, e);
            return null;
        }
    }

    /**
     * 将字节数组转换为十六进制字符串
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
}
