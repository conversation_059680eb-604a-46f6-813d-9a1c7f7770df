<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://www.easitline.org/easitline-taglib" prefix="EasyTag"%>
<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<title>Easitline-Console  ${platform}</title>
		 <LINK rel="Shortcut Icon" href="${ctxPath}/favicon.ico" />
		<link rel="stylesheet" href="${staticPath}/lib/layui/css/layui.css">
		<link rel="stylesheet" href="${ctxPath}/static/css/site.css">
		<script type="text/javascript" src="${staticPath}/js/jquery.min.js"></script> 
		<style>
		   @keyframes blink {
			  0% { opacity: 1; }
			  50% { opacity: 0; }
			  100% { opacity: 1; }
			}
		   .profileActiveEl{animation: blink 2s infinite;padding: 0px 10px;}
		   /* 移动端 */
		    @media screen and (max-width: 768px) {
		      .layui-layout-admin .layui-layout-left,
		      .layui-layout-admin .layui-body,
		      .layui-layout-admin .layui-footer{left: 0;}
		      .layui-layout-admin .layui-side{left: -300px;}
		    }
		  </style>
	</head>
	<body class="${profileActive}">
		<div class="layui-layout layui-layout-admin">
			<div class="layui-header">
				<div class="layui-logo layui-hide-xs"><a href="${ctxPath}/index"><img src="${ctxPath}/static/images/logo.png" alt="Easitline管理控制台" style="width:200px"></a></div>
				 <ul class="layui-nav layui-layout-left">
			      <!-- 移动端显示 -->
			      <li class="layui-nav-item layui-show-xs-inline-block layui-hide-sm" lay-header-event="menuLeft">
			        <i class="layui-icon layui-icon-spread-left"></i>
			      </li>
			      <li class="layui-nav-item">${platform}</li>
			      <li class="layui-nav-item"><span style="padding: 0px 10px;">${versionInfo}</span></li>
			      <li class="layui-nav-item"><div class="ml-10 profileActiveEl">${profileActive}</div> </li>
			      <li class="layui-nav-item"><div class="ml-10"><c:if test="${!empty consoleUpdateConfTime}">系统维护时间：${consoleUpdateConfTime}时</c:if></div> </li>
			      <li class="layui-nav-item node-ul">
			        <a href="javascript:;">选择节点</a>
			        <dl class="layui-nav-child nodeList">
			        </dl>
			      </li>
			    </ul>
				<ul class="layui-nav  layui-layout-right">
					 <!-- 移动端显示 -->
			      	<li class="layui-nav-item layui-show-xs-inline-block layui-hide-sm" lay-header-event="menuLeft">
			        	<i class="layui-icon layui-icon-spread-left"></i>
      				</li>
					<li class="layui-nav-item layui-hide-xs">
				        <a href="javascript:;">欢迎您，${userAcct}</a>
				        <dl class="layui-nav-child">
				          <dd><a  href="${ctxPath}/index">首页</a></dd>
				          <dd><a href="javascript:void(0)" onclick="loadPage('server/manager-info.jsp',{})">修改密码</a></dd>
				          <dd><a href="${ctxPath}/login?action=logout">登出</a></dd>
				        </dl>
				     </li>
				</ul>
			</div>
			<div class="layui-side layui-bg-black">
				<div class="layui-side-scroll">
					<ul class="layui-nav layui-nav-tree" lay-filter="test">
						
					</ul>
				</div>
			</div>

			<div class="layui-body">
				<!-- 内容主体区域 -->
				<div  id="layui-main" class="layui-main">
					<c:if test="${not empty page}">
						<jsp:include page="${page}"  flush="true" />
					</c:if>
				</div>
				<br><br><br>
			</div>
			<div class="layui-footer" title="2025-03-25">
				<!-- 底部固定区域 -->
				© 2025 - <a target="_blank" style="color:#175199;" href="${ctxPath}/servlet/application?action=updateTables&type=sqlite">v3.5</a> 
				<span style="margin-left: 20px;display: none;">机器码：${machineCode }</span>
				<div style="float: right;"> <a target="_blank" style="color:#175199;display: none;" href="${ctxPath}/servlet/application?action=moveData"> 迁移数据(sqlite)</a></div>
			</div>
		</div>
		
		
		 <script id="navTmpl" type="text/template">
			{{for data}}
				{{if pResId=='0'}}
					<li class="layui-nav-item layui-nav-itemed">
							<a href="javascript:;">{{:resName}}</a>
							<dl class="layui-nav-child">
								 {{for #parent.parent.data}}
						 			 {{if #parent.parent.data.resId==pResId}}
											<dd>
												{{if resUrl.indexOf('_blank')>-1}}
													<a href="{{:resUrl}}" target="_blank">{{:resName}}</a>
													{{else}}
													<a href="javascript:;" data-uid="{{:resId}}" data-url="{{:resUrl}}">{{:resName}}</a>
												{{/if}}
											</dd>
	  								 {{/if}}
								{{/for}}
							</dl>
						</li>
				{{/if}}
			{{/for}}
		 </script>
		
		
		<script type="text/javascript" src="${staticPath}/lib/layer/layer.js"></script>
		<script type="text/javascript" src="${staticPath}/js/jsrender.min.js"></script>
		<script type="text/javascript" src="${staticPath}/lib/layui/layui.js"></script>
		<script type="text/javascript" src="${staticPath}/js/easitline.core-2.0.0.js"></script>
		<script type="text/javascript" src="${staticPath}/js/requreLib.js"></script>
		<script type="text/javascript" src="${ctxPath}/js/aes.js"></script>
		
		<script>
		
		  var ctxPath = '${ctxPath}';
		
		  
		  ajax.remoteCall("${ctxPath}/index?action=getMenu",{}, function(result) { 
			  var list = result.data;
	          var tmp = $.templates("#navTmpl");
	          var html = tmp.render({data:list});
		      $('.layui-nav-tree').html(html);
		            
			  layui.use(['element'], function(){
				var element = layui.element;
				$(".layui-side a[data-url]").click(function(){
					var t=$(this);
					var d = new Date();
					var url=t.data("url");
					if(url&&url.indexOf("?")>-1){
						url=url+"&t="+d.getTime();
					}else{
						url=url+"?t="+d.getTime();
					}
					window.location.hash=url; 
				});
				var url=window.location.hash;
				if(url){
					url=url.substr(1);
					loadPage(url,{});
				}else{
					//loadPage("${ctxPath}/pages/server/summary.jsp",{});
				}
				
				window.addEventListener('hashchange',function(e) {
						var url=window.location.hash;
						url=url.substr(1);
						loadPage(url,{});
				  }
				,false);
				  
				  
				ajax.daoCall({params:{},controls:['NodeDao.list']},function(result){
					var list = result['NodeDao.list'];
					var key = list['securityKey'];
					var data = list.data;
					if(data.length>0){
						for(var index in data){
							$('.nodeList').append('<dd><a target="_blank" href="'+data[index]['NODE_URL']+'${ctxPath}/node/server?action=auth&key='+key+'">'+data[index]['NODE_NAME']+'</a></dd>');
						}
					}else{
						$('.node-ul').remove();
					}
				});
				
				var safetyRmdr = sessionStorage.getItem('safetyRmdr');
				if(safetyRmdr==null){
					ajax.remoteCall("${ctxPath}/index?action=checkUserInfo",{}, function(result) { 
						sessionStorage.setItem('safetyRmdr','1');
						var data=result.data;
						if(data){
							layer.alert(data,{title:'安全提醒',icon:7,offset:'20px'});
						}
					});
				}
				ajax.remoteCall("${ctxPath}/index?action=checkField",{}, function(result) {});
				  
			});
			  
			var profileActive = $('.profileActiveEl').text();
			if(profileActive){
				 var profileActiveJson = {'test':'测试环境','prod':'生产环境','dev':'开发环境','pre':'预发环境'};
				 $('.profileActiveEl').text(profileActiveJson[profileActive]||'');
			}else{
				layer.alert('请先设置本IP地址的【环境类型】',{icon:7,offset:'30px'},function(index){
					layer.close(index);
					$("[data-uid='config']").click();
				});
			}
		 });
		  
		  
		
		function loadPage(url,data){
			 if(url.indexOf('/pages/')==-1&&url.indexOf('${ctxPath}/')==-1){
				 url = "/pages/"+url;
			 }
			 if(url.indexOf('${ctxPath}/')==-1){
				 url = "${ctxPath}/"+url;
			 }
			 $.ajax({
				 dataType:'html',
				 url:url,
				 data:data,
				 success:function(result){
				 	$(".layui-main").html(result);
				},beforeSend:function(){
					$(".layui-main").empty();
					layer.load(1);
				},complete:function(){
					layer.closeAll('loading');
				}
			});
		}
		
		function logout(){
			location.href="${ctxPath}/login?action=logout";
		}
		
		var oldTime = new Date().getTime();
		var newTime = new Date().getTime();
		var outTime = 30 * 60 * 1000; //设置超时时间：30分钟

		$(function(){
		    /* 鼠标移动事件 */
		    $(document).mouseover(function(){
		        oldTime = new Date().getTime(); //鼠标移入重置停留的时间
		    });
		});
		
		function OutTime(){
		    newTime = new Date().getTime(); //更新未进行操作的当前时间
		    if(newTime - oldTime > outTime){ //判断是否超时不操作
		        console.log("时间到，退出登录");
				setTimeout(function(){
					logout();
				},10000);
				  layer.alert('30分钟无操作,您已被系统强制退出.',{title:'系统提醒',icon:7,closeBtn:false},function(){
					logout();
				  });
		    }
		}
		/* 定时器  判断每60秒是否长时间未进行页面操作 */
		var timeTask = window.setInterval(OutTime, 1000*60);
		function clearTask(){
			clearInterval(timeTask);
		}
		
		</script>
		<script>
				var _hmt = _hmt || [];
				(function() {
				  var hm = document.createElement("script");
				  hm.src = "https://hm.baidu.com/hm.js?b9e2d180a3f5f12f1c0498f881395280";
				  var s = document.getElementsByTagName("script")[0]; 
				  s.parentNode.insertBefore(hm, s);
				})();
			</script>
	</body>
	

</html>