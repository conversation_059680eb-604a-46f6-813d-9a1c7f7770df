package org.easitline.console.service;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.dom4j.Document;
import org.dom4j.Element;
import org.easitline.common.core.Globals;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.lang.DictUtil;
import org.apache.commons.lang3.StringUtils;
import org.easitline.console.base.Constants;
import org.easitline.console.deploy.vo.AppConfig;
import org.easitline.console.utils.ConsoleUtils;
import org.easitline.console.utils.FileUtil;
import org.easitline.console.vo.AppdsModel;
import org.easitline.console.vo.ApplicationModel;
import org.easitline.console.vo.DictModel;
import org.easitline.console.vo.ResourceModel;

import com.alibaba.fastjson.JSONObject;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyQuery;

/**
 * 应用操作公共服务类
 * 主要用于统一管理应用WAR包解析、部署和升级逻辑
 */
public class ApplicationService {

    /**
     * 应用备份路径
     */
    public static final String APPS_BASE_PATH = Globals.BASE_DIR+File.separator+"easyserver"+File.separator+"apps";
    
    /**
     * 获取应用信息
     * @param warFile WAR文件
     * @param resourcePath 资源路径
     * @return ApplicationModel 应用信息
     */
    public static ApplicationModel getAppInfo(File warFile, String resourcePath) {
        ApplicationModel info = new ApplicationModel();
        try {
            Document doc = FileUtil.getResource(warFile, resourcePath);
            if (doc == null) return info;
            
            Element rootEle = doc.getRootElement();
            String appName = rootEle.attributeValue("name");
            String appVersion = rootEle.attributeValue("version");
            String appId = rootEle.attributeValue("id");
            
            info.setResTable(rootEle.attributeValue("resTable"));
            info.setAppId(appId);
            info.setWarName(warFile.getName());
            info.setAppName(appName);
            info.setAppVersion(appVersion);
            info.setDeployTime(EasyDate.getCurrentDateString());
            String lastModified = EasyDate.dateToString(new Date(warFile.lastModified()), "yyyy-MM-dd HH:mm:ss");
            info.setLastModified(lastModified);
            long fileLength = warFile.length();
            String warSize = ((fileLength/1024)+(fileLength%1024==0?0:1))+" KB";
            info.setWarSize(warSize);
            info.setAppFilePath(warFile.getAbsolutePath());
            
            Element description = rootEle.element("description");
            if (description != null) {
                info.setAppVersionDesc(description.getText());
            } else {
                info.setAppVersionDesc("");
            }
            
            if (info.getWarName().indexOf(".war") > -1) {
                info.setFileType("war");
            } else {
                info.setFileType("jar");
            }
            
            Element datasourcesEle = rootEle.element("datasources");
            
            if (null == datasourcesEle) return info;
            
            List<Element> dsListEle = datasourcesEle.elements();
            
            List<AppdsModel> dsList = new ArrayList<AppdsModel>();
            
            AppdsModel ds = new AppdsModel();
            ds.setDsName("_default_ds");
            ds.setDsDesc("缺省数据源");
            dsList.add(ds);
            
            for (Iterator<Element> iter = dsListEle.iterator(); iter.hasNext();) {
                Element dsEle = iter.next();
                ds = new AppdsModel();
                String dsName = dsEle.attributeValue("name");
                String dsDesc = dsEle.attributeValue("description");
                String target = dsEle.attributeValue("target");
                ds.setDsName(target);
                ds.setDsName(dsName);
                ds.setDsDesc(dsDesc);
                dsList.add(ds);
            }
            info.setDsList(dsList);
            
            return info;
        } catch (Exception e) {
            ConsoleUtils.getLogger().error("解析应用信息失败: " + e.getMessage(), e);
            return info;
        }
    }
    
    /**
     * 获取应用配置文件信息
     * @param appId 应用ID
     * @param version 版本
     * @param warFile WAR文件
     * @param resourcePath 资源路径
     * @return 应用配置信息列表
     */
    public static List<AppConfig> getAppConfig(String appId, String version, File warFile, String resourcePath) {
        List<AppConfig> appConfigList = new ArrayList<AppConfig>();
        Document doc = FileUtil.getResource(warFile, resourcePath);
        if (null == doc) return appConfigList;
        
        Element rootEle = doc.getRootElement();
        List<Element> elementList = rootEle.elements();
        
        int i = 1;
        for (Element paramEle : elementList) {
            AppConfig appConfig = new AppConfig();
            
            String key = paramEle.attributeValue("key");
            appConfig.setAppId(appId);
            appConfig.setVersion(version);
            appConfig.setItemKey(key);
            appConfig.setpItemKey("0");
            appConfig.setItemName(paramEle.attributeValue("name"));
            appConfig.setItemJson(paramEle.attributeValue("items"));
            appConfig.setItemValue(paramEle.attributeValue("value"));
            appConfig.setItemType(paramEle.attributeValue("type"));
            appConfig.setItemDesc(paramEle.attributeValue("description"));
            appConfig.setOrderIndex(i + "");
            
            List<Element> childEls = paramEle.elements();
            if (childEls != null && childEls.size() > 0) {
                appConfig.setpItemKey("1");
                for (Element _paramEle : childEls) {
                    AppConfig _appConfig = new AppConfig();
                    _appConfig.setAppId(appId);
                    _appConfig.setVersion(version);
                    _appConfig.setItemKey(_paramEle.attributeValue("key"));
                    _appConfig.setpItemKey(key);
                    _appConfig.setItemName(_paramEle.attributeValue("name"));
                    _appConfig.setItemJson(_paramEle.attributeValue("items"));
                    _appConfig.setItemValue(_paramEle.attributeValue("value"));
                    _appConfig.setItemType(_paramEle.attributeValue("type"));
                    _appConfig.setItemDesc(_paramEle.attributeValue("description"));
                    i++;
                    _appConfig.setOrderIndex(i + "");
                    appConfigList.add(_appConfig);
                }
            }
            i++;
            appConfigList.add(appConfig);
        }

        return appConfigList;
    }
    
    /**
     * 获取资源信息
     * @param appId 应用ID
     * @param version 应用版本 
     * @param warFile WAR文件
     * @param resourcePath 资源路径
     * @return 资源信息列表
     */
    public static List<ResourceModel> getResourceInfo(String appId, String version, File warFile, String resourcePath) {
        List<ResourceModel> resList = new ArrayList<ResourceModel>();
        Document doc = FileUtil.getResource(warFile, resourcePath);
        if (doc == null) return resList;
        Element element = (Element) doc.getRootElement();
        try {
            List<Element> elementList = element.elements();
            for (Element celement : elementList) {
                if (celement.getName().equalsIgnoreCase("resource")) {
                    ResourceModel resourceModel = new ResourceModel();
                    resourceModel.setAppId(appId);
                    resourceModel.setPresId("2000");
                    resourceModel.setResId(celement.attributeValue("id"));
                    resourceModel.setResName(celement.attributeValue("name"));
                    resourceModel.setResType(celement.attributeValue("type"));
                    resourceModel.setResUpdate(celement.attributeValue("update"));
                    resourceModel.setResUrl(celement.attributeValue("url"));
                    resourceModel.setIdxOrder(celement.attributeValue("index"));
                    resourceModel.setResState(celement.attributeValue("state"));
                    resourceModel.setPortal(celement.attributeValue("portal"));
                    resourceModel.setResIcon(celement.attributeValue("icon"));
                    resourceModel.setAuthFlag(celement.attributeValue("authFlag"));
                    resourceModel.setEntId(StringUtils.defaultString(celement.attributeValue("entId"), "0"));
                    resourceModel.setResRemark(StringUtils.defaultString(celement.attributeValue("remark"), ""));
                    resourceModel.setResCode(StringUtils.defaultString(celement.attributeValue("code"), ""));
                    resList.add(resourceModel);
                    parseResourceInfo(resList, appId, version, resourceModel.getResId(), celement);
                }
            }
        } catch (Exception ex) {
            ConsoleUtils.getLogger().warn("应用程序[" + appId + "]菜单配置文件不存在，原因：" + ex.getMessage(), ex);
        }
        return resList;
    }
    
    /**
     * 解析资源信息
     * @param resList 资源列表
     * @param appId 应用ID
     * @param version 版本
     * @param presId 父资源ID
     * @param element 元素
     */
    private static void parseResourceInfo(List<ResourceModel> resList, String appId, String version, String presId, Element element) {
        List<Element> elementList = element.elements();
        for (Element celement : elementList) {
            if (celement.getName().equalsIgnoreCase("resource")) {
                ResourceModel resourceModel = new ResourceModel();
                resourceModel.setAppId(appId);
                resourceModel.setPresId(presId);
                resourceModel.setResId(celement.attributeValue("id"));
                resourceModel.setResName(celement.attributeValue("name"));
                resourceModel.setResType(celement.attributeValue("type"));
                resourceModel.setResUpdate(celement.attributeValue("update"));
                resourceModel.setResUrl(celement.attributeValue("url"));
                resourceModel.setIdxOrder(celement.attributeValue("index"));
                resourceModel.setResState(celement.attributeValue("state"));
                resourceModel.setPortal(celement.attributeValue("portal"));
                resourceModel.setResIcon(celement.attributeValue("icon"));
                resourceModel.setAuthFlag(celement.attributeValue("authFlag"));
                resourceModel.setEntId(StringUtils.defaultString(celement.attributeValue("entId"), "0"));
                resourceModel.setResRemark(StringUtils.defaultString(celement.attributeValue("remark"), ""));
                resourceModel.setResCode(StringUtils.defaultString(celement.attributeValue("code"), ""));
                resList.add(resourceModel);
                parseResourceInfo(resList, appId, version, resourceModel.getResId(), celement);
            }
        }
    }
    
    /**
     * 获取数据字典信息
     * @param appId 应用ID
     * @param version 版本
     * @param warFile WAR文件
     * @param resourcePath 资源路径
     * @return 数据字典列表
     */
    public static List<DictModel> getDictInfo(String appId, String version, File warFile, String resourcePath) {
        List<DictModel> dicts = new ArrayList<DictModel>();
        try {
        	Document doc = FileUtil.getResource(warFile, resourcePath);
        	if (doc == null) return dicts;
        	Element element = (Element) doc.getRootElement();
            List<Element> elementList = element.elements();
            for (Element celement : elementList) {
                if (celement.getName().equalsIgnoreCase("dict")) {
                    DictModel model = new DictModel();
                    model.setAppId(appId);
                    model.setId(celement.attributeValue("id"));
                    model.setName(celement.attributeValue("name"));
                    model.setIndex(Integer.parseInt(celement.attributeValue("index")));
                    model.setType(celement.attributeValue("type"));
                    model.setItems(getDictItems(celement));
                    dicts.add(model);
                }
            }
        } catch (Exception ex) {
            ConsoleUtils.getLogger().warn("应用程序[" + appId + "]数据字典文件不存在，原因：" + ex.getMessage(), ex);
        }
        return dicts;
    }
    
    /**
          * 获取字典项
     * @param typeElement 类型元素
     * @return 字典项列表
     */
    public static List<JSONObject> getDictItems(Element typeElement) {
        List<JSONObject> items = new ArrayList<JSONObject>();
        List<Element> elementList = typeElement.elements();
        for (Element celement : elementList) {
            if (celement.getName().equalsIgnoreCase("item")) {
                JSONObject item = new JSONObject();
                item.put("id", celement.attributeValue("id"));
                item.put("name", celement.attributeValue("name"));
                item.put("pid", celement.attributeValue("pid"));
                item.put("desc", celement.attributeValue("desc"));
                item.put("index", celement.attributeValue("index"));
                items.add(item);
            }
        }
        return items;
    }
    
    /**
          * 更新应用信息
     * @param appInfo 应用信息
     * @throws SQLException SQL异常
     */
    public static void saveAppInfo(ApplicationModel appInfo) throws SQLException {
        String sql = "select count(*) as tcount from EASI_APP_INFO where APP_ID = ?";
        if (Constants.getDb().queryForExist(sql, new Object[] { appInfo.getAppId() })) {
            sql = "update EASI_APP_INFO set APP_NAME=?, APP_VERSION=?, DEPLOY_TIME=?, APP_VERSION_DESC=?, LAST_MODIFIED=?, WAR_SIZE=? where APP_ID=?";
            Object[] params = new Object[] {
                appInfo.getAppName(),
                appInfo.getAppVersion(),
                appInfo.getDeployTime(),
                appInfo.getAppVersionDesc(),
                appInfo.getLastModified(),
                appInfo.getWarSize(),
                appInfo.getAppId()
            };
            Constants.getDb().execute(sql, params);
            ConsoleUtils.getLogger().info("更新应用信息: " + appInfo.getAppId());
        } else {
            sql = "insert into EASI_APP_INFO(APP_ID,APP_NAME,APP_VERSION,WAR_NAME,DEPLOY_TIME,APP_FILE_PATH,APP_VERSION_DESC,LAST_MODIFIED,WAR_SIZE) values(?,?,?,?,?,?,?,?,?)";
            Object[] params = new Object[] {
                appInfo.getAppId(),
                appInfo.getAppName(),
                appInfo.getAppVersion(),
                appInfo.getWarName(),
                appInfo.getDeployTime(),
                appInfo.getAppFilePath(),
                appInfo.getAppVersionDesc(),
                appInfo.getLastModified(),
                appInfo.getWarSize()
            };
            Constants.getDb().execute(sql, params);
            ConsoleUtils.getLogger().info("插入新应用信息: " + appInfo.getAppId());
        }
        
        if("jar".equals(appInfo.getFileType())){
            EasyRecord record = new EasyRecord("EASI_APP_INFO", "APP_ID");
            record.set("APP_ID", appInfo.getAppId());
            record.set("DEPOY_DIR", appInfo.getDepoyDir());
            try {
                Constants.getDb().update(record);
            } catch (SQLException e) {
                sql = "ALTER TABLE EASI_APP_INFO ADD COLUMN DEPOY_DIR varchar(255)";
                Constants.getDb().execute(sql.toString(), new Object[] {});
                Constants.getDb().update(record);
            }
        }
    	sql = " delete from  EASI_APP_DS where APP_ID = ?";
    	Constants.getDb().execute(sql, new Object[]{appInfo.getAppId()});
		for(AppdsModel appds:appInfo.getDsList()){
			sql = "insert into EASI_APP_DS(APP_ID,DS_NAME,DS_DESC,SYS_DS_NAME) values(?,?,?,?)";
			Object[] params = new Object[]{appInfo.getAppId(),appds.getDsName(),appds.getDsDesc(),appds.getSysDsName()};
			Constants.getDb().execute(sql, params);
		}
        
    }
    
    public static void updateAppInfo(ApplicationModel appInfo,File destFile) throws SQLException {
    	EasyRecord record = new EasyRecord("EASI_APP_INFO", "APP_ID");
		record.setPrimaryValues(appInfo.getAppId());
		record.set("DEPLOY_TIME",appInfo.getDeployTime());
		record.set("APP_VERSION", appInfo.getAppVersion());
		record.set("WAR_NAME",appInfo.getWarName());
		record.set("APP_NAME", appInfo.getAppName());
		record.set("APP_VERSION_DESC", appInfo.getAppVersionDesc());
		record.set("WAR_SIZE", appInfo.getWarSize());
		record.set("APP_FILE_PATH",destFile.getAbsolutePath());
		Constants.getDb().update(record);
    }
    
    /**
          * 添加历史记录
     * @param tempfile 临时文件
     * @param model 应用模型
     * @throws IOException IO异常
     */
    public static void addHis(File tempfile, ApplicationModel model) throws IOException {
        try {
            File tmpDir = new File(APPS_BASE_PATH + File.separator + model.getAppId());
            if (!tmpDir.isDirectory() || !tmpDir.exists()) tmpDir.mkdir();
            
            File destFile = new java.io.File(APPS_BASE_PATH + File.separator + model.getAppId() + File.separator + EasyDate.getCurrentDateString("yyyyMMddHHmm") + "_" + model.getWarName());
            
            String profileActive = ServerContext.getProperties("PROFILE_ACTIVE", "prod");
            if (("prod".equals(profileActive) || "pre".equals(profileActive)) || "0".equals(AppContext.getContext(Constants.EASITLINE_CONSOLE).getProperty("upgradeBakWar", "0"))) {
                FileUtils.copyFile(tempfile, destFile);
                ConsoleUtils.getLogger().warn("备份升级WAR文件: " + destFile.getAbsolutePath());
            }
            
            EasyRecord record = new EasyRecord("EASI_APP_INFO_DEPLOY_HIS", "ID");
            record.set("ID", RandomKit.uniqueStr());
            record.set("APP_NAME", model.getAppName());
            record.set("APP_ID", model.getAppId());
            record.set("APP_VERSION", model.getAppVersion());
            record.set("WAR_NAME", model.getWarName());
            record.set("DEPLOY_TIME", model.getDeployTime());
            record.set("APP_FILE_PATH", destFile.getAbsolutePath());
            record.set("APP_VERSION_DESC", model.getAppVersionDesc());
            record.set("LAST_MODIFIED", model.getLastModified());
            record.set("WAR_SIZE", model.getWarSize());
            
            Constants.getDb().save(record);
            ConsoleUtils.getLogger().info("添加应用部署历史记录: " + model.getAppId() + " 版本: " + model.getAppVersion());
        } catch (Exception e) {
            ConsoleUtils.getLogger().error("添加应用部署历史记录失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取部署目录
     * @return 部署目录路径
     */
    public static String deployDir() {
        return System.getProperty("deployDir", Globals.WEBAPPS_DIR);
    }
    
    /**
     * 获取应用配置
     * @param appId 应用ID
     * @return 配置项映射
     */
    public static Map<String,String> getAppConfigs(String appId) {
        String sql = "select ITEM_VALUE,ITEM_KEY from EASI_APP_CONF where APP_ID = ? ";
        Map<String,String> items = new HashMap<String,String>();
        try {
            List<EasyRow> list = Constants.getDb().queryForList(sql, new Object[]{appId});
            for(EasyRow row : list) {
                items.put(row.getColumnValue("ITEM_KEY"), row.getColumnValue("ITEM_VALUE"));
            }
        } catch (SQLException ex) {
            ConsoleUtils.getLogger().error("获取应用的配置信息失败，原因：" + ex.getMessage(), ex);
        }
        return items;
    }
    
    /**
         * 更新应用配置
     * @param appInfo 应用信息
     * @param appConfigs 配置列表
     * @throws SQLException SQL异常
     */
    public static void updateAppConfig(ApplicationModel appInfo, List<AppConfig> appConfigs) throws SQLException {
        String appId = appInfo.getAppId();
        Map<String,String> oldAppConfig = getAppConfigs(appId);
        String sql = "delete from EASI_APP_CONF where APP_ID = ?  ";
        Constants.getDb().execute(sql, new Object[]{appId});
        long tempVal = System.currentTimeMillis();
        
        for (AppConfig appConfig : appConfigs) {
            String value = oldAppConfig.get(appConfig.getItemKey());
            if(StringUtils.isBlank(value)) {
            	value = appConfig.getItemValue();
            }
            value = ConfigCryptorService.encryptString(value,"AV");
            
            sql = "insert into EASI_APP_CONF(APP_ID,ITEM_KEY,P_ITEM_KEY,ITEM_NAME,ITEM_DESC,JSON_STRING,ITEM_VALUE,ITEM_TYPE,ORDER_INDEX) values(?,?,?,?,?,?,?,?,?)";
            Object[] params = new Object[] { 
                appConfig.getAppId(), appConfig.getItemKey(), appConfig.getpItemKey(), appConfig.getItemName(),
                appConfig.getItemDesc(), appConfig.getItemJson(), value, appConfig.getItemType(), appConfig.getOrderIndex()
            };
            
            Constants.getDb().execute(sql, params);
            
            sql = "insert into EASI_APP_CONF_HIS(APP_ID,APP_VERSIOIN,BACKUP_TIME,ITEM_KEY,P_ITEM_KEY,ITEM_NAME,ITEM_DESC,JSON_STRING,ITEM_VALUE,ITEM_TYPE) values(?,?,?,?,?,?,?,?,?,?)";
            params = new Object[] { 
                appConfig.getAppId(), tempVal, EasyDate.getCurrentDateString(), appConfig.getItemKey(), appConfig.getpItemKey(), appConfig.getItemName(),
                appConfig.getItemDesc(), appConfig.getItemJson(), value, appConfig.getItemType()
            };
            Constants.getDb().execute(sql, params);
        }
    }
    
    /**
     * 更新应用数据字典
     * @param appDicts 字典列表
     * @throws SQLException SQL异常
     */
    public static void updateAppDict(List<DictModel> appDicts) throws SQLException {
        String sql = "";
        //更新字典类型
        for(DictModel dict : appDicts) {
            sql = "select count(*) as tcount from EASI_PC_DICT_TYPE where DICT_TYPE_ID = ?";
            if(ServerContext.getAdminQuery().queryForExist(sql, new Object[]{dict.getId()})) {
                sql = "update EASI_PC_DICT_TYPE set DICT_TYPE_NAME = ? ,IDX_ORDER=? ,APP_ID=? where DICT_TYPE_ID = ? ";
                Object[] params = new Object[]{dict.getName(), dict.getIndex(), dict.getAppId(), dict.getId()};
                ServerContext.getAdminQuery().execute(sql, params);
            } else {
                sql = "insert into EASI_PC_DICT_TYPE(DICT_TYPE_ID,DICT_TYPE_NAME,IDX_ORDER,APP_ID,DICT_RES) values(?,?,?,?,?)";
                Object[] params = new Object[]{dict.getId(), dict.getName(), Integer.valueOf(dict.getIndex()), dict.getAppId(), dict.getType()};
                ServerContext.getAdminQuery().execute(sql, params);
            }
            //更新字典信息EASI_PC_DICT
            ConsoleUtils.getLogger().debug("字典数据->" + dict.getItems());
            for(JSONObject jsonObject : dict.getItems()) {
                sql = "delete from EASI_PC_DICT where DICT_TYPE_ID = ? and DICT_ID = ? ";
                ServerContext.getAdminQuery().execute(sql, new Object[]{dict.getId(), jsonObject.getString("id")});
                sql = "insert into EASI_PC_DICT(DICT_TYPE_ID,DICT_ID,DICT_NAME,P_DICT_ID,IDX_ORDER,DICT_DESC) values(?,?,?,?,?,?)";
                Object[] params = new Object[]{
                    dict.getId(), jsonObject.getString("id"), jsonObject.getString("name"), 
                    jsonObject.getString("pid"), jsonObject.getIntValue("index"), jsonObject.getString("desc")
                };
                ServerContext.getAdminQuery().execute(sql, params);
            }
            DictUtil.reload(dict.getId());
        }
    }

    /**
     * 初始化应用数据库脚本
     * @param appId 应用ID
     * @param jarFile JAR文件
     * @return 是否成功
     * @throws Exception 异常
     */
    public static boolean initAppDbscript(String appId, File jarFile) throws Exception {
        EasyQuery easyQuery = EasyQuery.getQuery(appId, "_default_ds");  //获得缺省数据源
        DBTypes dbType = easyQuery.getTypes();
        ConsoleUtils.getLogger().info("数据库类型:" + dbType);
        String scriptFile = null;
        if (dbType == DBTypes.MYSQL) {
            scriptFile = "META-INF/script/script_mysql.sql";
        } else if (dbType == DBTypes.ORACLE) {
            scriptFile = "META-INF/script/script_oracle.sql";
        } else if (dbType == DBTypes.SQLSERVER) {
            scriptFile = "META-INF/script/script_sqlserver.sql";
        }
        
        ConsoleUtils.getLogger().debug("应用[" + appId + "," + jarFile + "]脚本文件：" + scriptFile);
        String[] sqls = {};
        try {
            byte[] script = FileUtil.getAppResource(jarFile, scriptFile);
            String sqlStr = new String(script, "UTF-8");
            sqls = sqlStr.split(";");
        } catch (Exception ex) {
            throw new Exception("获取[" + appId + "]初始化脚本失败，请检查脚本文件[" + scriptFile + "]是否存在!", ex);
        }
        
        //执行数据库初始化操作
        for(String sql : sqls) {
            try {
                if(StringUtils.isBlank(sql)) {
                    continue;
                }
                if(sql.toLowerCase().indexOf("drop") > 0) {
                    ConsoleUtils.getLogger().warn("不允许执行drop语句，SQL：" + sql);
                    continue;
                }
                if(sql.toLowerCase().indexOf("delete") > 0) {
                    ConsoleUtils.getLogger().warn("不允许执行delete语句，SQL：" + sql);
                    continue;
                }
                
                easyQuery.execute(sql, null);
            } catch (Exception ex) {
                ConsoleUtils.getLogger().error(ex.getMessage(), ex);
            }
        }
        return true;
    }
}
