<%@page pageEncoding="UTF-8"%>
<div>
	<fieldset class="layui-elem-field layui-field-title">
		<legend>缓存管理</legend>
	</fieldset>
	<form class="layui-form" method="post" id="easyform"  autocomplete="off">
		  <div class="layui-form-item">
		    <label class="layui-form-label">key</label>
		    <div class="layui-input-block">
		      <input type="text" name="cacheKey" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">cacheTime</label>
		    <div class="layui-input-block">
		      <input type="text" value="600" placeholder="秒,新增需要填写" name="cacheTime" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">value</label>
		    <div class="layui-input-block">
		      <textarea style="height: 120px;" name="cacheValue" class="layui-textarea"></textarea>
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <div class="layui-input-block">
		      <button type="button" onclick="doExcute('search')" class="layui-btn layui-btn-sm">查询</button>&nbsp;&nbsp;
		      <button type="button" class="layui-btn layui-btn-sm layui-btn-warm" onclick="doExcute('add')">新增</button>&nbsp;&nbsp;
		      <button type="button" onclick="doExcute('del')" class="layui-btn layui-btn-sm layui-btn-normal">删除</button>
		    </div>
		  </div>
	</form>
</div>

<script>
	function doExcute(opp) {
		var data = form.getJSONObject("easyform"); 
		ajax.remoteCall("${ctxPath}/servlet/cacheMgr?action=excute&type="+opp,data,function(result) { 	
			if(result.state==1){
				layer.msg(result.msg,{icon: 1,time:800},function(){
					layer.closeAll();
					if(opp=='search'){
						$("[name='cacheValue']").val(result.data);
					}
				});
			}else{
				layer.msg(result.msg,{icon: 5});
			}
		});
	}
	
	
</script>