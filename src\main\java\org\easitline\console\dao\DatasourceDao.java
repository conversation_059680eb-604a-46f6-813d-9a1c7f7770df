package org.easitline.console.dao;


import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.apache.commons.lang3.StringUtils;
import org.easitline.console.base.AppDaoContext;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

@WebObject(name="datasource")
public class DatasourceDao extends AppDaoContext{
	
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		JSONObject result = queryForList("select * from EASI_DS_INFO", null,new MapRowMapperImpl());
		JSONArray list = result.getJSONArray("data");
		if(list!=null&&list.size()>0) {
			for(int i=0,len=list.size();i<len;i++) {
				JSONObject row = list.getJSONObject(i);
				row.remove("PASSWORD");
			}
		}
		return result;
	}
	
	@WebControl(name="record",type=Types.RECORD)
	public  JSONObject record(){
		String pkId = param.getString("pkId");
		if(StringUtils.isNotBlank(pkId)) {
			JSONObject result = queryForRecord("select * from EASI_DS_INFO where SYS_DS_NAME = ?", pkId);
			JSONObject row = result.getJSONObject("data");
			if(row!=null) {
				row.remove("PASSWORD");
			}
			return result;
		}
		return getJsonResult(new JSONObject());
	}
	
	@WebControl(name="dsDict",type=Types.DICT)
	public  JSONObject dsDict(){
		return getDictByQuery("select SYS_DS_NAME as DS_ID,SYS_DS_NAME from EASI_DS_INFO", new Object[]{});
	}
}





