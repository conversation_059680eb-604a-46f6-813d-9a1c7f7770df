<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
	 <blockquote class="layui-elem-quote" onclick="getGlobalConfig()">
		<a href="javascript:void(0)" onclick="appLogPage()">应用日志</a>
		<a style="margin-left: 20px;" href="javascript:void(0)" onclick="operateLogPage()">操作日志</a>
	</blockquote>
	 <fieldset class="layui-elem-field layui-field-title">
		<legend>登录日志</legend>
	  </fieldset>
		<form id="easyform-log-list">
			<div style="padding:0 15px" id="appHisList">
				<table class="layui-table text-center" data-mars="server.latelyLoginLog" data-container="#histData" data-template="hisListTemp">
					<thead>
						<tr>
							<th>序号</th>
							<th>登录用户</th>
							<th>登录IP</th>
							<th>登录时间</th>
							<th>登录结果</th>
							<th>操作</th>
						</tr>
					</thead>
					<tbody id="histData">
					</tbody>
				</table>
					<script id="hisListTemp" type="text/x-jsrender">
					{{for list}}
							<tr>
								<td>{{:#index+1}}</td>
								<td>{{:USERNAME}}</td>
								<td>{{:IP}}</td>
								<td>{{:LOGIN_TIME}}</td>
								<td>{{:MSG}}</td>
								<td><a style="display:none;" href="javascript:void(0);" onclick="clearLoginLog('{{:LOG_ID}}')">删除</a></td>
							</tr>
					{{/for}}					         
					</script>
		    </div>
		</form>

<script>

	$(function(){
		$("#easyform-log-list").render();
	});
	
	function appLogPage(){
		loadPage("logger/app-log-list.jsp",{});
	}
	
	function operateLogPage(){
		loadPage("logger/operate-log-list.jsp",{});
	}
	
	function clearLoginLog(id){
		ajax.remoteCall("${ctxPath}/servlet/serverInfo?action=delLoginLog",{logId:id},function(result) { 	
			if(result.state==1){
				layer.msg(result.msg,{icon: 1,time:800},function(){
					layer.closeAll();
					loadPage("logger/login-log-list.jsp");
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
	
</script>

