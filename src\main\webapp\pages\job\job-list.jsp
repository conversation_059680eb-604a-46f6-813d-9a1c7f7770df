<%@page pageEncoding="UTF-8"%>
<div>
	<blockquote class="layui-elem-quote">
		定时任务列表
	</blockquote>
	<form id="easyform-job-list">
		<fieldset class="layui-elem-field layui-field-title">
			<legend>quartzJob定时任务列表</legend>
		</fieldset>
		<table class="layui-table text-center" data-mars="job.quartzJobList" data-container="dataList2" data-template="list-template2">
				<thead>
					<tr>
						<th>组</th>
						<th>任务名称</th>
						<th>任务类的名称</th>
						<th>任务的周期或者时期</th>
						<th>状态</th>
						<th>描述</th>
						<th>下次执行时间</th>
						<th>操作</th>
					</tr>
				</thead>
				<tbody id="dataList2"></tbody>
			</table>
			
		<fieldset class="layui-elem-field layui-field-title">
			<legend>Console定时任务列表</legend>
		</fieldset>
		<div class="layui-form" style="padding-left:15px">
			<table class="layui-table text-center" data-mars="job.list" data-container="dataList" data-template="list-template">
				<thead>
					<tr>
						<th>任务名称</th>
						<th>应用名称</th>
						<th>服务</th>
						<th>成功次数</th>
						<th>失败次数</th>
						<th>上次成功时间</th>
						<th>上次失败时间</th>
						<th>任务状态</th>
						<th>下次执行时间</th>
						<th>操作</th>
					</tr>
				</thead>
				<tbody id="dataList"></tbody>
			</table>
			<button type="button" style="margin-top:6px;margin-left:10px" class="layui-btn layui-btn-small layui-btn-normal" onclick="addJob()">新增任务</button>
	    </div>
    </form>
</div>
<script id="list-template" type="text/x-jsrender">
				{{for list}}
				<tr>
					<td class="td-textcut">{{:jobName}}</td>
					<td class="td-textcut">{{:appName}}</td>
					<td class="td-textcut" title="{{:serviceId}}({{:serviceName}})">{{if serviceId&&serviceId!=""}}{{:serviceId}}({{:serviceName}}){{else}}未关联服务{{/if}}</td>
					<td class="td-textcut">{{:okCount}}</td>
					<td title="点击查看错误日志"  onclick="checkJobLog('{{:jobId}}')"><a href="javascript:void(0)">{{:failCount}}</a></td>
					<td>{{:lastOkTime}}</td>
					<td>{{:lastFailTime}}</td>
					<td>{{:jobStateName}}</td>
					<td>{{:nextTime}}</td>
					<td>
						<a href="javascript:addJob('{{:jobId}}')">编辑</a>&nbsp;
                        <a href="javascript:deleteJob('{{:jobId}}')">删除</a>&nbsp;
						{{if jobStateName=='暂停'}}
							<a href="javascript:startJob('{{:jobId}}')">启动</a>&nbsp;
						{{else}}
							<a href="javascript:pauseJob('{{:jobId}}')">暂停</a>&nbsp;
						{{/if}}
					</td>
				</tr>
				{{/for}}
</script>

<script id="list-template2" type="text/x-jsrender">
			  {{for list}}
				<tr>
					<td title="{{:TriggerGroup}}" class="td-textcut">{{:TriggerGroup}}</td>
					<td title="{{:JobName}}" class="td-textcut">{{:JobName}}</td>
					<td title="{{:JobClass}}" class="td-textcut">{{:JobClass}}</td>
					<td title="{{:cronExpression}}" class="td-textcut">{{:cronExpression}}</td>
					<td>{{:State}}</td>
					<td title="{{:Description}}" class="td-textcut">{{:Description}}</td>
					<td>{{call:NextFireTime fn='formatChineseDateTime'}}</td>
					<td>
						{{if State=='NORMAL'}}
							<a style="color:red" href="javascript:quartzJobPause('{{:TriggerGroup}}','{{:TriggerName}}')">暂停</a>&nbsp;
						{{else}}
							<a href="javascript:quartzJobStart('{{:TriggerGroup}}','{{:TriggerName}}')">启动</a>&nbsp;
						{{/if}}
					</td>
				</tr>
			{{/for}}
</script>

<script type="text/javascript">
	$(function(){
		$("#easyform-job-list").render({success:function(){
		}});
	});
	
	//添加和修改任务
	function addJob(jobId){
		loadPage("job/job-add.jsp",{jobId:jobId});
	}
	
	//查看日志
	function checkJobLog(jobId) {
		popup.layerShow({type:1,title:'定时任务执行日志',offset:'auto',area:['60%','50%']},"${ctxPath}/pages/job/joblog-list.jsp",{jobId:jobId});
	}
	
	//删除调度任务
	function deleteJob(jobId) {
		layer.confirm("是否确认删除调度任务？",function(){
			var data = {jobId:jobId};
			ajax.remoteCall("${ctxPath}/servlet/job?action=delete",
				data,
				function(result) { 	
					if(result.state==1){
						layer.msg(result.msg,{icon:1,time:800},function(){
							layer.closeAll();
							loadPage("job/job-list.jsp",{});
						});
					}else{
						layer.alert(result.msg);
					}
				}
			);
		});
	}
	
	//暂停调度任务
	function pauseJob(jobId) {
		var data = {jobId:jobId};
		ajax.remoteCall("${ctxPath}/servlet/job?action=pause",data,function(result) { 	
				if(result.state==1){
					layer.msg(result.msg,{icon:1,time:800},function(){
						layer.closeAll();
						loadPage("job/job-list.jsp",{});
					});
				}else{
					layer.alert(result.msg);
				}
			}
		);
	}
	
	//暂停调度任务
	function quartzJobPause(triggerGroup,triggerName) {
		var data = {triggerGroup:triggerGroup,triggerName:triggerName};
		ajax.remoteCall("${ctxPath}/servlet/job?action=quartzJobPause",data,function(result) { 	
				if(result.state==1){
					layer.msg(result.msg,{icon:1,time:800},function(){
						layer.closeAll();
						loadPage("job/job-list.jsp",{});
					});
				}else{
					layer.alert(result.msg);
				}
			}
		);
	}
	
	//启动调度任务
	function startJob(jobId) {
		var data = {jobId:jobId};
		ajax.remoteCall("${ctxPath}/servlet/job?action=start",data,function(result) { 	
			if(result.state==1){
				layer.msg(result.msg,{icon:1,time:800},function(){
					layer.closeAll();
					loadPage("job/job-list.jsp",{});
				});
			}else{
				layer.alert(result.msg);
			}
		  }
		);
	}
	
	function quartzJobStart(triggerGroup,triggerName) {
		var data = {triggerGroup:triggerGroup,triggerName:triggerName};
		ajax.remoteCall("${ctxPath}/servlet/job?action=quartzJobStart",data,function(result) { 	
			if(result.state==1){
				layer.msg(result.msg,{icon:1,time:800},function(){
					layer.closeAll();
					loadPage("job/job-list.jsp",{});
				});
			}else{
				layer.alert(result.msg);
			}
		}
	  );
	}
	
	function formatChineseDateTime(timestamp) {
		// 创建一个Date对象，传入时间戳
		let nextFireDate = new Date(timestamp);
		// 将Date对象转换为指定格式的日期字符串（yyyy-MM-dd HH:mm:ss）
		let formattedDate = nextFireDate.toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit' });
		return formattedDate;
	}
</script>
