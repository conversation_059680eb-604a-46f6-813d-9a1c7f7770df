package org.easitline.console.command;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Kill extends Thread {
    private String processId;
    private long delay = 0L;
    private static final Logger logger = LoggerFactory.getLogger(Kill.class);

    /**
     * @param processId
     */
    public Kill(String processId) {
        this.processId = processId;
    }

    /**
     * @param processId
     * @param delay
     */
    public Kill(String processId, long delay) {
        this.processId = processId;
        this.delay = delay;
    }

    /**
     * execute
     */
    @Override
    public void run() {
        if(this.processId == null || this.processId.length() < 1) {
            return;
        }

        if(this.delay > 0L) {
            try {
                Thread.sleep(this.delay);
            }
            catch(InterruptedException e) {
                logger.error(e.getMessage(), e);
            }
        }
        Kill.execute(this.processId);
    }

    /**
     * @param processId
     * @param delay
     */
    public static void kill(String processId, long delay) {
        new Kill(processId, delay).start();
    }

    /**
     * 杀进程
     * @param processId
     */
    public static void execute(String processId) {
        boolean deamon = Thread.currentThread().isDaemon();

        if(OS.WINDOWS) {
            try {
                logger.info("taskkill /F /pid {}", processId);
                ProcessBuilder processBuilder = new ProcessBuilder("taskkill", "/F", "/pid", processId);
                Process process = processBuilder.start();

                ReadThread.execute("errout", process.getErrorStream(), deamon);
                ReadThread.execute("stdout" , process.getInputStream(), deamon);

                int exitCode = process.waitFor();
                logger.info("exitCode: {}", exitCode);
            }
            catch(Exception e) {
                logger.error(e.getMessage(), e);
            }
            return;
        }
        else {
            try {
                logger.info("kill -9 {}", processId);
                ProcessBuilder processBuilder = new ProcessBuilder("kill", "-9", processId);
                Process process = processBuilder.start();

                ReadThread.execute("errout", process.getErrorStream(), deamon);
                ReadThread.execute("stdout" , process.getInputStream(), deamon);

                int exitCode = process.waitFor();
                logger.info("exitCode: {}", exitCode);
            }
            catch(Exception e) {
                logger.error(e.getMessage(), e);
            }
            return;
        }
    }
}

