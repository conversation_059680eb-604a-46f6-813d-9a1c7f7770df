package org.easitline.console.service;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.SQLException;
import java.util.List;

import org.easitline.common.core.Globals;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.crypt.SM4Util;
import org.easitline.common.utils.string.StringUtils;
import org.easitline.console.base.Constants;
import org.easitline.console.utils.ConsoleUtils;
import org.easitline.console.utils.DESUtil;

import com.alibaba.fastjson.JSONObject;

public class CryptoDataUpdateService {

	public static void migrateEncryptedConfig() {
		try {
			migrateByDes();
		} catch (Exception e) {
			ConsoleUtils.getLogger().error("[迁移入口][DES] 迁移异常: " + e.getMessage(), e);
		}
		try {
			migrateBySm4();
		} catch (Exception e) {
			ConsoleUtils.getLogger().error("[迁移入口][SM4] 迁移异常: " + e.getMessage(), e);
		}
		try {
			desToSm4();
		} catch (Exception e) {
			ConsoleUtils.getLogger().error("[迁移入口][DES->SM4] 迁移异常: " + e.getMessage(), e);
		}
	}
	
	public static void migrateByDes(){
		String currentKeyFilePath = Globals.BASE_DIR + File.separator + "des.current.key";
		String previousKeyFilePath = Globals.BASE_DIR + File.separator + "des.previous.key";
		File currentKeyFile = new File(currentKeyFilePath);
		File previousKeyFile = new File(previousKeyFilePath);
		if(!currentKeyFile.exists()) {
//			ConsoleUtils.getLogger().warn("[DES迁移] 当前密钥文件不存在: " + currentKeyFilePath);
			return;
		}
		ConsoleUtils.getLogger().info("[DES迁移] 开始DES密钥迁移流程...");
		if(!previousKeyFile.exists()) {
			ConsoleUtils.getLogger().warn("[DES迁移] 上一个密钥文件不存在: " + previousKeyFilePath);
			return;
		}
		String previousKey = loadSecretKey(previousKeyFilePath);
		String currentKey = loadSecretKey(currentKeyFilePath);
		if(StringUtils.isBlank(previousKey)) {
			ConsoleUtils.getLogger().warn("[DES迁移] 上一个密钥内容为空: " + previousKeyFilePath);
			return;
		}
		if(StringUtils.isBlank(currentKey)) {
			ConsoleUtils.getLogger().warn("[DES迁移] 当前密钥内容为空: " + currentKeyFilePath);
			return;
		}
		currentKeyFile.delete();
		previousKeyFile.delete();
		
		EasyQuery db = Constants.getDb();
		List<JSONObject> recordList;
		int total = 0, updated = 0, skipped = 0, failed = 0;
		java.util.List<String> successKeys = new java.util.ArrayList<>();
		java.util.List<String> skipKeys = new java.util.ArrayList<>();
		java.util.List<String> failKeys = new java.util.ArrayList<>();
		java.util.List<String> failReasons = new java.util.ArrayList<>();
		try {
			ConsoleUtils.getLogger().info("[DES迁移] 查询EASI_DS_INFO数据...");
			recordList = db.queryForList("select * from EASI_DS_INFO", new Object[] {}, new JSONMapperImpl());
			ConsoleUtils.getLogger().info("[DES迁移] 共查询到" + recordList.size() + "条记录");
			for(JSONObject record : recordList) {
				total++;
				String recordKey = record.getString("SYS_DS_NAME");
				String encryptedField = record.getString("PASSWORD");
				try {
					if(encryptedField != null && encryptedField.startsWith("3DES")){
						String encryptedValue = encryptedField.replace("3DES_", "");
						String plainValue = DESUtil.getInstance(previousKey).decryptStr(encryptedValue);
						String newEncryptedValue = DESUtil.getInstance(currentKey).encryptStr(plainValue);
						db.executeUpdate("update EASI_DS_INFO set PASSWORD = ? where SYS_DS_NAME = ?", new Object[] {"3DES_" + newEncryptedValue, recordKey});
						updated++;
						successKeys.add(recordKey);
						ConsoleUtils.getLogger().info("[DES迁移][成功] key=" + recordKey + " 已完成密钥迁移");
					} else {
						skipped++;
						skipKeys.add(recordKey);
						ConsoleUtils.getLogger().debug("[DES迁移][跳过] key=" + recordKey + " 未匹配3DES前缀");
					}
				} catch (Exception ex) {
					failed++;
					failKeys.add(recordKey);
					failReasons.add("EASI_DS_INFO key=" + recordKey + ": " + ex.getMessage());
					ConsoleUtils.getLogger().error("[DES迁移][失败] key=" + recordKey + ", 错误: " + ex.getMessage(), ex);
				}
			}
			ConsoleUtils.getLogger().info("[DES迁移] 处理完成，总数:" + total + "，成功:" + updated + "，跳过:" + skipped + "，失败:" + failed);
			if (!successKeys.isEmpty()) {
				ConsoleUtils.getLogger().info("[DES迁移] 成功key列表: " + successKeys);
			}
			if (!skipKeys.isEmpty()) {
				ConsoleUtils.getLogger().info("[DES迁移] 跳过key列表: " + skipKeys);
			}
			if (!failKeys.isEmpty()) {
				ConsoleUtils.getLogger().error("[DES迁移] 失败key列表: " + failKeys);
				for (String reason : failReasons) {
					ConsoleUtils.getLogger().error("[DES迁移] 失败原因: " + reason);
				}
			}
		} catch (SQLException e) {
			ConsoleUtils.getLogger().error("[DES迁移] 数据库操作异常: " + e.getMessage(), e);
		} catch (Exception e) {
			ConsoleUtils.getLogger().error("[DES迁移] 迁移过程异常: " + e.getMessage(), e);
		}
		
		try {
			List<JSONObject> configList = db.queryForList("select * from EASI_CONF", new Object[] {}, new JSONMapperImpl());
			int confTotal = 0, confUpdated = 0, confSkipped = 0, confFailed = 0;
			java.util.List<String> confSuccessKeys = new java.util.ArrayList<>();
			java.util.List<String> confSkipKeys = new java.util.ArrayList<>();
			java.util.List<String> confFailKeys = new java.util.ArrayList<>();
			java.util.List<String> confFailReasons = new java.util.ArrayList<>();
			for(JSONObject record : configList) {
				confTotal++;
				String confKey = record.getString("CONF_KEY");
				String confValue = record.getString("CONF_VALUE");
				try {
					if(StringUtils.notBlank(confValue)&&confValue.startsWith("3DES")) {
						String encryptedValue = confValue.replace("3DES_", "");
						String plainValue = DESUtil.getInstance(previousKey).decryptStr(encryptedValue);
						String newEncryptedValue = DESUtil.getInstance(currentKey).encryptStr(plainValue);
						db.executeUpdate("update EASI_CONF set CONF_VALUE = ? where CONF_KEY = ?", new Object[] {"3DES_" + newEncryptedValue, confKey});
						confUpdated++;
						confSuccessKeys.add(confKey);
						ConsoleUtils.getLogger().info("[DES迁移][CONF][成功] key=" + confKey + " 已完成密钥迁移");
					} else {
						confSkipped++;
						confSkipKeys.add(confKey);
						ConsoleUtils.getLogger().debug("[DES迁移][CONF][跳过] key=" + confKey + " 未匹配3DES前缀");
					}
				} catch (Exception ex) {
					confFailed++;
					confFailKeys.add(confKey);
					confFailReasons.add("EASI_CONF key=" + confKey + ": " + ex.getMessage());
					ConsoleUtils.getLogger().error("[DES迁移][CONF][失败] key=" + confKey + ", 错误: " + ex.getMessage(), ex);
				}
			}
			ConsoleUtils.getLogger().info("[DES迁移][CONF] 处理完成，总数:" + confTotal + "，成功:" + confUpdated + "，跳过:" + confSkipped + "，失败:" + confFailed);
			if (!confSuccessKeys.isEmpty()) {
				ConsoleUtils.getLogger().info("[DES迁移][CONF] 成功key列表: " + confSuccessKeys);
			}
			if (!confSkipKeys.isEmpty()) {
				ConsoleUtils.getLogger().info("[DES迁移][CONF] 跳过key列表: " + confSkipKeys);
			}
			if (!confFailKeys.isEmpty()) {
				ConsoleUtils.getLogger().error("[DES迁移][CONF] 失败key列表: " + confFailKeys);
				for (String reason : confFailReasons) {
					ConsoleUtils.getLogger().error("[DES迁移][CONF] 失败原因: " + reason);
				}
			}
		} catch (SQLException e) {
			ConsoleUtils.getLogger().error("[DES迁移][CONF] 数据库操作异常: " + e.getMessage(), e);
		}
		ConsoleUtils.getLogger().info("[DES迁移] DES密钥迁移流程结束。");
	}
	
	
	public static void migrateBySm4() {
		String currentKeyFilePath = Globals.BASE_DIR + File.separator + "sm4.current.key";
		String previousKeyFilePath = Globals.BASE_DIR + File.separator + "sm4.previous.key";
		File currentKeyFile = new File(currentKeyFilePath);
		File previousKeyFile = new File(previousKeyFilePath);
		if(!currentKeyFile.exists()) {
//			ConsoleUtils.getLogger().warn("[SM4迁移] 当前密钥文件不存在: " + currentKeyFilePath);
			return;
		}
		ConsoleUtils.getLogger().info("[SM4迁移] 开始SM4密钥迁移流程...");
		if(!previousKeyFile.exists()) {
			ConsoleUtils.getLogger().warn("[SM4迁移] 上一个密钥文件不存在: " + previousKeyFilePath);
			return;
		}
		String previousKey = loadSecretKey(previousKeyFilePath);
		String currentKey = loadSecretKey(currentKeyFilePath);
		if(StringUtils.isBlank(previousKey)) {
			ConsoleUtils.getLogger().warn("[SM4迁移] 上一个密钥内容为空: " + previousKeyFilePath);
			return;
		}
		if(StringUtils.isBlank(currentKey)) {
			ConsoleUtils.getLogger().warn("[SM4迁移] 当前密钥内容为空: " + currentKeyFilePath);
			return;
		}
		currentKeyFile.delete();
		previousKeyFile.delete();
		
		EasyQuery db = Constants.getDb();
		List<JSONObject> recordList;
		int total = 0, updated = 0, skipped = 0, failed = 0;
		java.util.List<String> successKeys = new java.util.ArrayList<>();
		java.util.List<String> skipKeys = new java.util.ArrayList<>();
		java.util.List<String> failKeys = new java.util.ArrayList<>();
		java.util.List<String> failReasons = new java.util.ArrayList<>();
		try {
			ConsoleUtils.getLogger().info("[SM4迁移] 查询EASI_DS_INFO数据...");
			recordList = db.queryForList("select * from EASI_DS_INFO", new Object[] {}, new JSONMapperImpl());
			ConsoleUtils.getLogger().info("[SM4迁移] 共查询到" + recordList.size() + "条记录");
			for(JSONObject record : recordList) {
				total++;
				String recordKey = record.getString("SYS_DS_NAME");
				String encryptedField = record.getString("PASSWORD");
				try {
					if(encryptedField != null && encryptedField.startsWith("S01_")){
						String encryptedValue = encryptedField.replace("S01_", "");
						String plainValue = SM4Util.decrypt(encryptedValue, previousKey);
						String newEncryptedValue = SM4Util.encrypt(plainValue, currentKey);
						db.executeUpdate("update EASI_DS_INFO set PASSWORD = ? where SYS_DS_NAME = ?", new Object[] {"S01_" + newEncryptedValue, recordKey});
						updated++;
						successKeys.add(recordKey);
						ConsoleUtils.getLogger().info("[SM4迁移][成功] key=" + recordKey + " 已完成密钥迁移");
					} else {
						skipped++;
						skipKeys.add(recordKey);
						ConsoleUtils.getLogger().debug("[SM4迁移][跳过] key=" + recordKey + " 未匹配S01_前缀");
					}
				} catch (Exception ex) {
					failed++;
					failKeys.add(recordKey);
					failReasons.add("EASI_DS_INFO key=" + recordKey + ": " + ex.getMessage());
					ConsoleUtils.getLogger().error("[SM4迁移][失败] key=" + recordKey + ", 错误: " + ex.getMessage(), ex);
				}
			}
			ConsoleUtils.getLogger().info("[SM4迁移] 处理完成，总数:" + total + "，成功:" + updated + "，跳过:" + skipped + "，失败:" + failed);
			if (!successKeys.isEmpty()) {
				ConsoleUtils.getLogger().info("[SM4迁移] 成功key列表: " + successKeys);
			}
			if (!skipKeys.isEmpty()) {
				ConsoleUtils.getLogger().info("[SM4迁移] 跳过key列表: " + skipKeys);
			}
			if (!failKeys.isEmpty()) {
				ConsoleUtils.getLogger().error("[SM4迁移] 失败key列表: " + failKeys);
				for (String reason : failReasons) {
					ConsoleUtils.getLogger().error("[SM4迁移] 失败原因: " + reason);
				}
			}
		} catch (SQLException e) {
			ConsoleUtils.getLogger().error("[SM4迁移] 数据库操作异常: " + e.getMessage(), e);
		} catch (Exception e) {
			ConsoleUtils.getLogger().error("[SM4迁移] 迁移过程异常: " + e.getMessage(), e);
		}
		
		try {
			List<JSONObject> configList = db.queryForList("select * from EASI_CONF", new Object[] {}, new JSONMapperImpl());
			int confTotal = 0, confUpdated = 0, confSkipped = 0, confFailed = 0;
			java.util.List<String> confSuccessKeys = new java.util.ArrayList<>();
			java.util.List<String> confSkipKeys = new java.util.ArrayList<>();
			java.util.List<String> confFailKeys = new java.util.ArrayList<>();
			java.util.List<String> confFailReasons = new java.util.ArrayList<>();
			for(JSONObject record : configList) {
				confTotal++;
				String confKey = record.getString("CONF_KEY");
				String confValue = record.getString("CONF_VALUE");
				try {
					if(StringUtils.notBlank(confValue)&&confValue.startsWith("S01_")) {
						String encryptedValue = confValue.replace("S01_", "");
						String plainValue = SM4Util.decrypt(encryptedValue, previousKey);
						String newEncryptedValue = SM4Util.encrypt(plainValue, currentKey);
						db.executeUpdate("update EASI_CONF set CONF_VALUE = ? where CONF_KEY = ?", new Object[] {"S01_" + newEncryptedValue, confKey});
						confUpdated++;
						confSuccessKeys.add(confKey);
						ConsoleUtils.getLogger().info("[SM4迁移][CONF][成功] key=" + confKey + " 已完成密钥迁移");
					} else {
						confSkipped++;
						confSkipKeys.add(confKey);
						ConsoleUtils.getLogger().debug("[SM4迁移][CONF][跳过] key=" + confKey + " 未匹配S01_前缀");
					}
				} catch (Exception ex) {
					confFailed++;
					confFailKeys.add(confKey);
					confFailReasons.add("EASI_CONF key=" + confKey + ": " + ex.getMessage());
					ConsoleUtils.getLogger().error("[SM4迁移][CONF][失败] key=" + confKey + ", 错误: " + ex.getMessage(), ex);
				}
			}
			ConsoleUtils.getLogger().info("[SM4迁移][CONF] 处理完成，总数:" + confTotal + "，成功:" + confUpdated + "，跳过:" + confSkipped + "，失败:" + confFailed);
			if (!confSuccessKeys.isEmpty()) {
				ConsoleUtils.getLogger().info("[SM4迁移][CONF] 成功key列表: " + confSuccessKeys);
			}
			if (!confSkipKeys.isEmpty()) {
				ConsoleUtils.getLogger().info("[SM4迁移][CONF] 跳过key列表: " + confSkipKeys);
			}
			if (!confFailKeys.isEmpty()) {
				ConsoleUtils.getLogger().error("[SM4迁移][CONF] 失败key列表: " + confFailKeys);
				for (String reason : confFailReasons) {
					ConsoleUtils.getLogger().error("[SM4迁移][CONF] 失败原因: " + reason);
				}
			}
		} catch (SQLException e) {
			ConsoleUtils.getLogger().error("[SM4迁移][CONF] 数据库操作异常: " + e.getMessage(), e);
		}
		
		try {
			List<JSONObject> appConfigList = db.queryForList("select * from EASI_APP_CONF", new Object[] {}, new JSONMapperImpl());
			int appTotal = 0, appUpdated = 0, appSkipped = 0, appFailed = 0;
			java.util.List<String> appSuccessKeys = new java.util.ArrayList<>();
			java.util.List<String> appSkipKeys = new java.util.ArrayList<>();
			java.util.List<String> appFailKeys = new java.util.ArrayList<>();
			java.util.List<String> appFailReasons = new java.util.ArrayList<>();
			for(JSONObject record : appConfigList) {
				appTotal++;
				String appId = record.getString("APP_ID");
				String itemKey = record.getString("ITEM_KEY");
				String itemValue = record.getString("ITEM_VALUE");
				try {
					if(StringUtils.notBlank(itemValue)&&itemValue.startsWith("S01_")) {
						String encryptedValue = itemValue.replace("S01_", "");
						String plainValue = SM4Util.decrypt(encryptedValue, previousKey);
						String newEncryptedValue = SM4Util.encrypt(plainValue, currentKey);
						db.executeUpdate("update EASI_APP_CONF set ITEM_VALUE = ? where ITEM_KEY = ? and APP_ID = ?", new Object[] {"S01_" + newEncryptedValue, itemKey,appId});
						appUpdated++;
						appSuccessKeys.add(itemKey + ", appId=" + appId);
						ConsoleUtils.getLogger().info("[SM4迁移][APP][成功] key=" + itemKey + ", appId=" + appId + " 已完成密钥迁移");
					} else {
						appSkipped++;
						appSkipKeys.add(itemKey + ", appId=" + appId);
						ConsoleUtils.getLogger().debug("[SM4迁移][APP][跳过] key=" + itemKey + ", appId=" + appId + " 未匹配S01_前缀");
					}
				} catch (Exception ex) {
					appFailed++;
					appFailKeys.add(itemKey + ", appId=" + appId);
					appFailReasons.add("EASI_APP_CONF key=" + itemKey + ", appId=" + appId + ": " + ex.getMessage());
					ConsoleUtils.getLogger().error("[SM4迁移][APP][失败] key=" + itemKey + ", appId=" + appId + ", 错误: " + ex.getMessage(), ex);
				}
			}
			ConsoleUtils.getLogger().info("[SM4迁移][APP] 处理完成，总数:" + appTotal + "，成功:" + appUpdated + "，跳过:" + appSkipped + "，失败:" + appFailed);
			if (!appSuccessKeys.isEmpty()) {
				ConsoleUtils.getLogger().info("[SM4迁移][APP] 成功key列表: " + appSuccessKeys);
			}
			if (!appSkipKeys.isEmpty()) {
				ConsoleUtils.getLogger().info("[SM4迁移][APP] 跳过key列表: " + appSkipKeys);
			}
			if (!appFailKeys.isEmpty()) {
				ConsoleUtils.getLogger().error("[SM4迁移][APP] 失败key列表: " + appFailKeys);
				for (String reason : appFailReasons) {
					ConsoleUtils.getLogger().error("[SM4迁移][APP] 失败原因: " + reason);
				}
			}
		} catch (SQLException e) {
			ConsoleUtils.getLogger().error("[SM4迁移][APP] 数据库操作异常: " + e.getMessage(), e);
		}
		ConsoleUtils.getLogger().info("[SM4迁移] SM4密钥迁移流程结束。");
	}
	
	public static void desToSm4() {
		String desKeyFilePath = Globals.BASE_DIR + File.separator + "des.current.key";
		String sm4KeyFilePath = Globals.BASE_DIR + File.separator + "sm4.current.key";
		File desKeyFile = new File(desKeyFilePath);
		File sm4KeyFile = new File(sm4KeyFilePath);
		if(!desKeyFile.exists()) {
//			ConsoleUtils.getLogger().warn("[DES->SM4迁移] DES密钥文件不存在: " + desKeyFilePath);
			return;
		}
		ConsoleUtils.getLogger().info("[DES->SM4迁移] 开始DES->SM4加密迁移流程...");
		if(!sm4KeyFile.exists()) {
			ConsoleUtils.getLogger().warn("[DES->SM4迁移] SM4密钥文件不存在: " + sm4KeyFilePath);
			return;
		}
		String desKey = loadSecretKey(desKeyFilePath);
		String sm4Key = loadSecretKey(sm4KeyFilePath);
		if(StringUtils.isBlank(desKey)) {
			ConsoleUtils.getLogger().warn("[DES->SM4迁移] DES密钥内容为空: " + desKeyFilePath);
			return;
		}
		if(StringUtils.isBlank(sm4Key)) {
			ConsoleUtils.getLogger().warn("[DES->SM4迁移] SM4密钥内容为空: " + sm4KeyFilePath);
			return;
		}
		desKeyFile.delete();
		sm4KeyFile.delete();

		EasyQuery db = Constants.getDb();
		int total = 0, updated = 0, skipped = 0, failed = 0;
		java.util.List<String> successKeys = new java.util.ArrayList<>();
		java.util.List<String> skipKeys = new java.util.ArrayList<>();
		java.util.List<String> failKeys = new java.util.ArrayList<>();
		java.util.List<String> failReasons = new java.util.ArrayList<>();

		// 处理EASI_DS_INFO
		try {
			ConsoleUtils.getLogger().info("[DES->SM4迁移] 查询EASI_DS_INFO数据...");
			List<JSONObject> recordList = db.queryForList("select * from EASI_DS_INFO", new Object[] {}, new JSONMapperImpl());
			ConsoleUtils.getLogger().info("[DES->SM4迁移] 共查询到" + recordList.size() + "条记录");
			for(JSONObject record : recordList) {
				total++;
				String recordKey = record.getString("SYS_DS_NAME");
				String encryptedField = record.getString("PASSWORD");
				try {
					if(StringUtils.notBlank(encryptedField) && encryptedField.startsWith("3DES_")){
						String encryptedValue = encryptedField.replace("3DES_", "");
						String plainValue = DESUtil.getInstance(desKey).decryptStr(encryptedValue);
						String newEncryptedValue = SM4Util.encrypt(plainValue, sm4Key);
						db.executeUpdate("update EASI_DS_INFO set PASSWORD = ? where SYS_DS_NAME = ?", new Object[] {"S01_" + newEncryptedValue, recordKey});
						updated++;
						successKeys.add("[3DES->SM4] " + recordKey);
						ConsoleUtils.getLogger().info("[DES->SM4迁移][成功] key=" + recordKey + " 3DES->SM4密钥迁移完成");
					} else if(StringUtils.notBlank(encryptedField) && encryptedField.startsWith("S01_")) {
						skipped++;
						skipKeys.add(recordKey);
						ConsoleUtils.getLogger().debug("[DES->SM4迁移][跳过] key=" + recordKey + " 已是SM4加密");
					} else if(StringUtils.notBlank(encryptedField)) {
						String newEncryptedValue = SM4Util.encrypt(encryptedField, sm4Key);
						db.executeUpdate("update EASI_DS_INFO set PASSWORD = ? where SYS_DS_NAME = ?", new Object[] {"S01_" + newEncryptedValue, recordKey});
						updated++;
						successKeys.add("[明文->SM4] " + recordKey);
						ConsoleUtils.getLogger().info("[DES->SM4迁移][成功] key=" + recordKey + " 明文->SM4加密完成");
					} else {
						skipped++;
						skipKeys.add(recordKey);
						ConsoleUtils.getLogger().debug("[DES->SM4迁移][跳过] key=" + recordKey + " 空密码");
					}
				} catch (Exception ex) {
					failed++;
					failKeys.add(recordKey);
					failReasons.add("EASI_DS_INFO key=" + recordKey + ": " + ex.getMessage());
					ConsoleUtils.getLogger().error("[DES->SM4迁移][失败] key=" + recordKey + ", 错误: " + ex.getMessage(), ex);
				}
			}
			ConsoleUtils.getLogger().info("[DES->SM4迁移] EASI_DS_INFO处理完成，总数:" + total + "，成功:" + updated + "，跳过:" + skipped + "，失败:" + failed);
			if (!successKeys.isEmpty()) {
				ConsoleUtils.getLogger().info("[DES->SM4迁移] 成功key列表: " + successKeys);
			}
			if (!skipKeys.isEmpty()) {
				ConsoleUtils.getLogger().info("[DES->SM4迁移] 跳过key列表: " + skipKeys);
			}
			if (!failKeys.isEmpty()) {
				ConsoleUtils.getLogger().error("[DES->SM4迁移] 失败key列表: " + failKeys);
				for (String reason : failReasons) {
					ConsoleUtils.getLogger().error("[DES->SM4迁移] 失败原因: " + reason);
				}
			}
		} catch (SQLException e) {
			ConsoleUtils.getLogger().error("[DES->SM4迁移] EASI_DS_INFO数据库操作异常: " + e.getMessage(), e);
		} catch (Exception e) {
			ConsoleUtils.getLogger().error("[DES->SM4迁移] EASI_DS_INFO迁移过程异常: " + e.getMessage(), e);
		}

		// 处理EASI_CONF
		try {
			List<JSONObject> configList = db.queryForList("select * from EASI_CONF", new Object[] {}, new JSONMapperImpl());
			int confTotal = 0, confUpdated = 0, confSkipped = 0, confFailed = 0;
			java.util.List<String> confSuccessKeys = new java.util.ArrayList<>();
			java.util.List<String> confSkipKeys = new java.util.ArrayList<>();
			java.util.List<String> confFailKeys = new java.util.ArrayList<>();
			java.util.List<String> confFailReasons = new java.util.ArrayList<>();
			for(JSONObject record : configList) {
				confTotal++;
				String confKey = record.getString("CONF_KEY");
				String confValue = record.getString("CONF_VALUE");
				try {
					if(StringUtils.notBlank(confValue) && confValue.startsWith("3DES_")) {
						String encryptedValue = confValue.replace("3DES_", "");
						String plainValue = DESUtil.getInstance(desKey).decryptStr(encryptedValue);
						String newEncryptedValue = SM4Util.encrypt(plainValue, sm4Key);
						db.executeUpdate("update EASI_CONF set CONF_VALUE = ? where CONF_KEY = ?", new Object[] {"S01_" + newEncryptedValue, confKey});
						confUpdated++;
						confSuccessKeys.add("[3DES->SM4] " + confKey);
						ConsoleUtils.getLogger().info("[DES->SM4迁移][CONF][成功] key=" + confKey + " 3DES->SM4密钥迁移完成");
					} else if(StringUtils.notBlank(confValue) && confValue.startsWith("S01_")) {
						confSkipped++;
						confSkipKeys.add(confKey);
						ConsoleUtils.getLogger().debug("[DES->SM4迁移][CONF][跳过] key=" + confKey + " 已是SM4加密");
					} else if(StringUtils.notBlank(confValue)) {
						String newEncryptedValue = SM4Util.encrypt(confValue, sm4Key);
						db.executeUpdate("update EASI_CONF set CONF_VALUE = ? where CONF_KEY = ?", new Object[] {"S01_" + newEncryptedValue, confKey});
						confUpdated++;
						confSuccessKeys.add("[明文->SM4] " + confKey);
						ConsoleUtils.getLogger().info("[DES->SM4迁移][CONF][成功] key=" + confKey + " 明文->SM4加密完成");
					} else {
						confSkipped++;
						confSkipKeys.add(confKey);
						ConsoleUtils.getLogger().debug("[DES->SM4迁移][CONF][跳过] key=" + confKey + " 空配置");
					}
				} catch (Exception ex) {
					confFailed++;
					confFailKeys.add(confKey);
					confFailReasons.add("EASI_CONF key=" + confKey + ": " + ex.getMessage());
					ConsoleUtils.getLogger().error("[DES->SM4迁移][CONF][失败] key=" + confKey + ", 错误: " + ex.getMessage(), ex);
				}
			}
			ConsoleUtils.getLogger().info("[DES->SM4迁移][CONF] 处理完成，总数:" + confTotal + "，成功:" + confUpdated + "，跳过:" + confSkipped + "，失败:" + confFailed);
			if (!confSuccessKeys.isEmpty()) {
				ConsoleUtils.getLogger().info("[DES->SM4迁移][CONF] 成功key列表: " + confSuccessKeys);
			}
			if (!confSkipKeys.isEmpty()) {
				ConsoleUtils.getLogger().info("[DES->SM4迁移][CONF] 跳过key列表: " + confSkipKeys);
			}
			if (!confFailKeys.isEmpty()) {
				ConsoleUtils.getLogger().error("[DES->SM4迁移][CONF] 失败key列表: " + confFailKeys);
				for (String reason : confFailReasons) {
					ConsoleUtils.getLogger().error("[DES->SM4迁移][CONF] 失败原因: " + reason);
				}
			}
		} catch (SQLException e) {
			ConsoleUtils.getLogger().error("[DES->SM4迁移][CONF] 数据库操作异常: " + e.getMessage(), e);
		}

		// 处理EASI_APP_CONF
		try {
			List<JSONObject> appConfigList = db.queryForList("select * from EASI_APP_CONF", new Object[] {}, new JSONMapperImpl());
			int appTotal = 0, appUpdated = 0, appSkipped = 0, appFailed = 0;
			java.util.List<String> appSuccessKeys = new java.util.ArrayList<>();
			java.util.List<String> appSkipKeys = new java.util.ArrayList<>();
			java.util.List<String> appFailKeys = new java.util.ArrayList<>();
			java.util.List<String> appFailReasons = new java.util.ArrayList<>();
			for(JSONObject record : appConfigList) {
				appTotal++;
				String appId = record.getString("APP_ID");
				String itemKey = record.getString("ITEM_KEY");
				String itemValue = record.getString("ITEM_VALUE");
				try {
					if(StringUtils.notBlank(itemValue) && itemValue.startsWith("3DES_")) {
						String encryptedValue = itemValue.replace("3DES_", "");
						String plainValue = DESUtil.getInstance(desKey).decryptStr(encryptedValue);
						String newEncryptedValue = SM4Util.encrypt(plainValue, sm4Key);
						db.executeUpdate("update EASI_APP_CONF set ITEM_VALUE = ? where ITEM_KEY = ? and APP_ID = ?", new Object[] {"S01_" + newEncryptedValue, itemKey, appId});
						appUpdated++;
						appSuccessKeys.add("[3DES->SM4] " + itemKey + ", appId=" + appId);
						ConsoleUtils.getLogger().info("[DES->SM4迁移][APP][成功] key=" + itemKey + ", appId=" + appId + " 3DES->SM4密钥迁移完成");
					} else if(StringUtils.notBlank(itemValue) && itemValue.startsWith("S01_")) {
						appSkipped++;
						appSkipKeys.add(itemKey + ", appId=" + appId);
						ConsoleUtils.getLogger().debug("[DES->SM4迁移][APP][跳过] key=" + itemKey + ", appId=" + appId + " 已是SM4加密");
					} else if(StringUtils.notBlank(itemValue)) {
						String newEncryptedValue = SM4Util.encrypt(itemValue, sm4Key);
						db.executeUpdate("update EASI_APP_CONF set ITEM_VALUE = ? where ITEM_KEY = ? and APP_ID = ?", new Object[] {"S01_" + newEncryptedValue, itemKey, appId});
						appUpdated++;
						appSuccessKeys.add("[明文->SM4] " + itemKey + ", appId=" + appId);
						ConsoleUtils.getLogger().info("[DES->SM4迁移][APP][成功] key=" + itemKey + ", appId=" + appId + " 明文->SM4加密完成");
					} else {
						appSkipped++;
						appSkipKeys.add(itemKey + ", appId=" + appId);
						ConsoleUtils.getLogger().debug("[DES->SM4迁移][APP][跳过] key=" + itemKey + ", appId=" + appId + " 空应用配置");
					}
				} catch (Exception ex) {
					appFailed++;
					appFailKeys.add(itemKey + ", appId=" + appId);
					appFailReasons.add("EASI_APP_CONF key=" + itemKey + ", appId=" + appId + ": " + ex.getMessage());
					ConsoleUtils.getLogger().error("[DES->SM4迁移][APP][失败] key=" + itemKey + ", appId=" + appId + ", 错误: " + ex.getMessage(), ex);
				}
			}
			ConsoleUtils.getLogger().info("[DES->SM4迁移][APP] 处理完成，总数:" + appTotal + "，成功:" + appUpdated + "，跳过:" + appSkipped + "，失败:" + appFailed);
			if (!appSuccessKeys.isEmpty()) {
				ConsoleUtils.getLogger().info("[DES->SM4迁移][APP] 成功key列表: " + appSuccessKeys);
			}
			if (!appSkipKeys.isEmpty()) {
				ConsoleUtils.getLogger().info("[DES->SM4迁移][APP] 跳过key列表: " + appSkipKeys);
			}
			if (!appFailKeys.isEmpty()) {
				ConsoleUtils.getLogger().error("[DES->SM4迁移][APP] 失败key列表: " + appFailKeys);
				for (String reason : appFailReasons) {
					ConsoleUtils.getLogger().error("[DES->SM4迁移][APP] 失败原因: " + reason);
				}
			}
		} catch (SQLException e) {
			ConsoleUtils.getLogger().error("[DES->SM4迁移][APP] 数据库操作异常: " + e.getMessage(), e);
		}
		ConsoleUtils.getLogger().info("[DES->SM4迁移] DES->SM4加密迁移流程结束。");
	}
	
	private static String loadSecretKey(String filePath) {
		if (filePath != null && !filePath.trim().isEmpty()) {
			Path path = Paths.get(filePath);
			if (Files.exists(path)) {
				try {
					byte[] bytes = Files.readAllBytes(path);
					String content = new String(bytes, StandardCharsets.UTF_8).replaceAll("\\s+", "").trim();

					// 去除 UTF-8 BOM（EF BB BF）
					if (content.startsWith("\uFEFF")) {
						content = content.substring(1);
					}

					return content;
				} catch (Exception e) {
					ConsoleUtils.getLogger().error("读取秘钥文件失败: " + filePath + ", 错误: " + e.getMessage(), e);
				}
			} else {
				ConsoleUtils.getLogger().error("秘钥文件路径不存在: " + path.toAbsolutePath());
			}
		}
		return "";
	}

}
