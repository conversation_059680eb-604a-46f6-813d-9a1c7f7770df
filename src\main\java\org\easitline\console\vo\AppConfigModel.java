package org.easitline.console.vo;

/**
 * 应用配置对象
 * 
--应用配置信息
create table EASI_APP_CONF (
   APP_ID               varchar(100)                   not null,
   ITEM_KEY             VARCHAR(100)                   ,
   ITEM_NAME            VARCHAR(100)                   ,
   ITEM_DESC            VARCHAR(500)                   ,
   JSON_STRING          VARCHAR(500)                   ,  --用于通过JOSN来配置选择内容，如果JSON内容存在，贼ITEM_TYPE没有用
   ITEM_VALUE           VARCHAR(500)                   ,  
   ITEM_TYPE            VARCHAR(100)                   ,  --取值：number、string、date、time、datetime
  PRIMARY KEY(APP_ID, ITEM_KEY)
)
 * <AUTHOR>
 * @date   
 */
public class AppConfigModel {

	private String appId;     //应用标识
	private String itemName;  //配置名称
	private String itemKey;   //配置键
	private String itemValue; //配置值
	private String itemDesc;  //配置描述
	private String itemType;  //配置项类型
	
	public String getAppId() {
		return appId;
	}
	public void setAppId(String appId) {
		this.appId = appId;
	}
	public String getItemName() {
		return itemName;
	}
	public void setItemName(String itemName) {
		this.itemName = itemName;
	}
	public String getItemValue() {
		return itemValue;
	}
	public void setItemValue(String itemValue) {
		this.itemValue = itemValue;
	}
	public String getItemDesc() {
		return itemDesc;
	}
	public void setItemDesc(String itemDesc) {
		this.itemDesc = itemDesc;
	}
	public String getItemType() {
		return itemType;
	}
	public void setItemType(String itemType) {
		this.itemType = itemType;
	}
	public String getItemKey() {
		return itemKey;
	}
	public void setItemKey(String itemKey) {
		this.itemKey = itemKey;
	}
	
}
