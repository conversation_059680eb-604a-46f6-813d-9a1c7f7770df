package org.easitline.console.utils;

import java.util.ArrayList;
import java.util.List;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.console.base.Constants;

public class MenuDataUtil {
	
	private static class Holder{
		private static MenuDataUtil service=new MenuDataUtil();
	}
	public static MenuDataUtil getService(){
		return Holder.service;
	}
	
	public MenuDataUtil() {
		
	}
	
	/**
	 * 
	 * @param roleId
	 * @return
	 */
	public List<MenuObj> getRoleRes(String roleId){
		String profileActive = ConsoleUtils.getProfileActive();
		List<MenuObj> list = getAllRes();
		if("prod".equals(profileActive)) {
			list = removeRes(list,"app-config");
		}
		if("9".equals(roleId)) {
			return onlyRes(list,"platform_log","log1","log2","log3","log4","log5","log6");
		}else if("4".equals(roleId)) {
			return list;
		}else {
			return removeRes(list,"service_command");
		}
	}
	
	private List<MenuObj> onlyRes(List<MenuObj> list,String... resIds) {
		List<MenuObj> newList = new ArrayList<MenuObj>();
		for(MenuObj obj:list) {
			String resId = obj.getResId();
			for(String str:resIds) {
				if(resId.equals(str)) {
					newList.add(obj);
				}
			}
		}
		return newList;
	}
	
	private List<MenuObj> removeRes(List<MenuObj> list,String... resIds) {
		List<MenuObj> newList = new ArrayList<MenuObj>();
		for(MenuObj obj:list) {
			String resId = obj.getResId();
			for(String str:resIds) {
				if(!resId.equals(str)) {
					newList.add(obj);
				}
			}
		}
		return newList;
	}
	
	@SuppressWarnings("unchecked")
	public List<MenuObj> getAllRes() {
		List<MenuObj> list = new ArrayList<MenuObj>();
		Document doc = null;
		try {
			String path = Constants.serverBasePath+"/META-INF/console_menu.xml";
			String xml = FileKit.readToString(path);
			doc = DocumentHelper.parseText(xml);
			Element rootEle = doc.getRootElement();
			List<Element> resList = rootEle.elements();
			for(Element el:resList) {
				String state = el.attributeValue("state");
				if(!"9".equals(state)) {
					list.add(new MenuObj(el.attributeValue("id"), "", el.attributeValue("name"), el.attributeValue("roleId")));
					List<Element> childResList = el.elements();
					if(childResList!=null) {
						for(Element sonEl:childResList) {
							String _state = sonEl.attributeValue("state");
							if(!"9".equals(_state)) {
								list.add(new MenuObj(sonEl.attributeValue("id"), el.attributeValue("id"), sonEl.attributeValue("url"),sonEl.attributeValue("name"), el.attributeValue("roleId")));
							}
						}
					}
				}
			}
			
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		
		if(doc==null) {
			list.add(new MenuObj("server_conf", "", "服务配置","1,2"));
			list.add(new MenuObj("datasource", "server_conf", "datasource/list.jsp","数据源管理","1,2"));
			list.add(new MenuObj("application", "server_conf", "application/list.jsp","应用管理","1,2"));
			
			
			list.add(new MenuObj("system_conf", "", "系统配置","1,2"));
			list.add(new MenuObj("config", "system_conf", "server/server-config.jsp","平台参数配置","1,2"));
			list.add(new MenuObj("config8", "system_conf", "server/config-mgr.jsp","应用参数配置","1,2"));
			list.add(new MenuObj("config1", "system_conf", "server/log-conf.jsp","日志参数配置","1,2"));
			list.add(new MenuObj("config2", "system_conf", "server/security-conf.jsp","登录安全配置","1,2"));
			list.add(new MenuObj("config3", "system_conf", "server/cache-adv-conf.jsp","缓存服务配置","1,2"));
			list.add(new MenuObj("config4", "system_conf", "server/mq-info.jsp","MQ服务配置","1,2"));
			list.add(new MenuObj("config5", "system_conf", "node/node-config.jsp","服务节点配置","1"));
			list.add(new MenuObj("config7", "system_conf", "oss/oss-config.jsp","OSS存储配置","1"));
			list.add(new MenuObj("config6", "system_conf", "job/job-list.jsp","定时任务配置","1"));
			list.add(new MenuObj("service_command", "system_conf", "server/server-mgr.jsp","服务命令","4"));
			
			
			list.add(new MenuObj("platform_log", "", "平台日志","1,2,9"));
			list.add(new MenuObj("log1", "platform_log", "logger/operate-log-list.jsp","操作日志查询","1,9"));
			list.add(new MenuObj("log2", "platform_log", "logger/login-log-list.jsp","登录日志查询","1,9"));
			list.add(new MenuObj("log3", "platform_log", "logger/upgrade-log-list.jsp","升级日志查询","1,9"));
			list.add(new MenuObj("log4", "platform_log", "logger/logger-list.jsp","应用日志下载","1,9"));
			list.add(new MenuObj("log5", "platform_log", "logger/tomcat-logger.jsp","系统日志下载","1,9"));
			list.add(new MenuObj("log6", "platform_log", "/easitline-finder/sso.jsp?_blank","在线日志查看","1,9"));
			
			list.add(new MenuObj("sys_monitor", "", "系统监控","1,2"));
			list.add(new MenuObj("monitor1", "sys_monitor", "monitor/app-monitor.jsp","Mars应用监控","1,2"));
			list.add(new MenuObj("monitor2", "sys_monitor", Constants.getContextPath()+"/servlet/monitor/druid/index.html?_blank","数据源监控","1"));
			list.add(new MenuObj("monitor3", "sys_monitor", "server/runtimeInfo.jsp","JVM环境变量信息","1"));
			list.add(new MenuObj("monitor4", "sys_monitor",  Constants.getContextPath()+"/pages/server/java-threads.jsp","线程监控","1"));
			list.add(new MenuObj("monitor5", "sys_monitor", "soa/service-list.jsp","SOA服务监控","1"));
			list.add(new MenuObj("monitor6", "sys_monitor",  Constants.getContextPath()+"/pages/job/joblog-list.jsp","定时任务监控","1"));
			
		}
		return list;
		
	}
	
	
	public class MenuObj{
		public MenuObj(String resId,String resUrl,String resName,String roleId) {
			this.setResId(resId);
			this.setResUrl(resUrl);
			this.setResName(resName);
			this.setRoleIds(roleId);
			this.setpResId("0");
		}
		
		public MenuObj(String resId,String pResId,String resUrl,String resName,String roleId) {
			this.setResId(resId);
			this.setpResId(pResId);
			this.setResUrl(resUrl);
			this.setResName(resName);
			this.setRoleIds(roleId);
		}
		private String resId;
		private String pResId;
		private String resUrl;
		private String resName;
		private String roleIds;
		public String getResId() {
			return resId;
		}
		public void setResId(String resId) {
			this.resId = resId;
		}
		public String getResUrl() {
			return resUrl;
		}
		public void setResUrl(String resUrl) {
			this.resUrl = resUrl;
		}
		public String getResName() {
			return resName;
		}
		public void setResName(String resName) {
			this.resName = resName;
		}
		public String getpResId() {
			return pResId;
		}
		public void setpResId(String pResId) {
			this.pResId = pResId;
		}

		public String getRoleIds() {
			return roleIds;
		}

		public void setRoleIds(String roleIds) {
			this.roleIds = roleIds;
		}
		
		
	}

}
