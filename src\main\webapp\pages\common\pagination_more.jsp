<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<div style="display: inline-block;position: relative;margin-left: 15px;">
		<input type="hidden" name="pageType"  value="2">
		<input type="hidden" name="pageIndex" value="1">
		 <select name="pageSize" class="form-control input-sm" style="width: 80px;display: inline-block;height: 28px;padding: 2px 5px">
				<c:choose>
					<c:when test="${empty param.pageSizes }">
								<option value="5">5条/页</option>
								<option value="10"  selected="selected">10条/页</option>
								<option value="15">15条/页</option>
								<option value="25">25条/页</option>
								<option value="50">50条/页</option>
								<option value="100">100条/页</option>
					</c:when>
					<c:otherwise>
							<c:forEach items="${fn:split(param.pageSizes,',')}"  var="val" varStatus="vs"  >
		                           <option value="${val}">${val }</option>
		                    </c:forEach>
					</c:otherwise>
				</c:choose>
		</select>
		<button type="button" class="btn btn-default btn-sm pageMore previous" data-page-num="-1">上一页</button>
		<button type="button" class="btn btn-default btn-sm pageMore next" data-page-num="1">下一页</button>
</div>

