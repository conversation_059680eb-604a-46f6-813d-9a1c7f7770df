<%@ page pageEncoding="UTF-8"%>
<style type="text/css">
	.layui-form-label{width: 350px;}
	.layui-input-block{margin-left: 380px;}
	.layui-form-item .layui-input-inline{width: 400px;}
</style>
<div>
	<fieldset class="layui-elem-field layui-field-title">
		<legend>OSS配置</legend>
	</fieldset>
	<form class="layui-form" id="easyform" autocomplete="off">
	  <div class="layui-form-item">
	    <label class="layui-form-label">系统附件存储方式/OSS_SWITCH</label>
	     <div class="layui-input-inline">
	      <select name="OSS_SWITCH" class="layui-select">
	        <option value="">请选择</option>
	        <option value="1"  selected="selected">服务器本机存储</option>
	        <option value="2">其它mars服务器存储</option>
	        <option value="3">通过自建minio存储</option>
	        <option value="4">FTP存储</option>
	        <option value="5">移动云S3</option>
	        <option value="99">定制类OSS方式存储</option>
	      </select>
	       <div class="layui-form-mid layui-word-aux">
			    系统附件存储方式 1-存储在mars node服务器上(或挂载目录里) 2-存储在其他mars服务器上(必须是不通服务器的mars，附件目录配置按node节点cc-base配置) 3-通过自建minio存储 4-FTP存储，端口必须是21 5-移动云S3   99-使用其它OSS方式(需要定制服务)
		  </div>
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">系统附件存储其它服务器的地址/OSS_MARS_SERVER</label>
	    <div class="layui-input-block">
	      <textarea name="OSS_MARS_SERVER" class="layui-textarea" placeholder="系统附件无法存储在本机上，配置其它服务的ip，对于minio存服务器地址；对于mars服务器存储存cc-api地址，不能以/结尾,如http://localhost:9060"></textarea>
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">系统附件存储其它服务器的账号/OSS_MARS_SERVER_ACC</label>
	    <div class="layui-input-block">
	      <input type="text" name="OSS_MARS_SERVER_ACC" class="layui-input" placeholder="附件存储到其它服务器时，接口调用的服务器账号，按情况设置;minio需要配置">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">系统附件存储其它服务器的密码/OSS_MARS_SERVER_PWD</label>
	    <div class="layui-input-block">
	      <input type="password" name="OSS_MARS_SERVER_PWD" class="layui-input" placeholder="附件存储到其它服务器时，接口调用的服务器密码，按情况设置;minio需要配置">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">系统附件存储的桶/OSS_MARS_SERVER_BUCKET</label>
	    <div class="layui-input-block">
	      <input type="text" name="OSS_MARS_SERVER_BUCKET" class="layui-input" placeholder="系统附件存储的桶,针对s3类存储,当配置后，所有附件全部存入到该桶，否则按内置的标识自动创建桶">
	    </div>
	  </div>
	  
	 <fieldset class="layui-elem-field layui-field-title">
		<legend>附件设置</legend>
	</fieldset>
	
	 <div class="layui-form-item">
	    <label class="layui-form-label">附件上传,文件地址存放地址,根目录/ATTACHMENT_ROOT_PAHT</label>
	    <div class="layui-input-block">
	      <input type="text" name="ATTACHMENT_ROOT_PAHT" value="/home/<USER>" class="layui-input" placeholder="如/home/<USER>">
	    </div>
	  </div>
	 <div class="layui-form-item">
	    <label class="layui-form-label">单个附件最大值(单位M)/ATTACHMENT_MAX_LEN</label>
	    <div class="layui-input-block">
	      <input type="text" name="ATTACHMENT_MAX_LEN" value="50" class="layui-input" placeholder="单个附件最大值(单位M)">
	    </div>
	  </div>
	 <div class="layui-form-item">
	    <label class="layui-form-label">业务对象附件最大值/ATTACHMENT_MAX_SIZE</label>
	    <div class="layui-input-block">
	      <input type="text" name="ATTACHMENT_MAX_SIZE" value="10" class="layui-input" placeholder="如每个工单最多上传多少个附件,如 ：10">
	    </div>
	  </div>
	 <div class="layui-form-item">
	    <label class="layui-form-label">附件格式/ATTACHMENT_SUFFIX</label>
	    <div class="layui-input-block">
	      <input type="text" name="ATTACHMENT_SUFFIX" value="jpg;jpeg;xls;txt;word;mp4;png;xlsx;docx;zip;pdf;AVI;FLV;mov;FLV;" class="layui-input" placeholder="如 ：jpg;jpeg;xls;txt;">
	    </div>
	  </div>
	 <div class="layui-form-item">
	    <label class="layui-form-label">在线图库附件格式/ATTACHMENT_ONLINE_SUFFIX</label>
	    <div class="layui-input-block">
	      <input type="text" name="ATTACHMENT_ONLINE_SUFFIX" value="jpg;jpeg;png;" class="layui-input" placeholder="如 ：jpg;jpeg;png;">
	    </div>
	  </div>
	 <div class="layui-form-item">
	    <label class="layui-form-label">是否允许未登录下载附件/LOGIN_DOWNLOAD_ATTR</label>
	    <div class="layui-input-block">
	      <input type="text" name="LOGIN_DOWNLOAD_ATTR" value="Y" class="layui-input" placeholder="是否允许未登录下载附件,Y-是，N-否,默认Y; 开启后，如需下载，要么已登录，要么传入签名串">
	    </div>
	  </div>
	 <div class="layui-form-item">
	    <label class="layui-form-label">是否记录用户下载附件的操作日志/SAVE_DOWNLOAD_ATTR_LOG</label>
	    <div class="layui-input-block">
	      <input type="text" name="SAVE_DOWNLOAD_ATTR_LOG" value="N" class="layui-input" placeholder="开启后会导致操作日志表里的数据很大，如无附件特殊统计要求，不建议开启,Y-是，N-否,默认Y">
	    </div>
	  </div>
	 <div class="layui-form-item">
	    <label class="layui-form-label">操作日志写入到全文索引/ADD_LOG_TO_SOLR</label>
	    <div class="layui-input-block">
	      <input type="text" name="ADD_LOG_TO_SOLR" value="N" class="layui-input" placeholder="需要安装全文索引，如果是solr，需要在9059新增新的仓库coreLog；Es类似">
	    </div>
	  </div>
	 <div class="layui-form-item">
	    <label class="layui-form-label">openoffice软件安装目录/OPEN_OFFICE_HOME</label>
	    <div class="layui-input-block">
	      <input type="text" name="OPEN_OFFICE_HOME" value="/opt/openoffice4" class="layui-input" placeholder="openoffice软件安装目录，用于上传附件时，生成pdf文件，方便预览">
	    </div>
	  </div>
	 <div class="layui-form-item">
	    <label class="layui-form-label">openoffice软件安装端口/OPEN_OFFICE_PORT</label>
	    <div class="layui-input-block">
	      <input type="text" name="OPEN_OFFICE_PORT" value="8100" class="layui-input" placeholder="openoffice软件安装端口">
	    </div>
	  </div>
	
	
	  <div class="layui-form-item">
	    <div class="layui-input-block">
	      <button type="button" class="layui-btn layui-btn-small layui-btn-normal" lay-submit lay-filter="submitBtn" style="padding:0 20px"> 保存 </button>
	    </div>
	  </div>
	</form>
</div>


<script type="text/javascript">

  var names = "OSS_SWITCH,OSS_MARS_SERVER,OSS_MARS_SERVER_ACC,OSS_MARS_SERVER_PWD,OSS_MARS_SERVER_BUCKET,ATTACHMENT_ROOT_PAHT,ATTACHMENT_MAX_LEN,ATTACHMENT_MAX_SIZE,ATTACHMENT_SUFFIX,ATTACHMENT_ONLINE_SUFFIX,LOGIN_DOWNLOAD_ATTR,SAVE_DOWNLOAD_ATTR_LOG,ADD_LOG_TO_SOLR,OPEN_OFFICE_HOME,OPEN_OFFICE_PORT";
  $(function(){
		layui.use('form', function(){
			getConf();
	         var layuiform = layui.form;
	         layuiform.render('select');
	         layuiform.render();
			 layuiform.on('submit(submitBtn)', function(data){
				 doExcute(data.field);
			 });
	     });
	});
	
	function getConf(){
		ajax.remoteCall("${ctxPath}/servlet/oss?action=getConf",{names:names}, function(result) { 
			var encryptStr = result.data;
			var data = JSON.parse(aesDecrypt(encryptStr));
			if(data){
				fillRecord(data);
				layui.use('form', function(){
			         var layuiform = layui.form;
			         layuiform.render('select');
			         layuiform.render('radio');
			         layuiform.render();
			     });
			}
		});
	}
	function doExcute(datas) {
		var json = form.getJSONObject("easyform");
		json['names'] = names;
		var jsonStr = JSON.stringify(json); 
		var dataStr = aesEncrypt(jsonStr);
		ajax.remoteCall("${ctxPath}/servlet/oss?action=saveConf",{encryptStr:dataStr},function(result) { 	
			if(result.state==1){
				layer.msg(result.msg,{icon: 1,time:800},function(){
					layer.closeAll();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
</script>


