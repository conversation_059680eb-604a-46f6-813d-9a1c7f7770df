package org.easitline.console.job;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.service.IService;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.console.base.Constants;
import org.easitline.console.vo.AppJobModel;
import org.easitline.console.vo.JobLogModel;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.PersistJobDataAfterExecution;

import com.alibaba.fastjson.JSONObject;

/**
 * cron调度任务类(并行)
 */
@PersistJobDataAfterExecution
@DisallowConcurrentExecution
public class QuartzStatefulJob  implements Job {
	
	private Logger logger = LogEngine.getLogger("easitline-job");
	private static JobManager jobManager = new JobManager();
	private static JobLogManager jobLogManager = new JobLogManager();

	@Override
	public void execute(JobExecutionContext context) throws JobExecutionException {
		
		JobKey jobKey = context.getJobDetail().getKey();
		String jobId = jobKey.getName();   //jobName为数据库表EASI_JOB_STEP的ID
		JobLogModel jobLog = null;
		Date nextFireTime = context.getNextFireTime();
		String nextTime = EasyCalendar.newInstance(nextFireTime).getDateTime("-");
		
		try {
			
			String isJobServer = ServerContext.getProperties("G_JOB_SERVER", "false");
			if("false".equals(isJobServer)){
				logger.info("调度模块未启用");
				return;
			}
			
			AppJobModel appJob = jobManager.getJobById(jobId);
			if(null == appJob) {
				logger.info("调度任务不存在");
				return;
			}
			
			//超时的不执行
			boolean isOverEndTimeFlag = isOverEndTime(appJob.getEndTime());
			
			if(isOverEndTimeFlag) {
			   return;
			}

			//过期的任务的不执行并停止调度任务 
			boolean isOverEndDateFlag = isOverEndDate(appJob.getEndDate());
			
			if(isOverEndDateFlag) {
				QuartzSchedulerEngine.pauseJob(appJob.getJobId(), appJob.getAppName());  //从schedule对象中去掉job
				jobManager.updateJobState(jobId, Constants.JobState.PAUSE.getKey());   //更新调度任务状态
				return;
			}

			 //设置为调度中
			jobLog = jobLogManager.setDoingStateBeforeExec(jobId); 
			
            //调用服务
			JSONObject  jsonIn = new JSONObject();
			jsonIn.put("param", "test");
			IService service = ServiceContext.getService(appJob.getServiceId());
			service.invoke(jsonIn);
			//执行子任务
			/*String dependJobs = appJob.getDependJobs();
			if(!StringUtils.isBlank(dependJobs)) {
				String[] jobAry = dependJobs.split("\\|");
				for(String serviceId:jobAry) {
					 service = ServiceContext.getService(serviceId);
					 result = service.invoke(jsonIn);
					 logger.info("调用成功，服务返回结果：" + result);
				}
			}*/

			//更新状态为成功
			jobLogManager.updateSuccessState(jobLog);
			
			jobManager.updateSuccessState(jobId, nextTime);
            
		} catch (Exception e) {
			logger.error("QuartzStatefulJob.execute出错，原因：" + e.getMessage(),e);
			//增加失败日志
			jobLogManager.updateFailState(jobLog, e.getMessage());
			jobManager.updateFailState(jobId, nextTime);
		} 
	}
	
	/**
	 * 是否已超时
	 * @return
	 */
	public boolean isOverEndTime(String endTime) { 
		
		boolean isOverEndTimeFlag = false;
		
		if(null == endTime || "".equals(endTime)) return isOverEndTimeFlag;
		
		Calendar calendar = Calendar.getInstance();
		int curHour = calendar.get(Calendar.HOUR_OF_DAY);
		int curMinute = calendar.get(Calendar.MINUTE);
		
		String[] endTimeAry = endTime.split(":");
		int hour = Integer.valueOf(endTimeAry[0]);
		int minute = Integer.valueOf(endTimeAry[1]);
		
		if(hour < curHour) {
			isOverEndTimeFlag = true;
		} else if(hour == curHour) {
			if(minute < curMinute) {
				isOverEndTimeFlag = true;
			}
		}
		
		return isOverEndTimeFlag;
	}
	
	/**
	 * 是否已过期
	 * @param endDate
	 * @return
	 */
	public boolean isOverEndDate(String endDate) {
		
		boolean isOverEndDateFlag = false;
		Date now  = new Date();
		Date deadline = null;
		
		if(null == endDate || "".equals(endDate)) return isOverEndDateFlag;
		
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		try {
			deadline = sdf.parse(endDate);
			
			if(deadline.getTime() < now.getTime()) {
				isOverEndDateFlag = true;
			}
		} catch (ParseException e) {
			e.printStackTrace();
		}

		return isOverEndDateFlag;
	}

}
