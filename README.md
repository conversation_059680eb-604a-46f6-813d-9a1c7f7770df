# easitline-console - 高性能Web控制台

## 目录
- [项目概述](#项目概述)
- [技术栈](#技术栈)
- [功能特性](#功能特性)
- [快速开始](#快速开始)
- [构建部署](#构建部署)
- [开发规范](#开发规范)

## 项目概述
基于Java的高性能Web管理系统，提供以下核心能力：
✅ 多租户用户权限体系  
✅ 应用全生命周期管理  
✅ 分布式任务调度引擎  
✅ 可视化日志分析平台  
✅ 实时系统健康监控看板

## 技术栈
| 类别       | 技术选型                                                                 |
|------------|--------------------------------------------------------------------------|
| **后端**   | JDK8 + Spring MVC + MyBatis + Shiro                                      |
| **前端**   | JSP + LayUI 2.7 + ECharts 5.3                                           |
| **数据库** | Oracle 12c/MySQL 8.0 (多数据源支持)                                      |
| **中间件** | Redis 6.0（缓存管理） + Quartz 2.3（任务调度）                           |
| **构建**   | Maven 3.6 + Tomcat 8.0.53                                                |

## 功能特性
### 用户管理
- RBAC权限模型
- 组织架构管理
- 登录审计日志

### 应用管理
- WAR包热部署
- JVM参数监控
- 线程池可视化

### 任务调度
- Cron表达式配置
- 任务执行历史追溯
- 失败任务重试机制

### 系统监控
- 实时服务器状态仪表盘
- 慢SQL分析报告
- 请求链路追踪

## 快速开始
1. 初始化数据库：