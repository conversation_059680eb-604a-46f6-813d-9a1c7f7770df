<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://www.easitline.org/easitline-taglib" prefix="EasyTag"%>
<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<title>Content</title>
		 <LINK rel="Shortcut Icon" href="${ctxPath}/favicon.ico" />
		<link rel="stylesheet" href="${staticPath}/lib/layui/css/layui.css">
		<link rel="stylesheet" href="${ctxPath}/static/css/site.css">
		<script type="text/javascript" src="${staticPath}/js/jquery.min.js"></script> 
		<style type="text/css">
			 body{padding: 10px 30px;margin: 10px 30px;}
			 .layui-input-block{width: 100%;}
			 .layui-textarea{height: 130px;}
			 .layui-input-block{margin-left: 0px;margin-top: 12px;}
			 #sqlToJson,#exportSchema{display: none;}
			 .fastSelectSql2 a{color: blue;}
		</style>
	</head>
<body>
	<form id="easyform" class="layui-form">
		 <input type="hidden" id="tabType"/>
		
		 <fieldset class="layui-elem-field site-demo-button" style="margin-top: 10px;">
			  <legend>内部工具</legend>
			  <div style="padding: 15px;">
			      <input type="number" value="" id="maxRow" class="layui-input" style="width: 120px;display: inline-block;" placeholder="最大行数">
			      <input type="number" value="" id="cellMinWidth" class="layui-input" style="width: 120px;display: inline-block;" placeholder="表格最小宽度">
				  <button style="margin-left: 30px;display: inline-block;" class="layui-btn" type="button" onclick="reqExcute(1)">查询</button>
				  <button style="margin-left: 30px;display: inline-block;" class="layui-btn" type="button" onclick="reqExcute(2)">执行</button>
				  <button style="margin-left: 30px;display: inline-block;" class="layui-btn layui-btn-normal" type="button" onclick="resetMsg()">重置</button>
				  <button id="exportSchema" style="margin-left: 30px;" class="layui-btn layui-btn-primary" type="button" onclick="exportDbJson()">导出表结构</button>
				  <button style="margin-left: 20px;display: none;" id="jsonParser"  type="button" class="layui-btn layui-btn-primary" onclick="jsonHelp()">JSON解析</button>
			  </div>
		 </fieldset>
		 <div class="layui-tab layui-tab-brief" lay-filter="tabBrief">
		  <ul class="layui-tab-title">
		    <li  class="layui-this">脚本语句,单引号请使用[]或{}代替</li>
		  </ul>
		  <div class="layui-tab-content">
		    <div class="layui-tab-item layui-show">
		    	<textarea id="sqlContent" style="width: 100%;" class="layui-textarea"></textarea>
		    	<p class="fastSelectSql" style="line-height: 26px;padding: 8px;color: #999;">快捷操作
		    		 <a href="javascript:void(0)" class="layui-badge-rim" onclick="fastSelectSql('s0')">所有库</a>
		    		 <a href="javascript:void(0)" class="layui-badge-rim" onclick="fastSelectSql('s1')">所有表</a>
		    		 <a href="javascript:void(0)" class="layui-badge-rim" onclick="fastSelectSql('s2')">表结构</a>
		    		 <a href="javascript:void(0)" class="layui-badge-rim" onclick="fastSelectSql('s3')">表数据量</a>
		    	</p>
		    </div>
		  </div>
		</div>   
		
		 
		 <div class="layui-tab layui-tab-brief" style="margin-top: -30px;" lay-filter="resultTabBrief">
		  <ul class="layui-tab-title">
		    <li class="layui-this">result</li>
		  </ul>
		  <div class="layui-tab-content">
		    <div class="layui-tab-item layui-show">
		    	  <div class="layui-input-block" id="sqlMsg"></div>
		    	  <textarea  class="layui-textarea layui-hide" class="layui-input-block" id="sqlToJson"  style="height: 350px;color:#666;">

		    	  </textarea>
		    	  <table id="cust-list" lay-filter="cust-list"></table>
		    </div>
		  </div>
		</div> 
	</form>
	<script type="text/javascript" src="${staticPath}/js/jsrender.min.js"></script>
	<script type="text/javascript" src="${staticPath}/lib/layer/layer.js"></script>
	<script type="text/javascript" src="${staticPath}/lib/layui/layui.js"></script>
	<script type="text/javascript" src="${staticPath}/js/easitline.core-2.0.0.js"></script>
	<script type="text/javascript" src="${staticPath}/js/requreLib.js"></script>
	<script type="text/javascript" src="${ctxPath}/js/aes.js"></script>
	<script type="text/javascript">
	
	String.prototype.startWith=function(str){     
		var reg=new RegExp("^"+str);     
		return reg.test(this);        
	}  

    String.prototype.endWith=function(str){     
	     var reg=new RegExp(str+"$");     
		 return reg.test(this);        
	}
    
    String.prototype.replaceAll = function(s1,s2){ 
    	return this.replace(new RegExp(s1,"gm"),s2); 
    }
    $(document).keydown(function (event) {
		 if (event.keyCode == 27) {
	         if(layer!=null){
	        	 layer.closeAll();
	         }
	     }
		 if(event.keyCode==13){ 
			// reqExcute();
		 }
	 });
    function resetMsg(){
    	$("#sqlMsg").html("");
    	$(".layui-table-view").remove();
    }
    var dbType='${param.dbType}';
    var pwd = localStorage.getItem("c_pwd")||'';
	function reqExcute(type){
		if(pwd){
				doExcute(type);
		}else{
			layer.prompt({title:"输入密码",formType:1},function(value, index, elem){
				layer.close(index);
				pwd=value;
				localStorage.setItem("c_pwd",pwd);
				doExcute();
			});
		}
	   function doExcute(type){
			var sqlStr=$("#sqlContent").val();
			if(sqlStr){
				var sqls=sqlStr.split(";");
				for(var index in sqls){
					var val=sqls[index]||'';
					if(val){
						val=val.trim();
						var sqlContent = val.toUpperCase();
						sqlContent = sqlContent.replaceAll('\'','"');
						var _sql=val.replaceAll('\'','"');
						
						_sql = aesEncrypt(_sql);
						
						batchExcuteSql(sqlContent,_sql,type);
					}
				}
			}else{
				layer.msg("不能为空!");
			}
	    }
		function batchExcuteSql(sqlContent,sqlStr,type){
			if(sqlContent.startWith("DELETE")||sqlContent.startWith("UPDATE")||sqlContent.startWith("INSERT")||sqlContent.startWith("ALTER")){
				doUpdate(sqlStr);
			}else if(sqlContent.startWith("SELECT")){
				doSelect(sqlStr);
			}else{
				if(type==1){
					doSelect(sqlStr);
				}else{
					doUpdate(sqlStr);
				}
			}
		}
	}
	function fastSelectSql(flag){
		if(flag=='s0'){
			if(dbType=='MySql'){
				$("#sqlContent").val("show databases");
			}else{
				$("#sqlContent").val("SELECT * FROM DBA_USERS");
			}
			return;	
		}
		if(flag=='s1'){
			if(dbType=='MySql'){
				$("#sqlContent").val("select  TABLE_NAME,TABLE_SCHEMA,TABLE_COMMENT  from information_schema.tables");
			}else{
				$("#sqlContent").val("select TABLE_NAME,TABLESPACE_NAME from all_tables");
			}
			return;	
		}
		if(flag=='s2'){
			if(dbType=='MySql'){
				$("#sqlContent").val("select * from information_schema.columns where table_name=表名称");
			}else{
				$("#sqlContent").val("select TABLE_NAME,OWNER,COLUMN_NAME,DATA_TYPE,DATA_LENGTH from all_tab_columns where Table_Name=表名称");
			}
			return;	
		}
		if(flag=='s3'){
			$("#sqlContent").val("select count(1) from 表名称");
			return;	
		}
		
	}
	function exportDbJson(){
		var checkStatus = table.checkStatus('cust-list');
		var checkList=checkStatus.data;
		var dbs=[];
		for(var i = 0;i < checkList.length;i++){
			var checkCust = checkList[i];
			if(checkCust.DATABASE)dbs.push(checkCust.DATABASE);
			if(checkCust.USERNAME)dbs.push(checkCust.USERNAME);
			ajax.remoteCall("${ctxPath}/servlet/dsTool?action=createJson",{dsName:'${param.dsName}',dbName:checkCust.DATABASE||checkCust.USERNAME},function(result){  
				layer.msg(result.msg);	
			});
		}
		
	}
	function jsonHelp(){
		var json=$("#sqlToJson").val();
		if(json){
			localStorage.setItem("CONSOLE_JSON",json);
		}
		layer.open({type:2,title:'JSON解析',maxmin:true,area:['50%','100%'],offset:'r',scrollbar:false,shade:false,content:'${ctxPath}/pages/json/json-parser.jsp'});
	}
	
	var table;
	function doSelect(sqlContent){
		var maxRow=$('#maxRow').val()||100;
		var cellMinWidth=$('#cellMinWidth').val()||130;
		var data={dsName:'${param.dsName}',contentStr:sqlContent,maxRow:maxRow,pwd:pwd};
		ajax.remoteCall("${ctxPath}/servlet/dsTool?action=getContent", data,function(result){  
			if(result.state==1){
				$("#sqlMsg").append("<span style='color:#009688;display: block;'>"+aesDecrypt(sqlContent)+">>"+result.msg+",结果行数："+result.data.row+",执行时间："+result.data.time+"</span>");
				if(result.data){
					$("#sqlToJson,#jsonParser").css('display','inline-block');
					var firstData=result.data.data[0];
					var cols=[];
					var i=2;
					cols[0]={type:'numbers'};
					cols[1]={type:'checkbox'};
					for(var key in firstData){
						cols[i]={'field':key,'title':key,sort:true};
						i++
					}
					 layui.use(['table'], function(){
						 table = layui.table;
						  table.render({
						      elem: '#cust-list'
						      ,cols: [cols],
						      page:true,
						      limit:30,
						      toolbar: true,
						      height:500,
						      defaultToolbar:['filter', 'print', 'exports'],
						      cellMinWidth:cellMinWidth,
						      limits:[10,15,30,20,50,100,200,500,1000,2000],
						      data:result.data.data
						  });
						  
				    }); 
				    $("#sqlToJson").val(JSON.stringify(result.data.data));
				}else{
					$("#sqlToJson,#jsonParser").hide();
				}
			}else if(result.state==403){
				pwd="";
				layer.alert(result.msg);
			}else{
			    $("#sqlToJson,#jsonParser").hide();
				$("#sqlMsg").append("<span style='color:red;display: block;'>"+aesDecrypt(sqlContent)+">>"+result.msg+"</span>");
			}
		},{then:function(result){
			
		}});
	}
	
	function doUpdate(sqlContent){
		//layer.confirm("操作需谨慎,是否继续？",function(index){
			$("#sqlToJson,#jsonParser").hide();
		//	layer.close(index);
			var data={dsName:'${param.dsName}',contentStr:sqlContent,pwd:pwd};
			ajax.remoteCall("${ctxPath}/servlet/dsTool?action=editContent", data,function(result){  
				$("#sqlToJson").val("");
				if(result.state==1){
					var row=result.data.row;
					var time=result.data.time;
					$("#sqlMsg").append("<span style='color:#009688;display: block;'>"+aesDecrypt(sqlContent)+">>影响行数："+row+",执行时间："+time+"</span>");
				}else if(result.state==403){
					pwd="";
					layer.alert(result.msg);
				}else{
					$("#sqlMsg").append("<span style='color:red;display: block;'>"+aesDecrypt(sqlContent)+">>"+result.msg+"</span>");
				}
			},{then:function(result){
				
			}});
		//});
	}
	 $(function(){
		 var url=location.href;
		 if(url.indexOf("172.16")>-1||url.indexOf("localhost")>-1){
			 $("#exportSchema").show();
		 }
		 layui.use('element', function(){
			  var element = layui.element;
			  element.on('tab(tabBrief)', function(){
			      var id= this.getAttribute('lay-id');
			  });
		   }
		 );
	 });
</script>
</body>
</html>
