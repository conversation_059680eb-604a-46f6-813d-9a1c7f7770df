package org.easitline.console.vo;

import com.alibaba.fastjson.JSONObject;
import org.apache.catalina.util.ServerInfo;
import org.easitline.common.core.EasyServer;
import org.easitline.common.core.Globals;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.console.command.Self;
import org.easitline.console.service.ConfigCryptorService;
import org.easitline.console.utils.ServerInfoUtils;
import org.hyperic.sigar.*;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;


public class ServerInfoModel { 
	
	public static Sigar sigar = new Sigar();
	
	public static int kb = 1024;
	
	public static DecimalFormat df = new DecimalFormat("##.0%");
	
	public static DecimalFormat df1 = new DecimalFormat("##.00");

	/**
	 * 获得版本号
	 * @return
	 */
	public String getVersion(){
		return EasyServer.getVersion();
	}
	
	/**
	 * 获取当前Tomcat的线程ID
	 * @return
	 */
	public String getPid(){
		return Self.getProcessId();
	}
	
	public String getCatalinaBase() {
		return Globals.BASE_DIR;
	}
	
	public String getServerInfo(){
		return  ServerInfo.getServerInfo();
	}
	public String getServerNumber(){
		return  ServerInfo.getServerNumber();
	}
	public String getServerBuilt(){
		return  ServerInfo.getServerBuilt();
	}
	
	public String getMachineCode() {
		return ServerContext.getProperties("MACHINE_CODE","");
	}
	
	/**
	 * 获得版本日期
	 * @return
	 */
	public String getVersionDate(){
		return  EasyServer.getVersionDate();
	}
	
	public String getBootTime(){
		return EasyServer.getBootTime();
	}
	
	public String getRuntime(){
		return EasyServer.getRunime();
	}

	public String getUserDir(){
		return System.getProperty("user.dir");
	}

	public String getUserName(){
		return System.getProperty("user.name");
	}

	public String getJavaHome(){
		return System.getProperty("java.home");
	}

	public String getJavaVendor(){
		return System.getProperty("java.vendor");
	}

	public String getJavaVmVersion(){
		return System.getProperty("java.version")+"-"+System.getProperty("java.vm.version");
	}

	public String getCryptorFlag(){
		return ConfigCryptorService.isCryptorFlag()?"启用":"未启用";
	}

	public String getCryptorState(){
		return ConfigCryptorService.checkState()?"可用":"不可用";
	}

	/**
	 * 获得服务器地址信息
	 * @return
	 */
	public String getAddress() {
		StringBuilder sb = new StringBuilder();
		try {
			String[] interfaces = sigar.getNetInterfaceList();
			int idx = 0;
			for (int i = 0; i < interfaces.length; i++) {
				NetInterfaceConfig ifconfig = sigar.getNetInterfaceConfig(interfaces[i]);

				if (NetFlags.LOOPBACK_ADDRESS.equals(ifconfig.getAddress())
						|| (ifconfig.getFlags() & NetFlags.IFF_LOOPBACK) != 0
						|| NetFlags.NULL_HWADDR.equals(ifconfig.getHwaddr()) || "0.0.0.0".equals(ifconfig.getAddress())
						|| (ifconfig.getFlags() & 1L) <= 0L) {
					continue;

				}

				idx++;
				if (idx > 1)
					sb.append(",");
				NetInterfaceStat stat = sigar.getNetInterfaceStat(interfaces[i]);
				sb.append(ifconfig.getAddress());
				//sb.append("IP:" + ifconfig.getAddress()  + " speed:" + df1.format(stat.getSpeed()/1000000)+"M");

			}

		} catch (UnsatisfiedLinkError ex) {
			CoreLogger.getLogger().error(ex.getMessage(), ex);
			return ServerInfoUtils.getAddress();
		} catch (SigarException ex) {
			CoreLogger.getLogger().error(ex.getMessage(), ex);
			return ServerInfoUtils.getAddress();
		}
		return sb.toString();
	}

	/**
	 * 获得服务器最大内存
	 *    long freeMem = Runtime.getRuntime().freeMemory()/(1024*1024);
   long totalMem = Runtime.getRuntime().totalMemory()/(1024*1024);
   long maxMem = Runtime.getRuntime().maxMemory()/(1024*1024);
	 * @return
	 */
	public String getTotalMemory() {
		try {
			return getPrintSize(sigar.getMem().getTotal());
			//Runtime.getRuntime().totalMemory()/kb/kb;
		}catch (UnsatisfiedLinkError ex) {
			return "-1";
		} catch (SigarException e) {
			return "-1";
		}
	}
	public long getTotalMemoryForLong() {
		try {
			return (sigar.getMem().getTotal()/kb/kb);
		}catch (UnsatisfiedLinkError ex) {
			return -1;
		} catch (SigarException e) {
			return -1;
		}
	}
	/**
	 * 服务器剩余内存
	 * @return
	 */
	public String getMemFree() {
		try {
			return getPrintSize(sigar.getMem().getTotal())+" / "+getPrintSize(sigar.getMem().getUsed())+" / "+getPrintSize(sigar.getMem().getFree());
		}catch (UnsatisfiedLinkError ex) {
			 CoreLogger.getLogger().error(ex.getMessage(), ex);
			 return ServerInfoUtils.getMemFree();
		} catch (SigarException ex) {
			CoreLogger.getLogger().error(ex.getMessage(), ex);
			return ServerInfoUtils.getMemFree();
		}
	}
	

	/**
	 * JVM内存的使用率
	 * @return
	 */
	public String getMemoryUsedPercent(){
		return df.format(1.0*(this.getMaxMemoryForLong()-this.getFreeMemoryForLong())/this.getMaxMemoryForLong());
	}
	 /**  JVM剩余内存. */
	public long getFreeMemoryForLong() {
		return Runtime.getRuntime().freeMemory()/kb/kb;
	}
	/**  JVM剩余内存. */
	public String getFreeMemory() {
		return getPrintSize(Runtime.getRuntime().freeMemory());
	}

	/** JVM最大可使用内存. */
	public String getMaxMemory() {
		return getPrintSize(Runtime.getRuntime().maxMemory());
	}
	

	/** JVM最大可使用内存. */
	public long getMaxMemoryForLong() {
		return Runtime.getRuntime().maxMemory()/kb/kb;
	}

	  /** 操作系统. */
	public String getOsInfo() {
		return System.getProperty("os.name")+" / "+System.getProperty("os.arch")+" /  "+System.getProperty("os.version");
	}
	
	public String getMemoryPoolInfo() {
		return ServerInfoUtils.printMemoryPoolInfo();
	}
	

	 /** 线程总数. */
	public  int getTotalThread() {
		ThreadGroup parentThread = null;
       for (parentThread = Thread.currentThread().getThreadGroup(); parentThread
               .getParent() != null; parentThread = parentThread.getParent())
           ;
       int totalThread = parentThread.activeCount();
       return totalThread;
	}
	

	/**
	 * 获得JVM版本
	 * @return
	 */
	public String getJavaVm() {
		return System.getProperty("java.vm.name")+" "+System.getProperty("java.version");
	}
	private String diskDFreeSum="0";
	
	@SuppressWarnings("unused")
	private String getDiskFreeSum(){
		return diskDFreeSum;
	}
	
	
	
	/**
	 * 获得文件系统信息
	 * @return
	 * @throws Exception
	 */
	public List<DiskModel> getFileSystemInfo() throws Exception {
		List<DiskModel> list  =  new ArrayList<DiskModel>();
		try{
			FileSystem fslist[] = sigar.getFileSystemList();
			for(int i = 0;i<fslist.length;i++){
				FileSystem fs = fslist[i];
				if(fs.getType() != FileSystem.TYPE_LOCAL_DISK) continue;
				DiskModel disk = new DiskModel();
				disk.setDevName(fs.getDirName());
				FileSystemUsage usage = sigar.getFileSystemUsage(fs.getDirName());
				disk.setTotal(df1.format(1.0*usage.getTotal()/1024/1024)+"G");
				disk.setUsed(df1.format(1.0*usage.getUsed()/1024/1024)+"G");
				disk.setAvail(df1.format(1.0*usage.getAvail()/1024/1024)+"G");
				list.add(disk);
			}
		}catch (UnsatisfiedLinkError ex) {
			return list;
		} catch (SigarException e) {
			return list;
		}
		 return list;
	}
	public List<JSONObject> getCpuInfo() throws Exception {
		List<JSONObject> list  =  new ArrayList<JSONObject>();
		try{
			CpuInfo infos[] = sigar.getCpuInfoList();
			CpuPerc cpuList[]  = sigar.getCpuPercList();
			for(int i = 0;i<infos.length;i++){
				CpuInfo  cp = infos[i];
				JSONObject info = new JSONObject();
				info.put("model",i+1+"."+cp.getVendor()+"/"+cp.getModel()+"/"+cp.getMhz());
				info.put("user",CpuPerc.format(cpuList[i].getUser()));
				info.put("sys",CpuPerc.format(cpuList[i].getSys()));
				info.put("nice",CpuPerc.format(cpuList[i].getNice()));
				info.put("idle",CpuPerc.format(cpuList[i].getIdle()));
				info.put("idleValue",cpuList[i].getIdle());
				info.put("combined",CpuPerc.format(cpuList[i].getCombined()));
				list.add(info);
			}
		}catch (UnsatisfiedLinkError ex) {
			return list;
		} catch (SigarException e) {
			return list;
		}
		return list;
	}
	
	public static void main(String[] args) throws Exception {
		Sigar sigar = new Sigar();
		
		System.out.println(sigar.getCpu().getTotal());
		System.out.print(sigar.getCpuInfoList()[0].getVendor()+"(R) ");
		System.out.print(sigar.getCpuInfoList()[0].getModel());
		System.out.println("\tx"+sigar.getCpuInfoList().length);
		System.out.println(sigar.getMem().getTotal());
		System.out.println(Sigar.class.getClass().getResource("/sigar-x86-winnt.dll"));
		System.out.println(Thread.currentThread().getContextClassLoader().getResource(""));;
		System.out.println(Sigar.class.getClassLoader().getResource(""));
		System.out.println(ClassLoader.getSystemResource(""));
		//System.out.println(System.getProperty("java.library.path"));

		System.out.println(sigar.getFQDN());
		//String str = "dsafsdf.dsf-sdf.dll";
		//System.out.println(str.substring(str.lastIndexOf("."),str.length()));
		
		OperatingSystem os = OperatingSystem.getInstance();
		System.out.println(os.getArch());
		System.out.println(os.getVendor());
		System.out.println(os.getPatchLevel());
		System.out.println(os.getCpuEndian());
		System.out.println(os.getDataModel());
		System.out.println(os.getDescription());
		System.out.println(os.getVersion());
		System.out.println(os.getVendorCodeName());
		System.out.println(os.getMachine());

		System.out.println("cup:"+1.0*sigar.getCpu().getIdle()/sigar.getCpu().getTotal());
		System.out.println(CpuPerc.format(sigar.getCpu().getTotal()));

		System.out.println("cup used: "+sigar.getCpuPerc().getCombined());
		 FileSystem fslist[] = sigar.getFileSystemList();
		 for(int i = 0;i<fslist.length;i++){
			 FileSystem fs = fslist[i];
			 System.out.println("fs.getDevName() = " + fs.getDevName());
			 if(fs.getType() != FileSystem.TYPE_LOCAL_DISK)continue;
			 FileSystemUsage usage = null;
			 usage = sigar.getFileSystemUsage(fs.getDirName());
			 System.out.println("Total:"+1.0*usage.getTotal()/1024/1024+"G");
			 System.out.println("Used:"+1.0*usage.getUsed()/1024/1024+"G");
			 System.out.println("Avail:"+1.0*usage.getAvail()/1024/1024+"G");
		 }

		 String[] interfaces = sigar.getNetInterfaceList();
		 for(int i = 0;i<interfaces.length;i++){
			 NetInterfaceConfig ifconfig = sigar.getNetInterfaceConfig(interfaces[i]);
			 
			 if (NetFlags.LOOPBACK_ADDRESS.equals(ifconfig.getAddress())

                     || (ifconfig.getFlags() & NetFlags.IFF_LOOPBACK) != 0

                     || NetFlags.NULL_HWADDR.equals(ifconfig.getHwaddr())
                     || "0.0.0.0".equals(ifconfig.getAddress())
                     || (ifconfig.getFlags()&1L) <= 0L
					 ) {
                  continue;

              }
			 System.out.println("address:"+ifconfig.getAddress()+"flag:"+ifconfig.getFlags());
			 System.out.println(interfaces[i]);
			 NetInterfaceStat stat = sigar.getNetInterfaceStat(interfaces[i]);
			 System.out.println(stat.getSpeed());
		 }
	}
	private  String getPrintSize(long size) {
		//如果字节数少于1024，则直接以B为单位，否则先除于1024，后3位因太少无意义
		if (size < 1024) {
			return String.valueOf(size) + "B";
		} else {
			size = size / 1024;
		}
		//如果原字节数除于1024之后，少于1024，则可以直接以KB作为单位
		//因为还没有到达要使用另一个单位的时候
		//接下去以此类推
		if (size < 1024) {
			return String.valueOf(size) + "KB";
		} else {
			size = size / 1024;
		}
		if (size < 1024) {
			//因为如果以MB为单位的话，要保留最后1位小数，
			//因此，把此数乘以100之后再取余
			size = size * 100;
			return String.valueOf((size / 100)) + "."
					+ String.valueOf((size % 100)) + "MB";
		} else {
			//否则如果要以GB为单位的，先除于1024再作同样的处理
			size = size * 100 / 1024;
			return String.valueOf((size / 100)) + "."
					+ String.valueOf((size % 100)) + "GB";
		}
	}

}
