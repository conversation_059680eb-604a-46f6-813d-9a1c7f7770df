package org.easitline.console.vo;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.easitline.common.db.EasyRowMapper;


/**
 create table EASI_APP_DS (
   APP_ID              VARCHAR(100)                  not null,
   DS_NAME              VARCHAR(100)                   not null,
   DS_DESC              VARCHAR(100)                   ,
   SYS_DS_NAME          VARCHAR(100)                   ,
   PRIMARY KEY(APP_ID, DS_NAME)
)
 * <AUTHOR>
 *
 */
public class AppdsRowMapper implements EasyRowMapper<AppdsModel> {

	@Override
	public AppdsModel mapRow(ResultSet rs, int rowNum) {
		// TODO Auto-generated method stub
		AppdsModel vo = new AppdsModel();
		try {
			vo.setAppId(rs.getString("APP_ID"));
			vo.setDsName(rs.getString("DS_NAME"));
			vo.setDsDesc(rs.getString("DS_DESC"));
			vo.setSysDsName(rs.getString("SYS_DS_NAME"));
		} catch (SQLException ex) {
			ex.printStackTrace();
		}
		return vo;
		
	}

}
