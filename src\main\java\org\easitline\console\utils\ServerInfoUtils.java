package org.easitline.console.utils;

import java.io.File;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryPoolMXBean;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

import org.apache.catalina.util.ServerInfo;
import org.easitline.common.core.EasyServer;
import org.easitline.common.core.Globals;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.console.command.Self;
import org.easitline.console.vo.DiskModel;

import com.alibaba.fastjson.JSONObject;
import com.sun.management.OperatingSystemMXBean;


public class ServerInfoUtils { 
	
	private static class Holder{
		private static ServerInfoUtils service = new ServerInfoUtils();
	}
	
	public static ServerInfoUtils getService(){
		return Holder.service;
	}
	
	public static int kb = 1024;
	
	public static DecimalFormat df = new DecimalFormat("##.0%");
	
	public static DecimalFormat df1 = new DecimalFormat("##.00");
	
	
	/**
	 * 获得服务器地址信息
	 * @return
	 */
	public static String getAddress() {
		try {
            InetAddress ip = InetAddress.getLocalHost();
            return ip.getHostAddress();
        } catch (UnknownHostException e) {
           CoreLogger.getLogger().error(e.getMessage(), e);
        }
		return "";
	}

	
	/**
	 * 服务器内存信息
	 * @return
	 */
	public static String getMemFree() {
		OperatingSystemMXBean osBean = ManagementFactory.getPlatformMXBean(OperatingSystemMXBean.class);
        long freePhysicalMemorySize  = osBean.getFreePhysicalMemorySize();
        long physicalMemorySize = osBean.getTotalPhysicalMemorySize();
        return getPrintSize(physicalMemorySize)+"/"+getPrintSize(physicalMemorySize - freePhysicalMemorySize)+"/"+getPrintSize(freePhysicalMemorySize);
	}

	/**
	 * JVM内存的使用率
	 * @return
	 */
	private String getMemoryUsedPercent(){
		return df.format(1.0*(this.getMaxMemoryForLong()-this.getFreeMemoryForLong())/this.getMaxMemoryForLong());
	}
	 /**  JVM剩余内存. */
	private long getFreeMemoryForLong() {
		return Runtime.getRuntime().freeMemory()/kb/kb;
	}
	/**  JVM剩余内存. */
	private String getFreeMemory() {
		return getPrintSize(Runtime.getRuntime().freeMemory());
	}

	/** JVM最大可使用内存. */
	private String getMaxMemory() {
		return getPrintSize(Runtime.getRuntime().maxMemory());
	}
	

	/** JVM最大可使用内存. */
	private long getMaxMemoryForLong() {
		return Runtime.getRuntime().maxMemory()/kb/kb;
	}
	
	 /** 线程总数. */
	private  int getTotalThread() {
	  ThreadGroup parentThread = null;
       for (parentThread = Thread.currentThread().getThreadGroup(); parentThread.getParent() != null; parentThread = parentThread.getParent());
       int totalThread = parentThread.activeCount();
       return totalThread;
	}
	
	private void setCupAndDiskInfo(JSONObject data) {
	    List<JSONObject> list = new ArrayList<>();
	    OperatingSystemMXBean osBean = (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
	    int availableProcessors = osBean.getAvailableProcessors();
	    double systemLoadAverage = osBean.getSystemLoadAverage();
	    JSONObject cpuInfo = new JSONObject();
	    cpuInfo.put("model",osBean.getVersion()+","+availableProcessors+"核");
	    cpuInfo.put("user", systemLoadAverage+"%");
	    list.add(cpuInfo);
        data.put("cpuInfo",list);
        
        List<DiskModel> list2 = new ArrayList<DiskModel>();
        File[] roots = File.listRoots();
        for (File root : roots) {
            DiskModel disk = new DiskModel();
            disk.setDevName(root.getPath());
            long totalSpace = root.getTotalSpace();
            long usableSpace = root.getUsableSpace();
            long freeSpace = root.getFreeSpace();
            disk.setTotal(df1.format(1.0 * totalSpace / 1024 / 1024 / 1024) + "G");
            disk.setUsed(df1.format(1.0 * (totalSpace - usableSpace) / 1024 / 1024 / 1024) + "G");
            disk.setAvail(df1.format(1.0 * usableSpace / 1024 / 1024 / 1024) + "G");
            list2.add(disk);
        }
        data.put("fileSystemInfo",list2);
	  }
	  
	
	private static String getPrintSize(long size) {
		//如果字节数少于1024，则直接以B为单位，否则先除于1024，后3位因太少无意义
		if (size < 1024) {
			return String.valueOf(size) + "B";
		} else {
			size = size / 1024;
		}
		//如果原字节数除于1024之后，少于1024，则可以直接以KB作为单位
		//因为还没有到达要使用另一个单位的时候
		//接下去以此类推
		if (size < 1024) {
			return String.valueOf(size) + "KB";
		} else {
			size = size / 1024;
		}
		if (size < 1024) {
			//因为如果以MB为单位的话，要保留最后1位小数，
			//因此，把此数乘以100之后再取余
			size = size * 100;
			return String.valueOf((size / 100)) + "."+ String.valueOf((size % 100)) + "MB";
		} else {
			//否则如果要以GB为单位的，先除于1024再作同样的处理
			size = size * 100 / 1024;
			return String.valueOf((size / 100)) + "."+ String.valueOf((size % 100)) + "GB";
		}
	}


    public static String printMemoryPoolInfo() {
    	MemoryPoolMXBean memoryPool = getOldGenMemoryPool();
    	StringBuffer sb = new StringBuffer();
    	if(memoryPool!=null) {
    		long usedAfterGc = memoryPool.getUsage().getUsed();
    		long maxMemory = memoryPool.getUsage().getMax();
    		float usagePercentage = (float) usedAfterGc / maxMemory * 100;
    		sb.append("分配内存: " + formatMemory(maxMemory));
    		sb.append("，使用内存: " + formatMemory(usedAfterGc));
    		sb.append("，内存使用率: " + String.format("%.1f", usagePercentage) + "%");
    	}
    	return sb.toString();
    }

    private static String formatMemory(long memory) {
        return String.format("%.1f", memory / 1024.0 / 1024.0) + "M";
    }

    private static MemoryPoolMXBean getOldGenMemoryPool() {
        List<MemoryPoolMXBean> memoryPools = ManagementFactory.getMemoryPoolMXBeans();
        for (MemoryPoolMXBean memoryPool : memoryPools) {
            if (memoryPool.getName().toLowerCase().contains("old")) {
                return memoryPool;
            }
        }
        return null;
    }
    
	
	public JSONObject getCommonData() {
		JSONObject data = new JSONObject();
		try {
			if(ConsoleUtils.isTomcat()) {
				data.put("serverInfo",ServerInfo.getServerInfo());
				data.put("serverNumber",ServerInfo.getServerNumber());
				data.put("serverBuilt",ServerInfo.getServerBuilt());
			}else {
				data.put("serverInfo",ConsoleUtils.getServerType());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		 
		data.put("memFree",getMemFree());
		data.put("memoryUsedPercent",getMemoryUsedPercent());
		data.put("freeMemoryForLong",getFreeMemoryForLong());
		data.put("freeMemory",getFreeMemory());
		data.put("maxMemory",getMaxMemory());
		data.put("totalThread",getTotalThread());
		data.put("memoryPoolInfo", printMemoryPoolInfo());
		
		
		
		data.put("pid",Self.getProcessId());
		data.put("version",EasyServer.getVersion());
		data.put("catalinaBase",Globals.BASE_DIR);
		data.put("versionDate",EasyServer.getVersionDate());
		data.put("runtime",EasyServer.getRunime());
		data.put("bootTime",EasyServer.getBootTime());
		data.put("address",getAddress());
		data.put("javaVm",System.getProperty("java.vm.name")+" "+System.getProperty("java.version"));
		data.put("osInfo",System.getProperty("os.name")+" / "+System.getProperty("os.arch")+" /  "+System.getProperty("os.version"));
		
		try {
			this.setCupAndDiskInfo(data);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return data;
	}
}
