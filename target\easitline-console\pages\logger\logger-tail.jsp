<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html>
<head>
    <title>Log Tailer  - ${name}</title>
    <meta charset="utf-8">
    <LINK rel="Shortcut Icon" href="${ctxPath}/static/images/favicon.ico" />
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="${staticPath}/lib/layui/css/layui.css">
    <style>
    	.json-view,.sql-view{display: none;}
    	.select-style{
    		border: 1px solid #d9d9d9;
    		transition:all .3s;
    		height: 30px;
    		padding: 6px 12px;
    		line-height: 30px;
    	}
    	.req-result{display: inline-block;color: #7512de;margin-left: 10px;}
    	#msg{display: inline-block;color: red;margin-left: 10px;}
    	#logContent{
   		    font-family: "Lucida Console";
    		background-color: rgb(0, 0, 0);
    		color: rgb(192, 192, 192);
    		line-height: 20px;
    		font-size: 14px;
    		width: 100%; 
    		height: calc(100vh - 100px);
    		overflow: scroll;
    	}
    	.time-span{color: yellow;}
    	.importantText{color: yellow;}
    	.highlight-sql{color: #32fff7;}
    	.highlight-yq{color:#ffffff;}
    	.highlight-normal{color: #ffffff;}
    	.highlight-easitline{color: #32c5ff;}
    	.highlight-warning {
		   color: yellow;
		}
		
		.highlight-error {
		  color: red;
		  font-weight: 700;
		}
		
		.highlight-match {
		  background-color: yellow;
		  color: black;
		  font-weight: bold;
		}
		
		.grep-controls {
		  margin-top: 10px;
		  padding: 10px;
		  border: 1px solid #e6e6e6;
		  background-color: #f2f2f2;
		  border-radius: 4px;
		}
		
		.inline-input {
		  display: inline-block;
		  margin-right: 10px;
		  width: 200px;
		}
		
		.grep-row {
		  margin-bottom: 10px;
		}
		
		.checkbox-item {
		  margin-right: 15px;
		  display: inline-block;
		}
		
		.pagination-controls {
		  margin-top: 10px;
		  text-align: center;
		  display: none;
		}
		
		.pagination-info {
		  display: inline-block;
		  margin: 0 15px;
		  color: #333;
		}
		
		.layui-alert {
		  padding: 8px 15px;
		  background-color: #f8f8f8;
		  border-radius: 2px;
		  border-left: 5px solid #1E9FFF;
		}
		
		.layui-alert p {
		  margin: 5px 0;
		}
		
		.layui-alert ul {
		  margin: 5px 0 5px 20px;
		  padding: 0;
		}
		
		.layui-alert code {
		  background-color: #eee;
		  padding: 2px 4px;
		  border-radius: 3px;
		}
		
		/* 状态指示器样式 */
		.status-indicator {
		  margin-top: 10px;
		  padding: 5px 10px;
		  background-color: #f8f8f8;
		  border-left: 3px solid #1E9FFF;
		  font-size: 13px;
		  display: none;
		}
		
		.status-indicator.warning {
		  border-left-color: #FFB800;
		}
		
		.status-indicator.error {
		  border-left-color: #FF5722;
		}
		
		/* 数据指标 */
		.data-metrics {
		  margin-top: 8px;
		  font-size: 12px;
		  color: #666;
		}
		
		.metrics-item {
		  display: inline-block;
		  margin-right: 15px;
		}
		
		/* 操作历史 */
		.search-history {
		  margin-top: 10px;
		}
		
		.history-item {
		  cursor: pointer;
		  padding: 3px 8px;
		  background: #f2f2f2;
		  border-radius: 3px;
		  margin: 3px;
		  display: inline-block;
		  font-size: 12px;
		}
		
		.history-item:hover {
		  background: #e6e6e6;
		}
    </style>
</head>
<body>
	<div class="layui-row" style="padding: 15px;">
		<div class="layui-col-xs12">
		    <div id="logContent" contenteditable="true" spellcheck="false" class="layui-textarea"></div>
		    <hr>
		    <div class="layui-row">
		        <div class="layui-col-xs7">
		            <select name="lines" id="lines" class="select-style">
		                <option value="20">20行</option>
		                <option value="30">30行</option>
		                <option value="40">40行</option>
		                <option value="50" selected="selected">50行</option>
		                <option value="60">60行</option>
		                <option value="70">70行</option>
		                <option value="80">80行</option>
		                <option value="90">90行</option>
		                <option value="100">100行</option>
		                <option value="200">200行</option>
		            </select>
		            <button type="button" class="layui-btn layui-btn-sm auto-refresh" onclick="autoFetchLog()">自动刷新</button>
		            <button type="button" class="layui-btn layui-btn-sm layui-bg-red stop-btn" onclick="stopLog()" style="display:none;">停止刷新</button>
		            <button type="button" class="layui-btn layui-btn-sm layui-bg-orange" onclick="manualFetchLog();">手动刷新</button>
		            <button type="button" class="layui-btn layui-btn-sm layui-bg-blue" onclick="clearLog()">清空</button>
		            <button type="button" class="layui-btn layui-btn-sm layui-bg-blue json-view" onclick="jsonView()">JSON可视化</button>
		            <button type="button" class="layui-btn layui-btn-sm layui-bg-blue sql-view" onclick="sqlView()">SQL格式化</button>
		            <button type="button" class="layui-btn layui-btn-sm" onclick="toggleGrepControls()">Grep工具</button>
		            <div class="checkbox-item">
		                <input type="checkbox" id="followMode" name="followMode" checked lay-skin="primary" title="Follow模式">
		                <label for="followMode">Follow模式</label>
		            </div>
		            <div class="checkbox-item">
		                <input type="checkbox" id="autoScroll" name="autoScroll" checked lay-skin="primary" title="自动滚动">
		                <label for="autoScroll">自动滚动</label>
		            </div>
		        </div>
		        <div class="layui-col-xs5">
		            <div id="msg"></div>
		            <div class="req-result"></div>
		        </div>
		    </div>
		    
		    <!-- Grep控制区域 -->
		    <div class="grep-controls" style="display: none;">
		        <div class="grep-row">
		            <div class="layui-inline inline-input">
		                <label>过滤表达式</label>
		                <input type="text" id="grepPattern" placeholder="输入正则表达式" class="layui-input">
		            </div>
		            <div class="checkbox-item">
		                <input type="checkbox" id="caseSensitive" name="caseSensitive" lay-skin="primary" title="区分大小写">
		                <label for="caseSensitive">区分大小写</label>
		            </div>
		            <div class="layui-inline inline-input">
		                <label>显示匹配行周围的行数</label>
		                <input type="number" id="contextLines" placeholder="显示匹配行周围的行数" value="0" min="0" max="10" class="layui-input">
		            </div>
		            <div class="layui-inline inline-input">
		                <label>每页结果数</label>
		                <input type="number" id="grepPageSize" placeholder="每页显示结果数" value="10" min="1" max="50" class="layui-input">
		            </div>
		            <button type="button" class="layui-btn" onclick="grepLog()">执行Grep</button>
		            <button type="button" class="layui-btn layui-btn-primary" onclick="clearGrep()">清除Grep</button>
		        </div>
		        <div class="grep-row">
		            <div class="layui-alert">
		                <p>提示：支持正则表达式，如不熟悉可以直接输入要查找的文本。高级用法示例：</p>
		                <ul>
		                    <li><code>ERROR|WARN</code> - 查找包含ERROR或WARN的行</li>
		                    <li><code>^\d{4}-\d{2}-\d{2}</code> - 查找以日期开头的行</li>
		                    <li><code>Exception.*?line \d+</code> - 查找异常行号</li>
		                </ul>
		            </div>
		        </div>
		        
		        <!-- 状态指示器 -->
		        <div id="statusIndicator" class="status-indicator"></div>
		        
		        <!-- 数据指标 -->
		        <div class="data-metrics" style="display: none;">
		            <div class="metrics-item">总匹配行: <span id="totalMatchesCount">0</span></div>
		            <div class="metrics-item">处理文件大小: <span id="processedFileSize">0</span> KB</div>
		            <div class="metrics-item">执行时间: <span id="executionTime">0</span> ms</div>
		        </div>
		        
		        <!-- 最近搜索记录 -->
		        <div class="search-history" style="display: none;">
		            <div>最近搜索: <span id="historyItems"></span></div>
		        </div>
		        
		        <!-- 分页控件 -->
		        <div class="pagination-controls">
		            <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" id="prevPage" onclick="gotoPage('prev')">上一页</button>
		            <span class="pagination-info">第 <span id="currentPage">1</span> 页 / 共 <span id="totalPages">1</span> 页</span>
		            <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" id="nextPage" onclick="gotoPage('next')">下一页</button>
		            <div class="layui-inline" style="margin-left: 10px;">
		                <input type="number" id="jumpPageInput" placeholder="页码" min="1" class="layui-input" style="display: inline-block; width: 60px; height: 30px;">
		                <button type="button" class="layui-btn layui-btn-sm" onclick="jumpToPage()">跳转</button>
		            </div>
		        </div>
		    </div>
		</div>
	</div>
	
	<script src="${staticPath}/js/jquery.min.js"></script>
	<script type="text/javascript" src="${staticPath}/lib/layui/layui.js"></script>
    <script type="text/javascript">
    
	    var intervalId = null;
	    var count = 0;
        var loadIng = false;
        var reqTime  = 0;
        var layer = null;
        var lastSearchParams = {}; // 存储最后一次搜索参数
        var currentGrepPage = 1; // 当前grep查询页码
        var totalGrepPages = 1; // grep查询总页数
        var grepResults = []; // 存储grep结果
        var searchHistory = []; // 存储搜索历史
        var isGrepRunning = false; // 是否正在执行grep

        // 显示/隐藏Grep控制区域
        function toggleGrepControls() {
            $('.grep-controls').toggle();
            
            // 如果显示了控制区域，加载历史搜索
            if ($('.grep-controls').is(":visible")) {
                loadSearchHistory();
            }
        }
        
        // 显示状态指示器
        function showStatus(message, type) {
            var statusElem = $('#statusIndicator');
            statusElem.text(message);
            statusElem.removeClass('warning error');
            
            if (type) {
                statusElem.addClass(type);
            }
            
            statusElem.show();
            
            // 5秒后自动隐藏非错误状态
            if (type !== 'error') {
                setTimeout(function() {
                    statusElem.fadeOut();
                }, 5000);
            }
        }
        
        // 更新数据指标
        function updateMetrics(matches, fileSize, time) {
            $('#totalMatchesCount').text(matches || 0);
            $('#processedFileSize').text(fileSize || 0);
            $('#executionTime').text(time || 0);
            $('.data-metrics').show();
        }
        
        // 加载搜索历史
        function loadSearchHistory() {
            try {
                var history = localStorage.getItem('grepSearchHistory');
                if (history) {
                    searchHistory = JSON.parse(history);
                    renderSearchHistory();
                }
            } catch (e) {
                console.error('无法加载搜索历史', e);
            }
        }
        
        // 保存搜索历史
        function saveSearchHistory(pattern) {
            if (!pattern || pattern.trim() === '') return;
            
            try {
                // 移除可能的重复项
                searchHistory = searchHistory.filter(function(item) {
                    return item !== pattern;
                });
                
                // 添加到开头
                searchHistory.unshift(pattern);
                
                // 限制最多保存10项
                if (searchHistory.length > 10) {
                    searchHistory = searchHistory.slice(0, 10);
                }
                
                // 保存到本地存储
                localStorage.setItem('grepSearchHistory', JSON.stringify(searchHistory));
                
                // 更新显示
                renderSearchHistory();
            } catch (e) {
                console.error('无法保存搜索历史', e);
            }
        }
        
        // 渲染搜索历史
        function renderSearchHistory() {
            if (searchHistory && searchHistory.length > 0) {
                var historyHtml = '';
                for (var i = 0; i < searchHistory.length; i++) {
                    historyHtml += '<span class="history-item" onclick="useHistoryItem(\'' + 
                                   searchHistory[i].replace(/'/g, "\\'") + '\')">' + 
                                   searchHistory[i] + '</span>';
                }
                $('#historyItems').html(historyHtml);
                $('.search-history').show();
            } else {
                $('.search-history').hide();
            }
        }
        
        // 使用历史搜索项
        function useHistoryItem(pattern) {
            $('#grepPattern').val(pattern);
        }
        
        // 执行Grep搜索
        function grepLog(page) {
            var pattern = $('#grepPattern').val();
            if (!pattern || pattern.trim() === '') {
                layer.msg('请输入过滤表达式', {icon: 2});
                return;
            }
            
            // 如果正在加载，则不重复发送请求
            if (loadIng) {
                return;
            }
            
            // 保存到搜索历史
            saveSearchHistory(pattern);
            
            // 如果未指定页码，默认第一页
            page = page || 1;
            currentGrepPage = page;
            
            var pageSize = parseInt($('#grepPageSize').val()) || 10;
            
            lastSearchParams = {
                grep: pattern,
                caseSensitive: $('#caseSensitive').prop('checked'),
                context: $('#contextLines').val(),
                pageSize: pageSize,
                page: page,
                follow: false, // Grep模式下禁用follow
                readLine: $('#lines').val() || 30,
                source: '${source}',
                name: '${name}'
            };
            
            // 保存搜索参数到本地，以便刷新页面后仍可继续搜索
            try {
                localStorage.setItem('lastGrepSearch', JSON.stringify({
                    pattern: pattern,
                    caseSensitive: $('#caseSensitive').prop('checked'),
                    contextLines: $('#contextLines').val(),
                    pageSize: pageSize
                }));
            } catch (e) {
                console.error('无法保存搜索参数', e);
            }
            
            stopLog();
            
            // 显示状态指示
            showStatus('正在执行搜索，请稍候...');
            isGrepRunning = true;
            
            // 记录开始时间
            var startTime = new Date().getTime();
            
            $('#logContent').html('<div style="color:white;text-align:center;padding:20px;">正在搜索，请稍候...</div>');
            
            fetchLogWithParams(lastSearchParams, function(success, data, timeTaken) {
                isGrepRunning = false;
                
                if (success) {
                    // 更新指标
                    var matches = 0;
                    var sizeKB = calculateSize(data);
                    
                    // 解析匹配数
                    var matchCountRegex = /总匹配数:\s*(\d+)/;
                    var matchCountMatch = data.match(matchCountRegex);
                    if (matchCountMatch && matchCountMatch.length > 1) {
                        matches = parseInt(matchCountMatch[1]);
                    }
                    
                    updateMetrics(matches, sizeKB, timeTaken);
                    
                    // 根据结果更新状态
                    if (data.includes('警告：内存使用过高') || data.includes('警告：搜索时间过长')) {
                        showStatus('搜索提前终止，结果可能不完整。请尝试更精确的搜索条件。', 'warning');
                    } else if (matches === 0) {
                        showStatus('没有找到匹配结果', 'warning');
                    } else {
                        showStatus('找到 ' + matches + ' 个匹配结果', '');
                    }
                } else {
                    showStatus('搜索失败: ' + (data || '未知错误'), 'error');
                }
            });
            
            // 显示分页控件
            $('.pagination-controls').show();
            updatePaginationControls();
        }
        
        // 翻页处理
        function gotoPage(direction) {
            if (direction === 'prev' && currentGrepPage > 1) {
                grepLog(currentGrepPage - 1);
            } else if (direction === 'next' && currentGrepPage < totalGrepPages) {
                grepLog(currentGrepPage + 1);
            }
        }
        
        // 跳转到指定页码
        function jumpToPage() {
            var pageInput = parseInt($('#jumpPageInput').val());
            if (isNaN(pageInput) || pageInput < 1) {
                layer.msg('请输入有效页码', {icon: 2});
                return;
            }
            
            if (pageInput > totalGrepPages) {
                pageInput = totalGrepPages;
            }
            
            if (pageInput !== currentGrepPage) {
                grepLog(pageInput);
            }
        }
        
        // 键盘事件：在页码输入框按回车跳转
        $(document).ready(function() {
            $('#jumpPageInput').on('keypress', function(e) {
                if (e.which === 13) { // Enter键
                    jumpToPage();
                }
            });
        });
        
        // 更新分页控件状态
        function updatePaginationControls() {
            $('#currentPage').text(currentGrepPage);
            $('#totalPages').text(totalGrepPages);
            $('#jumpPageInput').attr('max', totalGrepPages);
            
            // 禁用或启用翻页按钮
            $('#prevPage').prop('disabled', currentGrepPage <= 1);
            $('#nextPage').prop('disabled', currentGrepPage >= totalGrepPages);
            
            if (currentGrepPage <= 1) {
                $('#prevPage').addClass('layui-btn-disabled');
            } else {
                $('#prevPage').removeClass('layui-btn-disabled');
            }
            
            if (currentGrepPage >= totalGrepPages) {
                $('#nextPage').addClass('layui-btn-disabled');
            } else {
                $('#nextPage').removeClass('layui-btn-disabled');
            }
        }
        
        // 清除Grep搜索
        function clearGrep() {
            // 1. 清空输入字段
            $('#grepPattern').val('');
            $('#caseSensitive').prop('checked', false);
            $('#contextLines').val(0);
            $('#grepPageSize').val(10);
            
            // 2. 重置搜索状态
            lastSearchParams = {};
            currentGrepPage = 1;
            totalGrepPages = 1;
            
            // 3. 隐藏分页控件
            $('.pagination-controls').hide();
            
            // 4. 清除本地存储的grep参数
            try {
                localStorage.removeItem('lastGrepSearch');
            } catch (e) {
                console.error('无法清除搜索参数', e);
            }
            
            // 5. 隐藏grep控制面板
            $('.grep-controls').hide();
            
            // 6. 隐藏指标和状态
            $('.data-metrics').hide();
            $('#statusIndicator').hide();
            
            // 7. 向后端发送清除grep缓存的请求
            showStatus('正在清除grep缓存...');
            
            $.ajax({
                url: '${ctxPath}/servlet/logger/tail',
                type: 'POST',
                data: {
                    action: 'clearGrepCache',
                    source: '${source}',
                    name: '${name}'
                },
                dataType: 'text',
                success: function(response) {
                    console.log('grep缓存清除结果:', response);
                    showStatus('缓存已清除', '');
                },
                error: function(error) {
                    console.error('清除grep缓存失败:', error);
                    showStatus('缓存清除失败: ' + error.statusText, 'error');
                },
                complete: function() {
                    // 8. 重新获取日志
                    manualFetchLog();
                }
            });
        }
        
        function fetchLog(params) {
            params = params || {};
            // 检查是否有上次的Grep参数
            if (lastSearchParams.grep) {
                params = Object.assign({}, lastSearchParams, params);
            } else {
                // 常规参数
                params = Object.assign({
                    readLine: $('#lines').val() || 30,
                    source: '${source}',
                    name: '${name}',
                    follow: $('#followMode').prop('checked')
                }, params);
            }
            fetchLogWithParams(params);
        }
        
        function fetchLogWithParams(params, callback) {
            if(loadIng){
                return;
            }
            loadIng = true;
            
            const start = new Date();
            $.ajax({
                url: '${ctxPath}/servlet/logger/tail',
                type: 'POST',
                data: params,
                dataType: 'text',
                success: function(data) {
                    const end = new Date();
                    const timeTaken = end - start;
                    
                    loadIng = false;
                    var content = highlightLogContent(data);
                    
                    // 处理grep模式下的高亮
                    if (params.grep) {
                        content = highlightGrepMatches(content, params.grep);
                        
                        // 更新分页信息
                        if (data.includes("totalPages:")) {
                            var matches = data.match(/totalPages:\s*(\d+)/);
                            if (matches && matches.length > 1) {
                                totalGrepPages = parseInt(matches[1]);
                                updatePaginationControls();
                            }
                        }
                    }
                    
                    var $logContent = $('#logContent');
                    var $pre = $logContent.find('pre');
                    if (!$pre.length) {
                        $pre = $('<pre></pre>');
                        $logContent.append($pre);
                    }
                    
                    if (params.follow && data.trim() !== "没有新内容") {
                        // follow模式下追加新内容
                        var currentContent = $pre.html() || '';
                        
                        // 如果当前内容超过1000行，移除最早的一些行
                        var lines = currentContent.split('\n');
                        if (lines.length > 1000) {
                            lines = lines.slice(-900); // 保留最后900行
                            currentContent = lines.join('\n');
                        }
                        
                        // 追加新内容
                        $pre.html(currentContent + content);
                        
                        // 如果启用了自动滚动,滚动到底部
                        if ($('#autoScroll').prop('checked')) {
                            $logContent[0].scrollTop = $logContent[0].scrollHeight;
                        }
                    } else {
                        // 非follow模式或没有新内容时直接替换
                        $pre.html(content);
                        
                        // 如果是grep模式,滚动到顶部
                        if (params.grep) {
                            $logContent.scrollTop(0);
                        } else {
                            // 否则滚动到底部
                            $logContent[0].scrollTop = $logContent[0].scrollHeight;
                        }
                    }
                    
                    $('.req-result').html('请求耗时：'+timeTaken+'毫秒,文本大小：'+calculateSize(data)+'KB');
                    
                    // 回调
                    if (callback) {
                        callback(true, data, timeTaken);
                    }
                    
                },
                error: function(error) {
                    loadIng = false;
                    layer.msg('请求出错: ' + error.statusText, {icon: 2});
                    
                    // 回调
                    if (callback) {
                        callback(false, error.statusText);
                    }
                }
            });
        }
        
        /**
         * 截断日志内容,保持最大行数限制
         * @param {jQuery} $pre pre元素
         * @param {number} maxLines 最大行数限制
         */
        function truncateLogContent($pre, maxLines) {
            // 获取pre标签内的所有div元素（每行日志）
            var $lines = $pre.children('div');
            
            if ($lines.length > maxLines) {
                // 移除多余的行（从开头移除）
                $lines.slice(0, $lines.length - maxLines).remove();
                
                // 输出提示信息
                console.log('日志内容已截断,保留最新的' + maxLines + '行，当前行数：' + $pre.children('div').length);
            }
        }
        
        // 高亮grep匹配
        function highlightGrepMatches(content, pattern) {
            try {
                // 查找 [[...]] 格式的匹配文本并替换为高亮格式
                const highlighted = content.replace(/\[\[(.*?)\]\]/g, '<span class="highlight-match">$1</span>');
                
                // 检查是否有错误消息
                if (highlighted.includes('正则表达式错误') || 
                    highlighted.includes('内存不足') || 
                    highlighted.includes('警告：内存使用过高')) {
                    layer.msg('搜索过程中出现问题，请查看日志内容', {icon: 2});
                }
                
                return highlighted;
            } catch (e) {
                console.error("高亮grep匹配出错", e);
                return content;
            }
        }
    	
    	function calculateSize(responseText) {
   		  const blob = new Blob([responseText]);
   		  const sizeInBytes = blob.size;
   		  const sizeInKB = sizeInBytes / 1024;
   		  return sizeInKB.toFixed(2);
    	}

        function clearLog() {
            $('#logContent').html('');
            stopLog();
            // 重置内存警告标志
            memoryWarningShown = false;
            $.get('${ctxPath}/servlet/logger/tail?action=clear');
        }
        
        /**
         * 清空日志内容但不发送clear请求
         */
        function clearLogContent() {
            $('#logContent').html('');
            memoryWarningShown = false;
        }
        
        
        function job(){
        	intervalId = setInterval(function() {
                fetchLog();
                count = count + 1;
                if (count >= 20) {
                    stopLog();
                    $('.auto-refresh').show();
                    $('.stop-btn').hide();
                    $('#msg').html('已触发20次，自动停止刷新。');
                } else {
                    $('.auto-refresh').hide();
                    $('.stop-btn').show();
                    $('#msg').html('已触发'+count+'次自动刷新，达到20次将停止');
                }

                // 每20次清理一次内容，而不是每10次
                if(count > 0 && count % 20 === 0){
                    var $pre = $('#logContent pre');
                    var lines = $pre.html().split('\n');
                    if (lines.length > 1000) {
                        // 保留最后900行
                        lines = lines.slice(-900);
                        $pre.html(lines.join('\n'));
                        console.log('日志已截断，保留最新的900行');
                    }
                }
            }, 2200);
        }
        
        function stopLog(){
       	  if(intervalId){
 	          clearInterval(intervalId);
       		  count = 0;
       		  $('.auto-refresh').show();
              $('.stop-btn').hide();
 	          $('#msg').html(new Date()+'已停止自动刷新');
           }
        }
        
        function manualFetchLog(){
         	var nowTime = new Date().getTime();
    		if(nowTime - reqTime < 1000){
    			layer.msg('点击太快了',{icon:7,offset:'20px',time:1200});
    			return;
    		}
    		reqTime = new Date().getTime();
    		
        	stopLog();
            // 清空现有内容
            clearLogContent();
        	fetchLog({follow: false});
        }
        
        function autoFetchLog(){
            count = 0;
            // 清空现有内容
            clearLogContent();
            // 第一次强制tail -n
            fetchLog({follow: false});
            $('.auto-refresh').hide();
            $('.stop-btn').show();
            // 后续定时增量刷新
            intervalId = setInterval(function() {
                fetchLog({follow: true});
                count = count + 1;
                if (count >= 20) {
                    stopLog();
                    $('.auto-refresh').show();
                    $('.stop-btn').hide();
                    $('#msg').html('已触发20次，自动停止刷新。');
                } else {
                    $('.auto-refresh').hide();
                    $('.stop-btn').show();
                    $('#msg').html('已触发'+count+'次自动刷新，达到20次将停止');
                }
                // 每20次清理一次内容，而不是每10次
                if(count > 0 && count % 20 === 0){
                    var $pre = $('#logContent pre');
                    var lines = $pre.html().split('\n');
                    if (lines.length > 1000) {
                        // 保留最后900行
                        lines = lines.slice(-900);
                        $pre.html(lines.join('\n'));
                        console.log('日志已截断，保留最新的900行');
                    }
                }
            }, 2200);
        }
        
        function highlightLogContent(logContent) {
            // 预编译正则表达式
            const timeRegex = /\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}/g;
            const partsToReplace = ['{','}','select','update','insert'];
            const regexCache = new Map();
            
            const lines = logContent.split('\n');
            const maxLines = 1000; // 最大处理行数限制
            
            // 如果行数超过限制，只处理最后的行
            const startIndex = Math.max(0, lines.length - maxLines);
            const processLines = lines.slice(startIndex);
            
            // 使用DocumentFragment优化DOM操作
            const fragment = document.createDocumentFragment();
            
            for (let i = 0; i < processLines.length; i++) {
                let rowStr = processLines[i];
                if (!rowStr) continue; // 跳过空行
                
                // 处理时间戳
                rowStr = rowStr.replace(timeRegex, '<span class="time-span">$&</span>');
                
                // 处理关键字高亮
                for (let part of partsToReplace) {
                    if (!regexCache.has(part)) {
                        regexCache.set(part, new RegExp(part, 'gi'));
                    }
                    const regex = regexCache.get(part);
                    rowStr = rowStr.replace(regex, '<span class="importantText">$&</span>');
                }
                
                // 创建div元素
                const div = document.createElement('div');
                
                // 根据内容类型设置样式
                if (rowStr.includes('WARN')) {
                    div.className = 'highlight-warning';
                } else if (rowStr.includes('params') && isSQL(rowStr)) {
                    div.className = 'highlight-sql';
                } else if (rowStr.includes('easitline')) {
                    div.className = 'highlight-easitline';
                } else if (rowStr.includes('yunqu') || rowStr.includes('.yq')) {
                    div.className = 'highlight-yq';
                } else if (rowStr.includes('ERROR') || rowStr.includes('Exception') || rowStr.includes('at ')) {
                    div.className = 'highlight-error';
                } else {
                    div.className = 'highlight-normal';
                }
                
                div.innerHTML = rowStr;
                fragment.appendChild(div);
            }
            
            // 返回处理后的HTML字符串
            const tempDiv = document.createElement('div');
            tempDiv.appendChild(fragment.cloneNode(true));
            return tempDiv.innerHTML;
        }
        
        function addSpansToText(text, partsToReplace) {
       	  for (let part of partsToReplace) {
       		 text = text.replace(/\n/g, '<br>');
       	     let timeRegex = /\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}/g;
       	     text = text.replace(timeRegex, `<span class="time-span">$&</span>`);
       		 let regex = new RegExp(part, 'gi'); 
       		 var newText = '<span class="importantText">'+part+'</span>';
       	     text = text.replace(regex, newText);
       	  }
       	  return text;
       	}
        
        function selectText(){
        	document.addEventListener('mouseup', function() {
       		  var selection = window.getSelection();
      		  var text = selection.toString();
      		  if(text.length > 0){
      			  text = text.trim();  
      			  text = text.replace(/^,/, '').replace(/,$/, '');
	       		  if(text.indexOf('{')>-1) {
	       			 text = text.replace(/\n/g, '');
	       			 sessionStorage.setItem("CONSOLE_JSON",text);
	       			 $('.json-view').show();
	       		   }else{
	       			 $('.json-view').hide();
	       		  }
	       		  
	       		  if(isSQL(text)){
	       			 $('.sql-view').show();
	       			 sessionStorage.setItem("CONSOLE_SQL",text);
	       		  }else{
	       			 $('.sql-view').hide();
	       		  }
      		  }
       	   });
        }
        
        function jsonView(){
      		layer.open({id:'JsonView',type:2,title:'Json解析',content:'${ctxPath}/servlet/pages?action=jsonView',maxmin:true,shade: 0.1,fixed: false,shadeClose:false,offset:'20px',area:['80%','80%']});
        }
        
        function isSQL(text) {
        	  const keywords = ['select', 'update', 'delete', 'insert'];
        	  for (const keyword of keywords) {
        	    if (text.toLowerCase().includes(keyword)) {
        	      return true;
        	    }
        	  }
        	  return false;
        }
        
        function sqlView(){
        	var content = sessionStorage.getItem("CONSOLE_SQL");
        	$.ajax({
    		    url: '${ctxPath}/servlet/dsTool?action=formatContent',
    		    type: 'POST',
    		    data:{content:generateSQLFromText(content)},
    		    dataType: 'json',
    		    success: function(result) {
					layer.prompt({value:sqlFormat(result.data),title:'格式化的SQl',maxlength:100000,formType: 2,area: ['600px', '400px']},function(val){
						copyText(val);
						layer.msg('复制成功',{icon:7,offset:'20px',time:1200});
    		    	});
    		    }
    		 });
        }
        
        function sqlFormat(text){
       	  text = text.replace(/&gt;/g, '>');
       	  text = text.replace(/&lt;/g, '<');
       	  return text;
        }
        
        
        function generateSQLFromText(sqlWithParamsText) {
        	  // 按逗号分割参数部分
        	  let cleanedText = sqlWithParamsText.replace('params[{', 'params[').replace('}]', ']');
        	  let parts = cleanedText.split(',params[');
        	  let sql = parts[0];

        	  let paramsPart = parts[1];
        	  if (paramsPart) {
        	    paramsPart = paramsPart.slice(0, paramsPart.length - 1);
        	    let params = paramsPart.split(',');
        	    let generatedSQL = sql;
        	    for (let i = 0; i < params.length; i++) {
        	    	var pv = params[i];
        	    	if(isInteger(pv)){
	        	        generatedSQL = generatedSQL.replace('?',pv);
        	    	}else{
        	        	generatedSQL = generatedSQL.replace('?','\''+pv+'\'');
        	    	}
        	    }
        	    return generatedSQL;
        	  } else {
        	    return sql;
        	  }
       	}
        
        function isInteger(obj) {
      		  if (typeof obj!== 'number') {
      		    return false;
      		  }
      		  if (isNaN(obj) ||!isFinite(obj)) {
      		    return false;
      		  }
      		  return obj % 1 === 0;
       	}
        
        function copyText(text) {
        	  const textarea = document.createElement('textarea');
        	  textarea.value = text;
        	  document.body.appendChild(textarea);
        	  textarea.select();
        	  document.execCommand('copy');
        	  document.body.removeChild(textarea);
       	}
        	
        $(function(){
       	    layui.use(['layer', 'form'], function(){
                layer = layui.layer;
                var form = layui.form;
                
                // 初始化表单组件
                form.render();
                
                // 隐藏停止按钮
                $('.stop-btn').hide();
                
                // 尝试恢复上次的搜索参数
                try {
                    var savedSearch = localStorage.getItem('lastGrepSearch');
                    if (savedSearch) {
                        var searchParams = JSON.parse(savedSearch);
                        $('#grepPattern').val(searchParams.pattern);
                        $('#caseSensitive').prop('checked', searchParams.caseSensitive);
                        $('#contextLines').val(searchParams.contextLines);
                        $('#grepPageSize').val(searchParams.pageSize);
                        form.render('checkbox'); // 更新复选框UI
                    }
                } catch (e) {
                    console.error('无法恢复搜索参数', e);
                }
                
                // 加载搜索历史
                loadSearchHistory();
                
	        	autoFetchLog();
	        	selectText();
            });
        });
    </script>
</body>
</html>