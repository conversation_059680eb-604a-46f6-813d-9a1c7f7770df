<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<!DOCTYPE html>
<html lang="en">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<title>Json Parser Online</title>
	<script type="text/javascript" src="${staticPath}/js/jquery.min.js"></script> 
	<link rel="stylesheet" href="${ctxPath}/pages/json/parse_new.css">
	<script src="${ctxPath}/pages/json/parse_new.js"></script>
</head>
<body class="json-empty">
		<div id="header" class="a"><b style="margin-right: 30px;">JSON在线解析<!-- (<font color="red">双击自动格式化</font>) --></b>
			<div class="c">
				<div class="n b"><span class="l">选项</span><span class="m">▼</span>
					<div class="j g h">
						<div class="e d on" id="yM">左右模式</div>
						<div class="e d" id="xL">上下模式</div>
						<div class="f"></div>
						<div class="e on" id="gV">解析JSON</div>
						<div class="e on" id="jY">执行JSON</div>
						<div class="f"></div>
						<div class="e" id="zN">压缩</div>
						<div class="e on" id="cR">着色</div>
						<div class="e" id="bQ">显示JS类型</div>
						<div class="e" id="aP">显示数组索引</div>
					</div>
				</div>
			</div>
		</div>
		<div id="split" class="N I" style="height: 953px; top: 41px;"><textarea class="J x P" id="eT" spellcheck="false" style="height: 941px; width: 749px;">${param.json }</textarea>
			<div class="M ui-draggable" style="left: 781px;"></div>
			<div class="K F color" id="fU" style="width: 1225px;">
				<div class="y Q">
					<div class="C A" id="hW" style="width: 604px;">准备就绪</div>
					<div class="C B" id="kZ">&nbsp;</div>
				</div>
				<div class="z L" style="height: 913.2px;">
					<div class="E" id="dS">
						<div class="C A" style="width: 604px; float: left;">
							<div class="w">
								<div class="P" id="iX"></div>
							</div>
						</div>
						<div class="C B" style="width: 603px;">
							<div class="w D">
								<div class="P" id="lA"></div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<script type="text/javascript">
			String.prototype.replaceAll = function(s1,s2){ 
				return this.replace(new RegExp(s1,"gm"),s2); 
			}
			$(function(){
				var json = sessionStorage.getItem("CONSOLE_JSON");
				json = json.replaceAll("'","\"");
				$("#eT").val(json);
				$("#eT").click();
			});
		</script>
</body>
</html>