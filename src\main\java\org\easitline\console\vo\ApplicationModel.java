package org.easitline.console.vo;

import java.util.ArrayList;
import java.util.List;

import org.easitline.common.utils.calendar.EasyCalendar;

import com.alibaba.fastjson.JSONObject;


/**
 * 应用模型
 * <AUTHOR>
 *
 */
public class ApplicationModel implements java.io.Serializable {


	private static final long serialVersionUID = 6530881948701950222L;
	private String id;            //id
	private String appId;         //应用标识
	private String appName;       //应用名称
	private String appVersion;    //应用版本
	private String appVersionDesc;//应用版本详细描述
	private String warName;       //war包名称
	private String deployTime;    //部署时间
	private String appFilePath; //路径
	private String lastModified;//war打包时间
	private String warSize;//war大小
	private String resTable;
	private String fileType;//war or jar
	private String depoyDir;
	private String vdate;//发版日期
	
	private List<AppdsModel> dsList = new ArrayList<AppdsModel>();  //数据源
	

	public void setAppDescInfo(JSONObject jsonObject){
		for(AppdsModel appds:dsList){
			appds.setSysDsName(jsonObject.getString(appds.getDsName()));
		}
	}
	
	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getAppName() {
		return appName;
	}

	public void setAppName(String appName) {
		this.appName = appName;
	}

	public String getAppVersion() {
		return appVersion;
	}

	public void setAppVersion(String appVersion) {
		this.appVersion = appVersion;
	}

	public String getWarName() {
		return warName;
	}

	public void setWarName(String warName) {
		this.warName = warName;
	}

	public String getDeployTime() {
		
		if(this.deployTime == null) return EasyCalendar.newInstance().getDateString("-");
		return deployTime;
	}

	public void setDeployTime(String deployTime) {
		this.deployTime = deployTime; 
	}

	public List<AppdsModel> getDsList() {
		return dsList;
	}

	public void setDsList(List<AppdsModel> dsList) {
		this.dsList = dsList;
	}

	public String getAppFilePath() {
		return appFilePath;
	}

	public void setAppFilePath(String appFilePath) {
		this.appFilePath = appFilePath;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getAppVersionDesc() {
		return appVersionDesc;
	}

	public void setAppVersionDesc(String appVersionDesc) {
		this.appVersionDesc = appVersionDesc;
	}

	public String getLastModified() {
		return lastModified;
	}

	public void setLastModified(String lastModified) {
		this.lastModified = lastModified;
	}

	public String getWarSize() {
		return warSize;
	}

	public void setWarSize(String warSize) {
		this.warSize = warSize;
	}

	public String getResTable() {
		return resTable;
	}

	public void setResTable(String resTable) {
		this.resTable = resTable;
	}

	public String getFileType() {
		return fileType;
	}

	public void setFileType(String fileType) {
		this.fileType = fileType;
	}

	public String getDepoyDir() {
		return depoyDir;
	}

	public void setDepoyDir(String depoyDir) {
		this.depoyDir = depoyDir;
	}

	public String getVdate() {
		return vdate;
	}

	public void setVdate(String vdate) {
		this.vdate = vdate;
	}
	
	
	
}
