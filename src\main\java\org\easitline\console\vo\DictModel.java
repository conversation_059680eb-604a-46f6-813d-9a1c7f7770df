package org.easitline.console.vo;

import java.util.List;

import com.alibaba.fastjson.JSONObject;

/**
 * 字典模型
 * <AUTHOR>
 *  格式：
 	<dict  id = "S001"  name = "性别"  index = "1"  type = "sys">
		<item  id = "0"  name = "未知性别" 	pid="" 	desc = "" />
		<item  id = "1"  name = "男" 		pid=""  desc = "" />
		<item  id = "2"  name = "女"  		pid=""	desc = "" />
		<item  id = "9"  name = "保密"  		pid=""	desc = "" />
	</dict>
 */
public class DictModel {
	private String id;
	private String name;
	private int index;
	private String type;
	private List<JSONObject>  items;
	private String appId;
	
	
	
	public String getAppId() {
		return appId;
	}
	public void setAppId(String appId) {
		this.appId = appId;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public int getIndex() {
		return index;
	}
	public void setIndex(int index) {
		this.index = index;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public List<JSONObject> getItems() {
		return items;
	}
	public void setItems(List<JSONObject> items) {
		this.items = items;
	}
	
	

}
