<%@ page pageEncoding="UTF-8"%>
<div>
	<blockquote class="layui-elem-quote">
		这里可以对Console的登录账号和密码进行修改，如果忘记了Console的登录账号或密码，请与开发厂商联系进行后台重置！
	</blockquote>

	<fieldset class="layui-elem-field layui-field-title">
		<legend>修改密码</legend>
	</fieldset>
	
	<form class="layui-form" autocomplete="off">
	  <div class="layui-form-item layui-hide">
	    <label class="layui-form-label">新登录帐号</label>
	    <div class="layui-input-block">
	      <input type="text" name="acct" class="layui-input" placeholder="新登录账号">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">旧密码</label>
	    <div class="layui-input-block">
	       <input type="text" name="oldpwd" lay-verify="required"  class="layui-input" placeholder="当前登录密码">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">新密码</label>
	    <div class="layui-input-block">
	      <input type="password" name="newpwd" lay-verify="required" class="layui-input" placeholder="新登录密码">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">重复新密码</label>
	    <div class="layui-input-block">
	      <input type="password" name="renewpwd" lay-verify="required"  class="layui-input" placeholder="重复新密码">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <div class="layui-input-block">
	      <button type="button" class="layui-btn layui-btn-small layui-btn-normal" lay-submit lay-filter="submitBtn" style="padding:0 20px"> 保存 </button>
	    </div>
	  </div>
	</form>
</div>


<script type="text/javascript">
	layui.use('form', function(){
		  var form = layui.form;
		  //监听提交
		  form.on('submit(submitBtn)', function(data){
			  docheck(data.field);
		  });
	});
	
   function docheck(data){
	  delete data['renewpwd'];
	  var dataStr = aesEncrypt(JSON.stringify(data));
	  ajax.remoteCall("${ctxPath}/servlet/user?action=modifyLoginInfo",{encryptStr:dataStr},function(result) { 	
			if(result.state == 1){
				layer.msg(result.msg,{icon:1,time:800},function(){
					layer.closeAll();
					location.href = "${ctxPath}/index";
				});
			}else{
				layer.alert(result.msg);
			}
		}
	 );

    }
</script>


