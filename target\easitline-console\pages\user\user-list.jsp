<%@page pageEncoding="UTF-8"%>
<div>
	<blockquote class="layui-elem-quote">
		用户列表
	</blockquote>
	<form id="easyform-job-list">
		<div class="layui-form" style="padding-left:15px">
			<table class="layui-table text-center" data-mars="NodeDao.list">
				<thead>
					<tr>
						<th>账号</th>
						<th>角色类型</th>
						<th>状态</th>
						<th>创建时间</th>
						<th>登录时间</th>
						<th>登录IP</th>
						<th>手机号码</th>
						<th>操作</th>
					</tr>
				</thead>
				<tbody id="dataList">
			    </tbody>
			</table>
			<button type="button" style="margin-top:6px;margin-left:10px" class="layui-btn layui-btn-small layui-btn-normal" onclick="addUser()">新增用户</button>
	    </div>
    </form>
</div>
<script id="userlist-template" type="text/x-jsrender">
			{{for list}}
				<tr>
					<td class="td-textcut">{{:LOGIN_ACCT}}</td>
					<td class="td-textcut">{{call:ROLE_ID fn='roleTypeDesc'}}</td>
					<td class="td-textcut">{{if STATE=='0'}}正常{{else}}<font color="red">停用</font>{{/if}}</td>
					<td>{{:CREATE_TIME}}</td>
					<td>{{:LAST_LOGIN_IP}}</td>
					<td>{{:LAST_LOGIN_TIME}}</td>
					<td class="td-textcut">{{:MOBILE}}</td>
					<td>
						<a href="javascript:;" onclick="editUser('{{:USER_ID}}');">修改</a>&nbsp;
						<a href="javascript:delUser('{{:USER_ID}}')">删除</a>&nbsp;
						<a href="javascript:resetUserPwd('{{:USER_ID}}')">重置密码</a>&nbsp;
					</td>
				</tr>
		 {{/for}}
</script>

<script type="text/javascript">
	
	$(function(){
		ajax.remoteCall("${ctxPath}/servlet/user?action=userList",{}, function(result) { 
			var data = result.data;
			var tmp = $.templates('#userlist-template');
			var html = tmp.render({list:data});
			$('#dataList').html(html);
		});
	});
	
	function editUser(userId){
		loadPage("user/user-edit.jsp",{userId:userId});
	}
	
	function addUser(userId){
		loadPage("user/user-edit.jsp",{userId:''});
	}
	
	function delUser(userId){
		layer.confirm("是否删除数据",{btn: [ '删除', '取消'],icon: 3, title:'警告',offset:'40%'},function(index){
			layer.close(index);
			ajax.remoteCall("${ctxPath}/servlet/user?action=delUser",{userId:userId},function(result) { 	
				if(result.state==1){
					layer.msg(result.msg,{icon: 1,time:800},function(){
						location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}, 
		function(index){
			layer.close(index);
		}
	  );
	}
	
	function resetUserPwd(userId){
		layer.confirm("是否重置密码",{btn: [ '确认', '取消'],icon: 3, title:'警告',offset:'40%'},function(index){
			layer.close(index);
			ajax.remoteCall("${ctxPath}/servlet/user?action=resetUserPwd",{userId:userId},function(result) { 	
				if(result.state==1){
					layer.alert('新密码 '+result.data+' 请复制妥善保存',{icon: 1},function(){
						location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}, 
		function(index){
			layer.close(index);
		}
	  );
	}
	
	function roleTypeDesc(id){
		if(id=='1'){
			return '管理员';
		}else if(id=='2'){
			return '只读用户';
		}else if(id=='9'){
			return '日志查看';
		}else if(id=='4'){
			return '超管';
		}
		return '其他';
	}
	
</script>
