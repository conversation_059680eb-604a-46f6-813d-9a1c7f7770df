<%@ page pageEncoding="UTF-8"%>
<style type="text/css">
	.layui-form-label{width: 250px;}
	.layui-input-block{margin-left: 280px;}
	.layui-form-item .layui-input-inline{width: 400px;}
</style>
<div>
	<fieldset class="layui-elem-field layui-field-title">
		<legend>MQ配置</legend>
	</fieldset>
	
	<form class="layui-form" id="easyform" autocomplete="off">
	  <div class="layui-form-item">
	    <label class="layui-form-label">请选择MQ类型/MQ_TYPE</label>
	     <div class="layui-input-block">
	      <select name="MQ_TYPE" class="layui-select">
	        <option value="">请选择</option>
	        <option value="activemq"  selected="selected">activemq</option>
	        <option value="kafka">kafka</option>
	        <option value="rocketmq">rocketmq</option>
	        <option value="ctgmq">ctgmq</option>
	        <option value="tonglinkq">tonglinkq</option>
	      </select>
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">服务地址/MQ_ADDR</label>
	    <div class="layui-input-block">
	      <textarea name="MQ_ADDR" class="layui-textarea" placeholder="tcp://127.0.0.1:61616"></textarea>
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">帐号/MQ_USERNAME</label>
	    <div class="layui-input-block">
	      <input type="text" name="MQ_USERNAME" class="layui-input" placeholder="默认不需要填">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">密码/MQ_PASSWORD</label>
	    <div class="layui-input-block">
	      <input type="password" name="MQ_PASSWORD" class="layui-input" placeholder="默认不需要填">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">集群名/MQ_CLUSTER_NAME</label>
	    <div class="layui-input-block">
	      <input type="text" name="MQ_CLUSTER_NAME" class="layui-input" placeholder="默认不需要填">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">租户ID/MQ_TENANTID</label>
	    <div class="layui-input-block">
	      <input type="text" name="MQ_TENANTID" class="layui-input" placeholder="默认不需要填">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">消费组名称/MQ_CONSUMER_GROUP</label>
	    <div class="layui-input-block">
	      <input type="text" name="MQ_CONSUMER_GROUP" class="layui-input" placeholder="默认不需要填">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">缺省队列前缀/MQ_PREFIX</label>
	    <div class="layui-input-block">
	      <input type="text" name="MQ_PREFIX" class="layui-input" placeholder="默认不需要填">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <div class="layui-input-block">
	      <button type="button" class="layui-btn layui-btn-small layui-btn-normal" lay-submit lay-filter="submitBtn" style="padding:0 20px"> 保存 </button>
	    </div>
	  </div>
	</form>
</div>


<script type="text/javascript">

  $(function(){
		layui.use('form', function(){
			getConf();
	         var layuiform = layui.form;
	         layuiform.render('select');
	         layuiform.render();
			 layuiform.on('submit(submitBtn)', function(data){
				 doExcute(data.field);
			 });
	     });
	});
	
	function getConf(){
		ajax.remoteCall("${ctxPath}/servlet/mqConf?action=getConf",{}, function(result) { 
			var encryptStr = result.data;
			var data = JSON.parse(aesDecrypt(encryptStr));
			if(data){
				fillRecord(data);
				layui.use('form', function(){
			         var layuiform = layui.form;
			         layuiform.render('select');
			         layuiform.render('radio');
			         layuiform.render();
			     });
			}
		});
	}
	function doExcute(datas) {
		var data = JSON.stringify(form.getJSONObject("easyform")); 
		var dataStr = aesEncrypt(data);
		ajax.remoteCall("${ctxPath}/servlet/mqConf?action=saveConf",{encryptStr:dataStr},function(result) { 	
			if(result.state==1){
				layer.msg(result.msg,{icon: 1,time:800},function(){
					layer.closeAll();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
</script>


