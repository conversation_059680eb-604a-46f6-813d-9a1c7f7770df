package org.easitline.console.servlet;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpSession;

import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.apache.commons.lang3.StringUtils;
import org.easitline.console.base.ConsoleBaseServlet;
import org.easitline.console.base.Constants;
import org.easitline.console.vo.AppConfigModel;
import org.easitline.console.vo.AppConfigRowMapper;

import com.alibaba.fastjson.JSONObject;

@WebServlet("/node/server/*")
public class NodeServerServlet extends ConsoleBaseServlet {
	private static final long serialVersionUID = 1L;

	public void actionForAuthTest() {
		String key  = getPara("key");
		String serverKey  = ServerContext.getProperties("SECURITY_KEY", "");
		if(StringUtils.isBlank(key)) {
			renderJson(EasyResult.fail("请配置安全KEY"));
			return;
		}
		if(StringUtils.isBlank(serverKey)) {
			renderJson(EasyResult.fail("请求的节点没配置安全KEY"));
			return;
		}
		if(!key.equals(serverKey)) {
			renderJson(EasyResult.fail("请求的节点和当前节点安全KEY不一致"));
			return;
		}
		Render.renderJsonp(getRequest(), getResponse(), EasyResult.ok());
		return;
	}
	
	public void actionForAuth() {
		String referer = getRequest().getHeader("Referer");
		if(StringUtils.isBlank(referer)) {
			renderHtml("非法访问");
			return;
		}
		String checkResult  = authCheck();
		if(checkResult!=null) {
			renderHtml(checkResult);
			return;
		}
		HttpSession session = this.getRequest().getSession();
		session.setMaxInactiveInterval(60*30);
		session.setAttribute("MARS_CONSOLE_USER", "admin@mars");
		session.setAttribute("MARS_CONSOLE_ROLE", 1);
		redirect(Constants.getContextPath()+"/index");
	}
	
	private String authCheck() {
		String key  = getPara("key");
		String serverKey  = ServerContext.getProperties("SECURITY_KEY", "");
		if(StringUtils.isBlank(key)) {
			return "请配置安全KEY";
		}
		if(StringUtils.isBlank(serverKey)) {
			return "当前节点没配置安全KEY";
		}
		if(!key.equals(serverKey)) {
			return "节点的安全KEY不一致";
		}
		return null;
	}
	
	public EasyResult actionForGetAppConfig() {
		String checkResult  = authCheck();
		if(checkResult!=null) {
			return EasyResult.fail(checkResult);
		}
		String appId = getPara("appId");
		try {
			List<JSONObject> list = this.getConsoleQuery().queryForList("select * from EASI_APP_CONF where APP_ID = ? order by ORDER_INDEX", new Object[]{appId},new JSONMapperImpl());
			return EasyResult.ok(list);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForUpdateConfig()throws Exception{
		String checkResult  = authCheck();
		if(checkResult!=null) {
			return EasyResult.fail(checkResult);
		}
		JSONObject jsonObject = this.getJSONObject();
		String appId = jsonObject.getString("appId");
		if(appId == null || "".equals(appId)){
			return EasyResult.error(501,"更新应用失败，原因：无效的应用参数[APP_ID]");
		}
		try{
			String sql = "select * from EASI_APP_CONF  where APP_ID = ?";
			List<AppConfigModel> list = this.getConsoleQuery().queryForList(sql, new Object[]{appId},new AppConfigRowMapper());
			long tempVal=System.currentTimeMillis();
			for(AppConfigModel config:list){
				sql = "update  EASI_APP_CONF  set ITEM_VALUE =? where APP_ID = ? and ITEM_KEY = ? ";
				Object[] params = new Object[]{StringUtils.trimToEmpty(jsonObject.getString(config.getItemKey())), config.getAppId(),config.getItemKey()};
				this.getConsoleQuery().execute(sql, params);
				
				sql = "insert into EASI_APP_CONF_HIS(APP_ID,APP_VERSIOIN,BACKUP_TIME,ITEM_KEY,ITEM_VALUE) values(?,?,?,?,?)";
			    params = new Object[] {appId,tempVal,EasyDate.getCurrentDateString(),config.getItemKey(),jsonObject.getString(config.getItemKey())};
			    this.getConsoleQuery().execute(sql, params);
			}
			return EasyResult.ok(null,"保存成功");
		} catch (SQLException ex) {
			this.error(null, ex);
			return EasyResult.error(501,"更新数据源失败，原因："+ex.getMessage());
		}finally{
			AppContext.getContext(appId,true);
		}

	}
	
	
	
}
