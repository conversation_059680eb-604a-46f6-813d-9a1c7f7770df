<%@ page pageEncoding="UTF-8"%>
<div>
	<blockquote class="layui-elem-quote">
		<a style="cursor: pointer;" onclick="cacheTest()">测试连接</a>
		<a style="margin-left: 30px;" href="javascript:esReload();">重载连接</a>
		<a style="margin-left: 30px;" href="javascript:readRsInfo();">ES信息</a>
		<a style="margin-left: 30px;" href="javascript:readStatus();">ES监控</a>
	</blockquote>
	<form class="layui-form" id="easyform" autocomplete="off">
	
	<div class="layui-tab layui-tab-brief">
		  <ul class="layui-tab-title">
		    <li class="layui-this">Elasticsearch配置</li>
		    <li>扩展配置</li>
		    <li>ES索引</li>
		  </ul>
		  <div class="layui-tab-content">
		    <div class="layui-tab-item layui-show">
		    	 <div class="layui-form-item">
				    <label class="layui-form-label">服务地址/ES_URL</label>
				    <div class="layui-input-block">
				      <textarea name="ES_URL" class="layui-textarea" placeholder="*************:9200,*************:9200"></textarea>
				    </div>
				  </div>
				  <div class="layui-form-item">
				    <label class="layui-form-label">帐号/ES_USER</label>
				    <div class="layui-input-block">
				      <input type="text" name="ES_USER" class="layui-input" placeholder="默认不需要填">
				    </div>
				  </div>
				  <div class="layui-form-item">
				    <label class="layui-form-label">密码/ES_PASS</label>
				    <div class="layui-input-block">
				      <input type="password" name="ES_PASS" class="layui-input" placeholder="默认不需要填">
				    </div>
				  </div>
				  <div class="layui-form-item">
				    <label class="layui-form-label">应用场景</label>
				    <div class="layui-input-block">
				      	<div class="layui-input-inline">
					      <select name="conf.mode" lay-filter="sysMode" class="layui-select">
					        <option value="1">小型应用</option>
					        <option value="2">中型应用</option>
					        <option value="3" selected="selected">大型应用</option>
					      </select>
					    </div>
				    </div>
				    <div class="layui-form-mid layui-word-aux">不同应用场景推荐不同初始的分片和副本数</div>
				  </div>
				  <div class="layui-form-item">
				    <label class="layui-form-label">分片数</label>
				    <div class="layui-input-block">
				      <input type="number" value="5" placeholder="" id="shards" name="conf.shards" class="layui-input">
				    </div>
				    <div class="layui-form-mid layui-word-aux">每个分片推荐大小：20GB-40GB</div>
				  </div>
				  <div class="layui-form-item">
				    <label class="layui-form-label">副本数</label>
				    <div class="layui-input-block">
				      <input type="number" value="2" placeholder="" id="replicas" name="conf.replicas" class="layui-input">
				    </div>
				    <div class="layui-form-mid layui-word-aux">生产环境：至少1个副本保证高可用</div>
				  </div>
				  <div class="layui-form-item">
				    <div class="layui-input-block">
				      <button type="button" class="layui-btn layui-btn-small layui-btn-normal" lay-submit lay-filter="submitBtn" style="padding:0 20px"> 保存 </button>
				    </div>
				  </div>
		    </div>
		    <div class="layui-tab-item">
				  <div class="layui-form-item">
				    <label class="layui-form-label">最大连接总数</label>
				    <div class="layui-input-block">
				      <input type="number" value="200" name="conf.maxConnTotal" class="layui-input">
				    </div>
				  </div>
				  <div class="layui-form-item">
				    <label class="layui-form-label">连接超时时间(毫秒)</label>
				    <div class="layui-input-block">
				      <input type="number" value="5000" name="conf.connectTimeout" class="layui-input">
				    </div>
				  </div>
				  <div class="layui-form-item">
				    <label class="layui-form-label">Socket超时时间(毫秒)</label>
				    <div class="layui-input-block">
				      <input type="number" value="60000" name="conf.socketTimeout" class="layui-input">
				    </div>
				  </div>
				  <div class="layui-form-item">
				    <label class="layui-form-label">每个路由的最大连接数</label>
				    <div class="layui-input-block">
				      <input type="number" value="10" name="conf.maxConnPerRoute" class="layui-input">
				    </div>
				  </div>
				  <div class="layui-form-item">
				    <label class="layui-form-label">是否跳过 SSL 验证</label>
				    <div class="layui-input-block">
				      	<div class="layui-input-inline">
					      <select name="conf.skipSslVerification" class="layui-select">
					        <option value="1" selected="selected">是</option>
					        <option value="0">否</option>
					      </select>
					    </div>
				    </div>
				  </div>
				  <div class="layui-form-item">
				    <label class="layui-form-label">协议</label>
				    <div class="layui-input-block">
				      	<div class="layui-input-inline">
					      <select name="conf.scheme" class="layui-select">
					        <option value="http" selected="selected">http</option>
					        <option value="https">https</option>
					      </select>
					    </div>
				    </div>
				  </div>
				  <div class="layui-form-item">
				    <label class="layui-form-label">心跳检测</label>
				    <div class="layui-input-block">
				      	<div class="layui-input-inline">
					      <select name="conf.healthChecker" class="layui-select">
					        <option value="0" selected="selected">关闭</option>
					        <option value="1">开启</option>
					      </select>
					    </div>
				    </div>
				  </div>
				  <div class="layui-form-item">
				    <label class="layui-form-label">是否启用压缩</label>
				    <div class="layui-input-block">
				      	<div class="layui-input-inline">
					      <select name="conf.compressionEnabled" class="layui-select">
					        <option value="1" selected="selected">开启</option>
					        <option value="0">关闭</option>
					      </select>
					    </div>
				    </div>
				    <div class="layui-form-mid layui-word-aux">减少网络传输量，节省带宽，但增加CPU开销（压缩/解压）</div>
				  </div>
				  <div class="layui-form-item">
				    <label class="layui-form-label">批量插入数</label>
				    <div class="layui-input-block">
				      <input type="number" value="1000" placeholder="" id="bulkSize" name="conf.bulkSize" class="layui-input">
				    </div>
				    <div class="layui-form-mid layui-word-aux">如果文档较大（>100KB），建议减小bulkSize到100-500；如果文档很小（<1KB），可以适当增加到2000-5000；
				    	<br>太小的bulkSize会导致请求次数过多，增加网络开销；太大的bulkSize会占用更多内存，并可能导致请求超时
				    </div>
				  </div>
				  <div class="layui-form-item">
				    <label class="layui-form-label">批量插入并发请求数</label>
				    <div class="layui-input-block">
				      <input type="number" value="10" placeholder="批量请求的并发请求数"  name="conf.bulkConcurrentRequests" class="layui-input">
				    </div>
				    <div class="layui-form-mid layui-word-aux">避免过多的并发请求导致服务器压力过大</div>
				  </div>
		    </div>
		    <div class="layui-tab-item">
		    	<a href="javascript:;" onclick="allIndices();" style="margin-bottom: 10px;display: block;">立即获取</a>
		    	<textarea id="esIndexAll" class="layui-textarea" style="height: 300px;"></textarea>
		    </div>
		  </div>
		</div>
	</form>
</div>


   <script id="node-template" type="text/x-jsrender">
        <h2>所有节点统计信息</h2>
        {{for nodes}}
            <div class="node">
                <h3>节点 ID: {{:id}}</h3>
                {{if os}}
                    <h4>操作系统信息:</h4>
                    <p><strong>可用 CPU 核心数:</strong> {{:os.available_processors}}</p>
                    <p><strong>CPU 使用率:</strong> {{if os.cpu}} {{:os.cpu.percent}} % {{else}} 数据不可用 {{/if}}</p>
                    <p><strong>操作系统负载:</strong> {{if os.cpu}} {{:os.cpu.load_1m}} (过去1分钟) {{else}} 数据不可用 {{/if}}</p>
                {{/if}}
                {{if jvm}}
                    <h4>JVM 信息:</h4>
                    <p><strong>JVM 堆内存使用量:</strong> {{:jvm.mem.heap_used_in_bytes / (1024 * 1024)}} MB</p>
                    <p><strong>JVM 堆内存最大值:</strong> {{:jvm.mem.heap_max_in_bytes / (1024 * 1024)}} MB</p>
                {{/if}}
                {{if fs}}
                    <h4>文件系统信息:</h4>
                    <p><strong>磁盘总空间:</strong> {{:fs.total.total_in_bytes / (1024 * 1024 * 1024)}} GB</p>
                    <p><strong>磁盘已用空间:</strong> {{:fs.total.used_in_bytes / (1024 * 1024 * 1024)}} GB</p>
                    <p><strong>磁盘空闲空间:</strong> {{:fs.total.free_in_bytes / (1024 * 1024 * 1024)}} GB</p>
                {{/if}}
                {{if network}}
                    <h4>网络信息:</h4>
                    <p><strong>传入流量:</strong> {{if network.rx}} {{:network.rx.bytes / (1024 * 1024)}} MB {{else}} 数据不可用 {{/if}}</p>
                    <p><strong>传出流量:</strong> {{if network.tx}} {{:network.tx.bytes / (1024 * 1024)}} MB {{else}} 数据不可用 {{/if}}</p>
                {{/if}}
                {{if process}}
                    <h4>进程信息:</h4>
                    <p><strong>进程使用的 CPU 时间:</strong> {{if process.cpu}} {{:process.cpu.percent}} % {{else}} 数据不可用 {{/if}}</p>
                    <p><strong>进程内存使用量:</strong> {{:process.mem.resident_in_bytes / (1024 * 1024)}} MB</p>
                {{/if}}
                {{if thread_pool}}
                    <h4>线程池信息:</h4>
                    <p><strong>活跃线程池数量:</strong> {{:thread_pool.active}}</p>
                    <p><strong>线程池队列大小:</strong> {{:thread_pool.queue}}</p>
                {{/if}}
                <hr />
            </div>
        {{/for}}
    </script>
    
<script type="text/javascript">

  $(function(){
		layui.use('form', function(){
			getConf();
	         var layuiform = layui.form;
	         layuiform.render('select');
	         layuiform.render();
			 layuiform.on('submit(submitBtn)', function(data){
				 doExcute(data.field);
			 });
			 
			 layuiform.on('select(sysMode)', function(data){
				 if(data.value=='1'){
					 $('#shards').val(1);
					 $('#replicas').val(0);
				 }
				 if(data.value=='2'){
					 $('#shards').val(3);
					 $('#replicas').val(1);
				 }
				 if(data.value=='3'){
					 $('#shards').val(5);
					 $('#replicas').val(2);
				 }
			});
	     });
	});
	
	function getConf(){
		ajax.remoteCall("${ctxPath}/servlet/esConf?action=getConf",{}, function(result) { 
			var encryptStr = result.data;
			var data = JSON.parse(aesDecrypt(encryptStr));
			if(data){
				fillRecord(data);
				
				var configJsonStr = data.ES_CONFIG;
				if(configJsonStr){
					var configJson  = JSON.parse(configJsonStr);
					fillRecord(configJson,"conf.",",","#easyform");
				}
				
				layui.use('form', function(){
			         var layuiform = layui.form;
			         layuiform.render('select');
			         layuiform.render('radio');
			         layuiform.render();
			     });
			}
		});
	}
	
	function getParamsWithPrefix(json, prefix) {
		  let result = {};
		  for (let key in json) {
		    if (key.startsWith(prefix)) {
		      let newKey = key.slice(prefix.length); 
		      result[newKey] = json[key];
		    }
		  }
		  return result;
	}
	
	function doExcute(datas) {
		var _json = form.getJSONObject("easyform");
		var configJson = getParamsWithPrefix(_json,'conf.');		
		_json['ES_CONFIG'] = JSON.stringify(configJson);
		
		var data = JSON.stringify(_json); 
		var dataStr = aesEncrypt(data);
		ajax.remoteCall("${ctxPath}/servlet/esConf?action=saveConf",{encryptStr:dataStr},function(result) { 	
			if(result.state==1){
				layer.msg(result.msg,{icon: 1,time:800},function(){
					layer.closeAll();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
	
	function cacheTest(){
		ajax.remoteCall("${ctxPath}/servlet/esConf?action=esTest",{}, function(result) {
			if(result.state==1){
				layer.msg('测试成功');
			}else{
				layer.alert(result.msg||'连接失败');
			}
		});
	}
	
	function esReload() {
		ajax.remoteCall("${ctxPath}/servlet/esConf?action=esReload",{},function(result) { 	
			if(result.state==1){
				layer.msg(result.msg+"请重启服务器配置生效。",{icon: 1,time:800},function(){
					layer.closeAll();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
	
	function readRsInfo(){
		ajax.remoteCall("${ctxPath}/servlet/esConf?action=esInfo",{}, function(result) {
			if(result.state==1){
				var str = result.data;
				layer.alert(str,{title:'ES信息',area:['70%','70%']});
			}else{
				
			}
		});
	}
	
	function readStatus(){
		ajax.remoteCall("${ctxPath}/servlet/esConf?action=getNodesStats",{}, function(result) {
			if(result.state==1){
				    var data = result.data;
				  	let nodesData = [];
	                for (let nodeId in data.nodes) {
	                    nodesData.push({
	                        id: nodeId,
	                        data: JSON.stringify(data.nodes[nodeId], null, 2),  // Display all node data in a formatted way
	                        os: JSON.stringify(data.nodes[nodeId].os, null, 2), // OS data
	                        jvm: JSON.stringify(data.nodes[nodeId].jvm, null, 2), // JVM data
	                        fs: {
	                            totalSpaceGB: (data.nodes[nodeId].fs.total.total_in_bytes / (1024 * 1024 * 1024)).toFixed(2),
	                            usedSpaceGB: (data.nodes[nodeId].fs.total.used_in_bytes / (1024 * 1024 * 1024)).toFixed(2),
	                            freeSpaceGB: (data.nodes[nodeId].fs.total.free_in_bytes / (1024 * 1024 * 1024)).toFixed(2)
	                        },
	                        fs: JSON.stringify(data.nodes[nodeId].fs, null, 2), // File system data
	                        network: JSON.stringify(data.nodes[nodeId].network, null, 2), // Network data
	                        process: JSON.stringify(data.nodes[nodeId].process, null, 2), // Process data
	                        thread_pool: JSON.stringify(data.nodes[nodeId].thread_pool, null, 2) // Thread pool data
	                    });
	                }
	                
	                let template = $('#node-template').html();
	                let renderedHtml = $.templates(template).render({ nodes: nodesData });
					
	                layer.open({content:renderedHtml,area:['80%','80%']});
	                
			}else{
				
			}
		});
	}
	
	function allIndices(){
		ajax.remoteCall("${ctxPath}/servlet/esConf?action=allIndices",{}, function(result) {
			if(result.state==1){
				var str = result.data;
				$('#esIndexAll').val(str);
			}else{
				
			}
		});
	}
	
</script>


