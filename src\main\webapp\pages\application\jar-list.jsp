<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
 <div>
 	<blockquote class="layui-elem-quote">
 		<span>请勿随便上传或删除jar</span>
		<span id="allSize" style="float: right;"></span>
	</blockquote>
	<fieldset class="layui-elem-field layui-field-title">
		<legend>JAR文件列表</legend>
	</fieldset>
	
	<div class="layui-form" style="padding-left:15px">
		<table class="layui-table text-center" lay-even lay-size="sm">
			<thead>
				<tr>
					<th>序号</th>
					<th onclick="orderType('1')" style="text-align:left;">文件名称 (排序)</th>
					<th onclick="orderType('2')">文件大小 (排序)</th>
					<th onclick="orderType('3')">上传时间 (排序)</th>
					<th>下载</th>
				</tr>
			</thead>
			<tbody id="data-area">
		    </tbody>
		</table>
    </div>
</div>
<script id="list-template" type="text/x-jsrender">
				{{for}}
					<tr>
					 <td>{{:#getIndex()+1}}</td>
	                 <td style="text-align:left;">
					   {{:basePath}}/{{:name}}
					 </td>
	                 <td>{{call:size fn='sizeTostr'}}</td>
	                 <td>{{:modifyDate}}</td>
	                 <td>
						<a style="display:none;" href = "${ctxPath}/servlet/application?action=downloadWar&warName={{:name}}" target="_blank">下载</a>
					 </td>
	               </tr>
				{{/for}}
</script>

  <script type="text/javascript" >
	
    $(function(){
		getData();
	});
	
    
    function orderType(orderType){
		getData(orderType);
	}
    
	function getData(orderType){
		if(orderType===undefined){orderType=''}
		ajax.remoteCall("${ctxPath}/servlet/logger?action=libList",{orderType:orderType},function(result){  
			if(result.state==1){
				var jsRenderTpl = $.templates("#list-template");
	          	var html = jsRenderTpl(result.data);
				$("#data-area").html(html); 
				$('#allSize').text("war总大小："+sizeTostr(result.allSize));
			}
		});	
	}

	function sizeTostr(size) {
	    var data = "";
	    if(size=='0'){
	    	data = 0 + "B";
	    }else if (size < 0.1 * 1024) { //如果小于0.1KB转化成B  
	        data = size.toFixed(2) + "B";
	    } else if (size < 0.1 * 1024 * 1024) {//如果小于0.1MB转化成KB  
	        data = (size / 1024).toFixed(2) + "KB";
	    } else if (size < 0.1 * 1024 * 1024 * 1024) { //如果小于0.1GB转化成MB  
	        data = (size / (1024 * 1024)).toFixed(2) + "MB";
	    } else { //其他转化成GB  
	        data = (size / (1024 * 1024 * 1024)).toFixed(2) + "GB";
	    }
	    var sizestr = data + "";
	    var len = sizestr.indexOf("\.");
	    var dec = sizestr.substr(len + 1, 2);
	    if (dec == "00") {//当小数点后为00时 去掉小数部分  
	        return sizestr.substring(0, len) + sizestr.substr(len + 3, 2);
	    }
	    return sizestr;
	} 
	
	</script>
