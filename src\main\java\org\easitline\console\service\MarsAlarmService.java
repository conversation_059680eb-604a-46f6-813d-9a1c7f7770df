package org.easitline.console.service;

import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.impl.RedisImpl;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONObject;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;

public class MarsAlarmService extends IService {
	
	private static String logName = "easitline-alarm";
	
	private static String ALARM_WARN_KEY = "#alarm-warn";
	
	private static String ALARM_FATAL_KEY = "#alarm-fatal";
	
	@Override
	public JSONObject invoke(JSONObject eventObj) throws ServiceException {
		LogEngine.getLogger(logName).info("[MarsAlarmService] << " + eventObj);
		
		String eventCode = eventObj.getString("eventCode");
		if(StringUtils.isBlank(eventCode)){
			LogEngine.getLogger(logName).info("[MarsAlarmService] -> 缺少事件代码[eventCode]参数！" );
			throw new  ServiceException(404,"缺少事件代码[eventCode]参数！");
		}
		String eventName = eventObj.getString("eventName");
		if(StringUtils.isBlank(eventName)){
			LogEngine.getLogger(logName).info("[MarsAlarmService] -> 缺少事件名称[eventName]参数！" );
			throw new  ServiceException(404,"缺少事件名称[eventName]参数！");
		}
		
		String eventLevel = StringUtils.trimToEmpty(eventObj.getString("eventLevel"));
		if(StringUtils.isBlank(eventLevel)){
			LogEngine.getLogger(logName).info("[MarsAlarmService] -> 缺少事件级别[eventLevel]参数！" );
			throw new  ServiceException(404,"缺少事件级别[eventLevel]参数[eventLevel]！");
		}
		
		if("warn".equalsIgnoreCase(eventLevel) || "fatal".equalsIgnoreCase(eventLevel)){
		}else{
			LogEngine.getLogger(logName).info("[MarsAlarmService] -> 事件级别[eventName]取值错误，当前取值为："+eventLevel+",正确取值为：warn 或  fatal ！" );
			throw new  ServiceException(404,"事件级别[eventName]取值错误，当前取值为："+eventLevel+",正确取值为：warn 或  fatal ！");
		}
		
		eventObj.put("eventLevel", eventLevel.toLowerCase());
		
		String moduleName = eventObj.getString("moduleName");
		if(StringUtils.isBlank(moduleName)){
			LogEngine.getLogger(logName).info("[MarsAlarmService] -> 缺少模块名[moduleName]参数！" );
			throw new  ServiceException(404,"缺少模块名[moduleName]参数！");
		}
		
		String appName = eventObj.getString("appName");
		if(StringUtils.isBlank(appName)){
			LogEngine.getLogger(logName).info("[MarsAlarmService] -> 缺少应用名[appName]参数！" );
			throw new  ServiceException(404,"缺少应用名[appName]参数！");
		}	
		
		String eventDesc = eventObj.getString("eventDesc");
		if(StringUtils.isBlank(eventDesc)){
			LogEngine.getLogger(logName).info("[MarsAlarmService] -> 缺少事件描述[eventDesc]参数！" );
			throw new  ServiceException(404,"缺少事件描述[eventDesc]参数！");
		}
		
		String moduleId = eventObj.getString("moduleId");
		if(StringUtils.isBlank(moduleId)){
			eventObj.put("moduleId", eventObj.getString("moduleName")+"@"+ServerContext.getNodeName());
		}
		EasyCalendar cal = EasyCalendar.newInstance();
		eventObj.put("eventTime", cal.getDateTime("-"));
		//只保留1000条告警日志，防止内存溢出
		if("fatal".equalsIgnoreCase(ALARM_FATAL_KEY)){
			LogEngine.getLogger(logName).info("[MarsAlarmService] redis.lpush("+ALARM_FATAL_KEY+") >> " + eventObj);
			lpush(ALARM_FATAL_KEY,eventObj.toJSONString());
		}else{
			LogEngine.getLogger(logName).info("[MarsAlarmService] redis.lpush("+ALARM_WARN_KEY+") >> " + eventObj);
			lpush(ALARM_WARN_KEY,eventObj.toJSONString());
		}
		
		return new JSONObject();
	}
	
//	
//	public static void testService() throws Exception{
//		
//		
//		
//		IService service = ServiceContext.getService("MARS-ALARM-SERVICE");
//		if(service == null) return;
//		
//		JSONObject params = new JSONObject();
//		params.put("moduleName", "testAlarm.war");
//		params.put("appName", "手工告警测试");
//		params.put("eventCode", "TEST-00001");
//		params.put("eventName", "短信网关请求失败");
//		params.put("eventLevel", "warn");
//		params.put("eventDesc", "短信网关接口[http://172.0.0.1:8999/smsgw/interface]请求失败，返回异常代码：404");
//		params.put("exception", "请求异常信息...");
//		service.invoke(params);
//		
//		params = new JSONObject();
//		params.put("moduleName", "testAlarm.war");
//		params.put("appName", "手工告警测试");
//		params.put("eventCode", "TEST-00002");
//		params.put("eventName", "REDIS访问失败");
//		params.put("eventLevel", "fatal");
//		params.put("eventDesc", "REDIS访问失败，请检查redis进程是否能正常访问");
//		params.put("exception", "请求异常信息...");
//		service.invoke(params);
//		
//		params = new JSONObject();
//		params.put("moduleName", "testAlarm.war");
//		params.put("appName", "手工告警测试");
//		params.put("eventCode", "TEST-00003");
//		params.put("eventName", "SQL执行时间超过60秒");
//		params.put("eventLevel", "warn");
//		params.put("eventDesc", "SQL执行时间超过60秒,请检查mars平台监控的SQL监控日志");
//		params.put("exception", "请求异常信息...");
//		service.invoke(params);
//		
//	}
//	
	
	/**
	 * 往链表中添加一条记录
	 * @param key
	 * @param value
	 */
	public static void  lpush(String key,String value){
		RedisImpl redis = (RedisImpl)CacheManager.getMemcache();
		Jedis jedis = redis.getJedis();
		if(jedis !=null){
			try {
				jedis.lpush(key, value);
			} catch (Exception ex) {
				LogEngine.getLogger(logName).error(ex,ex);;
			}finally{
				if(jedis!=null) jedis.close();
			}
		}else{
			JedisCluster cluster = redis.getCluster();
			cluster.lpush(key, value);
		}
	}

	
	
	public static void checkRedis(){
		try {
			remoteRedisOverItem(ALARM_WARN_KEY);
		} catch (Exception ex) {
			LogEngine.getLogger(logName).error(ex,ex);
		}
		try {
			remoteRedisOverItem(ALARM_FATAL_KEY);
		} catch (Exception ex) {
			LogEngine.getLogger(logName).error(ex,ex);
		}
	}
	
	/**
	 * 往链表中末端移除一条监控数据
	 * @param key
	 * @param value
	 */
	private static void  remoteRedisOverItem(String key){
		RedisImpl redis = (RedisImpl)CacheManager.getMemcache();
		Jedis jedis = redis.getJedis();
		if(jedis !=null){
			try {
				for(int i = 0 ; i < 10000 ;i++){
					if(llen(key)<10000) break;
					String value = jedis.rpop(key);
					LogEngine.getLogger(logName).info("[MarsAlarmService] 告警["+key+"]链表长度超过10000，执行移除操作 >> " + value);
				}
			} catch (Exception ex) {
				LogEngine.getLogger(logName).error(ex,ex);
			}finally{
				if(jedis!=null) jedis.close();
			}
		}else{
			JedisCluster cluster = redis.getCluster();
			cluster.rpop(key);
		}
	}
	
	/**
	 * 获取链表的长度
	 * @param key
	 * @return
	 */
	public static long  llen(String key){
		RedisImpl redis = (RedisImpl)CacheManager.getMemcache();
		Jedis jedis = redis.getJedis();
		if(jedis !=null){
			try {
				return jedis.llen(key);
			} catch (Exception ex) {
				LogEngine.getLogger(logName).error(ex,ex);;
			}finally{
				if(jedis!=null) jedis.close();
			}
		}else{
			JedisCluster cluster = redis.getCluster();
			return cluster.llen(key);
		}
		return 0l;
	}
	
}