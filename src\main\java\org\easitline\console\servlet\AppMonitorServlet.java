package org.easitline.console.servlet;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.catalina.Container;
import org.apache.catalina.Context;
import org.apache.catalina.DistributedManager;
import org.apache.catalina.Manager;
import org.apache.catalina.Session;
import org.apache.catalina.manager.Constants;
import org.apache.catalina.manager.DummyProxySession;
import org.apache.catalina.manager.ManagerServlet;
import org.apache.catalina.util.ContextName;
import org.apache.tomcat.util.res.StringManager;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.core.web.render.Render;
import org.easitline.console.deploy.vo.AppContextInfo;
import org.easitline.console.utils.RequestUtil;

@WebServlet(urlPatterns={"/servlet/monitor/app/*","/servlet/appmonitor/*"})
public final class AppMonitorServlet extends ManagerServlet {
	
	private static final long serialVersionUID = 1L;

    private boolean showProxySessions = false;
    
    /**
     * 跳转到tomcatManager页面
     * @param request
     * @param response
     */
    public void list(HttpServletRequest request, HttpServletResponse response) {
    	
    	List<AppContextInfo> contextList = new ArrayList<AppContextInfo>();
    	try {
    	
    		// Apps Row Section
            // Create sorted map of deployed applications by context name.
            Container children[] = host.findChildren();
            String contextNames[] = new String[children.length];
            for (int i = 0; i < children.length; i++)
                contextNames[i] = children[i].getName();

            Arrays.sort(contextNames);
            
            for (String contextName : contextNames) {
                Context ctxt = (Context) host.findChild(contextName);
                
                AppContextInfo contextInfo = new AppContextInfo();
                
                if (ctxt != null) {
                   
                    String contextPath = ctxt.getPath();
                   // ctxt.getDisplayName()
                    
                    if("".equals(contextPath)) {
                    	contextPath = "/";
                    }
                    contextInfo.setContextPath(contextPath);  //上下文路径
                    
                    contextInfo.setState(Boolean.valueOf(ctxt.getState().isAvailable())); //状态
                    
                    Manager manager = ctxt.getManager();
                    Integer sessionCount = new Integer(0);
                    if (manager instanceof DistributedManager) {
                    	sessionCount = Integer.valueOf(
                                ((DistributedManager)manager).getActiveSessionsFull());
                    } else if (manager != null){
                    	sessionCount = Integer.valueOf(manager.getActiveSessions());
                    } else {
                    	sessionCount = Integer.valueOf(0);
                    }
                    contextInfo.setSessionCount(sessionCount);  //session数量
                    
                    //拼接操作应用路径和版本号
                    String displayPath = contextPath;
                    StringBuilder tmp = new StringBuilder();
                    tmp.append("path=");
                    tmp.append(RequestUtil.encode(displayPath));
                    if (ctxt.getWebappVersion().length() > 0) {
                        tmp.append("&version=");
                        tmp.append(RequestUtil.encode(ctxt.getWebappVersion()));
                    }
                    String pathVersion = tmp.toString();
                    
//                   //获取应用信息
//                   AppInfo appInfo = appInfoMap.get(contextPath.replace("/", ""));
//                   
//                   String appId = "";
//                   if(null != appInfo) {
//                    	contextInfo.setAppId(appInfo.getAppId());
//                    	contextInfo.setAppName(appInfo.getAppName());
//                    	contextInfo.setVersion(appInfo.getAppVersion());
//                    	contextInfo.setWarName(appInfo.getWarName());
//                    	contextInfo.setDeployTime(appInfo.getDeployTime());
//                    	
//                    	pathVersion += "&appId=" + appInfo.getAppId();
//                    } 
                    pathVersion = org.easitline.console.base.Constants.getContextPrefix()+pathVersion;
                    String startUrl = RequestUtil.filter(response.encodeURL(org.easitline.console.base.Constants.getContextPath() + "/servlet/monitor/app/start?" + pathVersion));
                    String stopUrl = RequestUtil.filter(response.encodeURL(org.easitline.console.base.Constants.getContextPath() + "/servlet/monitor/app/stop?" + pathVersion));
                    String reloadUrl = RequestUtil.filter(response.encodeURL(org.easitline.console.base.Constants.getContextPath() + "/servlet/monitor/app/reload?" + pathVersion));
                    String undeployUrl = RequestUtil.filter(response.encodeURL(org.easitline.console.base.Constants.getContextPath() + "/servlet/monitor/app/undeploy?" + pathVersion));
                   
                    
                    contextInfo.setAppName(ctxt.getDisplayName());
                    contextInfo.setWarName(ctxt.getBaseName());
                    contextInfo.setStateName(ctxt.getStateName());
                    contextInfo.setSessionTimeout(ctxt.getSessionTimeout());
                    contextInfo.setStartUrl(startUrl);
                	contextInfo.setStopUrl(stopUrl);
                	contextInfo.setReloadUrl(reloadUrl);
                	contextInfo.setUndeployUrl(undeployUrl);
                	//contextInfo.setUpdateUrl(updateAppDsConfigUrl);
               }
               contextList.add(contextInfo);
            }
 
            request.setAttribute("appContextList", contextList);
            EasyResult result = new EasyResult();
            result.setState(1);
            result.setData(contextList);
            responseWriter(result.toJSONString(), response);
		} catch (Exception e) {
			e.printStackTrace();
			Render.renderJson(request, response, e.getMessage());
		}
    }  

    public void responseWriter(String conent,HttpServletResponse response){
		response.setCharacterEncoding("UTF-8");
		PrintWriter writer = null;
		try {
			writer = response.getWriter();
			writer.write(conent);
			writer.flush();
			writer.close();
		} catch (IOException e) {
			e.printStackTrace();
		}finally {
			if(writer!=null){
				writer.close();
			}
		}
	}

    
    
    // --------------------------------------------------------- Public Methods

    /**
     * Process a GET request for the specified resource.
     *
     * @param request The servlet request we are processing
     * @param response The servlet response we are creating
     *
     * @exception IOException if an input/output error occurs
     * @exception ServletException if a servlet-specified error occurs
     */
    @Override
    public void doGet(HttpServletRequest request,HttpServletResponse response) throws IOException, ServletException {
    	//if(true) return ;
        StringManager smClient = StringManager.getManager(Constants.Package, request.getLocales());

        // Identify the request parameters that we need
        // By obtaining the command from the pathInfo, per-command security can
        // be configured in web.xml
        String command = request.getPathInfo();

        String path = request.getParameter("path");
        String appId = request.getParameter("appId");   //应用标识
        ContextName cn = null;
        if (path != null) {
            cn = new ContextName(path, request.getParameter("version"));
        }

        // Prepare our output writer to generate the response message
        response.setContentType("text/html; charset=" + Constants.CHARSET);

        String message = "";
        // Process the requested command
        if (command == null || command.equals("/")) {
            // No command == list
        } else if (command.equals("/list")) {
            // List always displayed - nothing to do here
        } else if (command.equals("/sessions")) {
            try {
                //doSessions(cn, request, response, smClient);
                return;
            } catch (Exception e) {
                log("HTMLManagerServlet.sessions[" + cn + "]", e);
                message = smClient.getString("managerServlet.exception",e.toString());
            }
        } else if (command.equals("/reload")) {
            message = reload(cn, smClient);
        } else if (command.equals("/start")) {
            message = start(cn, smClient);
        } else if (command.equals("/stop")) {
            message = stop(cn, smClient);
        } 
        
        list(request, response);
    }

    /**
     * Process a POST request for the specified resource.
     *
     * @param request The servlet request we are processing
     * @param response The servlet response we are creating
     *
     * @exception IOException if an input/output error occurs
     * @exception ServletException if a servlet-specified error occurs
     */
    @Override
    public void doPost(HttpServletRequest request,HttpServletResponse response) throws IOException, ServletException {

        StringManager smClient = StringManager.getManager(Constants.Package, request.getLocales());

        // Identify the request parameters that we need
        // By obtaining the command from the pathInfo, per-command security can
        // be configured in web.xml
        String command = request.getPathInfo();

        String path = request.getParameter("path");
        String appId = request.getParameter("appId");
        ContextName cn = null;
        if (path != null) {
            cn = new ContextName(path, request.getParameter("version"));
        }
        String deployPath = request.getParameter("deployPath");
        ContextName deployCn = null;
        if (deployPath != null) {
            deployCn = new ContextName(deployPath,
                    request.getParameter("deployVersion"));
        }
        String deployConfig = request.getParameter("deployConfig");
        String deployWar = request.getParameter("deployWar");

        // Prepare our output writer to generate the response message
        response.setContentType("text/html; charset=" + Constants.CHARSET);

        String message = "";

        if (command == null || command.length() == 0) {
            // No command == list
            // List always displayed -> do nothing
        } else if (command.equals("/reload")) {
            message = reload(cn, smClient);
        } else if (command.equals("/expire")) {
            //message = expireSessions(cn, request, smClient);
        } else if (command.equals("/start")) {
            message = start(cn, smClient);
        } else if (command.equals("/stop")) {
            message = stop(cn, smClient);
        } else if (command.equals("/findleaks")) {
            message = findleaks(smClient);
        } 
        
        list(request, response);
    }

    /**
     * Reload the web application at the specified context path.
     *
     * @see ManagerServlet#reload(PrintWriter, ContextName, StringManager)
     *
     * @param cn Name of the application to be restarted
     * @param smClient  StringManager for the client's locale
     * @return message String
     */
    protected String reload(ContextName cn, StringManager smClient) {

        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);

        super.reload(printWriter, cn, smClient);

        return stringWriter.toString();
    }

    /**
     * Undeploy the web application at the specified context path.
     *
     * @see ManagerServlet#undeploy(PrintWriter, ContextName, StringManager)
     *
     * @param cn Name of the application to be undeployed
     * @param smClient  StringManager for the client's locale
     * @return message String
     */
    protected String undeploy(ContextName cn, StringManager smClient) {

        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);

        super.undeploy(printWriter, cn, smClient);

        return stringWriter.toString();
    }

    /**
     * Start the web application at the specified context path.
     *
     * @see ManagerServlet#start(PrintWriter, ContextName, StringManager)
     *
     * @param cn Name of the application to be started
     * @param smClient  StringManager for the client's locale
     * @return message String
     */
    protected String start(ContextName cn, StringManager smClient) {

        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);

        super.start(printWriter, cn, smClient);

        return stringWriter.toString();
    }

    /**
     * Stop the web application at the specified context path.
     *
     * @see ManagerServlet#stop(PrintWriter, ContextName, StringManager)
     *
     * @param cn Name of the application to be stopped
     * @param smClient  StringManager for the client's locale
     * @return message String
     */
    protected String stop(ContextName cn, StringManager smClient) {

        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);

        super.stop(printWriter, cn, smClient);

        return stringWriter.toString();
    }

    /**
     * Find potential memory leaks caused by web application reload.
     *
     * @see ManagerServlet#findleaks(boolean, PrintWriter, StringManager)
     *
     * @param smClient  StringManager for the client's locale
     *
     * @return message String
     */
    protected String findleaks(StringManager smClient) {

        StringBuilder msg = new StringBuilder();

        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);

        super.findleaks(false, printWriter, smClient);

        String writerText = stringWriter.toString();

        if (writerText.length() > 0) {
            if (!writerText.startsWith("FAIL -")) {
                msg.append(smClient.getString(
                        "htmlManagerServlet.findleaksList"));
            }
            msg.append(writerText);
        } else {
            msg.append(smClient.getString("htmlManagerServlet.findleaksNone"));
        }

        return msg.toString();
    }

    /**
     * @see javax.servlet.Servlet#getServletInfo()
     */
    @Override
    public String getServletInfo() {
        return "HTMLManagerServlet, Copyright (c) 1999-2016, The Apache Software Foundation";
    }

    /**
     * @see javax.servlet.GenericServlet#init()
     */
    @Override
    public void init() throws ServletException {
        super.init();

        // Set our properties from the initialization parameters
        String value = null;
        value = getServletConfig().getInitParameter("showProxySessions");
        showProxySessions = Boolean.parseBoolean(value);
    }

    // ------------------------------------------------ Sessions administration

    protected List<Session> getSessionsForName(ContextName cn,
            StringManager smClient) {
        if ((cn == null) || !(cn.getPath().startsWith("/") ||
                cn.getPath().equals(""))) {
            String path = null;
            if (cn != null) {
                path = cn.getPath();
            }
            throw new IllegalArgumentException(smClient.getString("managerServlet.invalidPath",RequestUtil.filter(path)));
        }

        Context ctxt = (Context) host.findChild(cn.getName());
        if (null == ctxt) {
            throw new IllegalArgumentException(smClient.getString("managerServlet.noContext",RequestUtil.filter(cn.getDisplayName())));
        }
        Manager manager = ctxt.getManager();
        List<Session> sessions = new ArrayList<>();
        sessions.addAll(Arrays.asList(manager.findSessions()));
        if (manager instanceof DistributedManager && showProxySessions) {
            // Add dummy proxy sessions
            Set<String> sessionIds =
                ((DistributedManager) manager).getSessionIdsFull();
            // Remove active (primary and backup) session IDs from full list
            for (Session session : sessions) {
                sessionIds.remove(session.getId());
            }
            // Left with just proxy sessions - add them
            for (String sessionId : sessionIds) {
                sessions.add(new DummyProxySession(sessionId));
            }
        }
        return sessions;
    }

    protected Session getSessionForNameAndId(ContextName cn, String id,
            StringManager smClient) {

        List<Session> sessions = getSessionsForName(cn, smClient);
        if (sessions.isEmpty()) return null;
        for(Session session : sessions) {
            if (session.getId().equals(id)) {
                return session;
            }
        }
        return null;
    }

    
    
}
