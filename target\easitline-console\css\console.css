/* easitline平台默认样式 */
/* easitline平台样式基础 */
.ml-0 {
  margin-left: 0!important;
}
.no-margin {
  margin: 0;
}
.no-padding {
  padding: 0;
}
.gray {
  color: #aaaaaa;
}
.ml-10 {
  margin-left: 0.3rem!important;
}
.mb-0 {
  margin-bottom: 0!important;
}
.mb-40 {
  margin-bottom: 2rem!important;
}
.mb-20 {
  margin-bottom: 20px!important;
}
.mb-10 {
  margin-bottom: 0.5rem!important;
}
.m-10 {
  margin-bottom: 0.5rem!important;
}
.mt-10 {
  margin-top: 0.3rem!important;
}
.lh-20 {
  line-height: 1.7rem!important;
}
.lh-30 {
  line-height: 1.5rem!important;
}
.lh-40 {
  line-height: 2rem!important;
}
.no-border {
  border-width: 0!important;
}
.pl-10 {
  padding-left: 0.5rem;
}
.pr-40 {
  padding-right: 2rem!important;
}
.bold {
  font-weight: bold;
}
.red {
  color: red;
}
.text-right {
  text-align: right;
}
.text-left {
  text-align: left!important;
}
.textoverflow {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
}
.hidden {
  display: none;
}
.red-bline {
  border-bottom: 1px solid #f54754;
}
.text2line {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.line-b {
  border-bottom: 1px solid #e4e4e4;
}
.top-line {
  border-top: 1px solid #f2f2f2;
}
.split {
  height: 0.5rem;
  border-bottom: 1px solid #e4e4e4;
  margin-bottom: 0.5rem;
}
.br {
  background: #f3f3f3;
  height: 0.5rem;
}
.no-after:after {
  display: none;
}
.border-base {
  border-radius: 1.4rem;
  border-width: 1px;
  border-style: solid;
  border-color: #419ffc;
}
.br2 {
  background: #f3f3f3;
  height: 0.5rem;
  border-bottom: 1px solid #419ffc;
}
.br3 {
  height: 0.3rem;
}
.block {
  display: block;
}
.nostyle-input {
  border: 0;
  box-shadow: none;
  outline: none;
  background: none;
}
.bg-white {
  background-color: #fff;
}
.bg-white2 {
  background-color: #fafafa!important;
}
.bg-none {
  background: none;
  background-color: transparent!important;
}
/* 定位 */
.content-padded2 {
  margin: 0 0.5rem;
}
.content-padded3 {
  padding: 0.5rem;
}
.content-padded4 {
  padding: 0 0.5rem;
}
.content-padded5 {
  padding: 0.5rem 0 ;
}
.relative {
  position: relative;
}
.abs-center {
  position: absolute;
  top: 50%;
  left: 50%;
  -ms-transform: translate(-50%, -50%);
  /* IE 9 */
  -webkit-transform: translate(-50%, -50%);
  /* Safari and Chrome */
  -o-transform: translate(-50%, -50%);
  /* Opera */
  -moz-transform: translate(-50%, -50%);
  /* Firefox */
  transform: translate(-50%, -50%);
}
.button-sub {
  width: 60%;
  margin: 0 auto;
  font-size: 0.65rem!important;
  height: 1.3rem!important;
  line-height: 1.25rem!important;
}
.fixed-btn ~ .content {
  bottom: 5.2rem;
}
.fixed-btn2 ~ .content {
  bottom: 4.7rem;
}
.fixed-btn2 {
  padding: 0.5rem;
  border-top: 1px solid #e4e4e4;
  position: absolute;
  width: 96%;
  z-index: 3;
  left: 2%;
  height: 5rem;
  bottom: 0;
}
.fixed-btn2 .button-big {
  width: 60%;
  margin: 0 auto;
}
.fixed-btn {
  background-color: #fff;
  padding: 0.5rem;
  border-top: 1px solid #e4e4e4;
  position: absolute;
  width: 96%;
  z-index: 3;
  left: 2%;
  bottom: 2.5rem;
}
.fixed-btn .button-big {
  width: 70%;
  margin: 0 auto;
}
.fixed-btn4 {
  background-color: #fff!important;
  padding: 0.5rem;
  border-top: 1px solid #f2f2f2;
  position: absolute;
  width: 100%;
  z-index: 3;
  left: 0;
  bottom: 0;
  height: 2.5rem;
}
.fixed-btn4 .button-big {
  width: 70%;
  margin: 0 auto;
}
.fixed-btn4 ~ .content {
  bottom: 2.55rem;
}
.fixed-btn .button-round,
.fixed-btn2 .button-round,
.fixed-btn3 .button-round,
.fixed-btn4 .button-round {
  border-radius: 0.25rem;
}
.fixed-btn .button.button-big,
.fixed-btn2 .button.button-big,
.fixed-btn3 .button.button-big,
.fixed-btn4 .button.button-big {
  font-size: 0.65rem;
  height: 1.3rem;
  line-height: 1.25rem;
}
.white-bar {
  background-color: #fff;
  border-bottom: 1px solid #f2f2f2;
}
/* 字体颜色 */
.color-black {
  color: #419ffc;
}
input[type='radio'],
input[type='checkbox'] {
  vertical-align: middle;
}
.button.button-big {
  font-size: 0.75rem;
  height: 1.7rem;
  line-height: 1.6rem;
}
/* 系统框架修改 */
.label-switch-mini.label-switch {
  width: 2.4rem;
  border-radius: 0.2rem;
  box-sizing: border-box;
  height: 1rem;
}
.label-switch-mini.label-switch .checkbox {
  width: 2.1rem;
  border-radius: 0.2rem;
  box-sizing: border-box;
  height: 1rem;
}
.label-switch-mini.label-switch .checkbox:after {
  height: 0.8rem;
  width: 0.8rem;
  border-radius: 0.2rem;
}
.label-switch-mini.label-switch .checkbox:before {
  width: 1.9rem;
  border-radius: 0.2rem;
  height: 0.8rem;
}
.label-switch-mini input[type="checkbox"]:checked + .checkbox {
  background: #419ffc;
}
.no-border-tab.buttons-tab .button.active {
  border-color: transparent;
}
.bar .button-nav.pull-left {
  margin-left: -0.05rem;
}
.bar .button-nav.pull-left .icon-left-nav {
  margin-right: -0.15rem;
}
.bar .button-nav.pull-right {
  margin-right: -0.05rem;
}
.bar .button-nav.pull-right .icon-right-nav {
  margin-left: -0.15rem;
}
.bar-tab .tab-item .mini-badge {
  position: absolute;
  top: .2rem;
  left: 50%;
  z-index: 100;
  height: .4rem;
  min-width: .4rem;
  padding: 0 .2rem;
  font-size: .6rem;
  line-height: .8rem;
  color: white;
  vertical-align: top;
  background: red;
  border-radius: .5rem;
  margin-left: .2rem;
}
.bar-header-secondary.bar {
  height: 2rem;
}
.bar-header-secondary ~ .content {
  top: 4.2rem;
}
.bar-header-secondary > .top-buttons-tab {
  margin: 0 -0.5rem;
}
.top-buttons-tab {
  -ms-flex-item-align: center;
  -webkit-align-self: center;
  align-self: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-lines: single;
  -moz-box-lines: single;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: none;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}
.top-buttons-tab.tabstyle2 .t-tab-btn:after {
  content: "";
  display: none;
  position: absolute;
  right: 15%;
  left: 15%;
  bottom: 0rem;
  width: auto;
  height: 2px;
  top: inherit;
  background-color: #419ffc;
}
.top-buttons-tab.tabstyle2 .t-tab-btn.active:after {
  display: block;
}
.top-buttons-tab.tab-mini {
  border-bottom: 1px dashed #eee ;
}
.top-buttons-tab.tab-mini .t-tab-btn {
  height: 1.75rem;
  line-height: 1.75rem;
  font-size: 0.6rem;
}
.top-buttons-tab .t-tab-btn {
  text-decoration: none;
  text-align: center;
  display: block;
  line-height: 1.25rem;
  box-sizing: border-box;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  background: none;
  padding: 0 0.5rem;
  margin: 0;
  white-space: nowrap;
  position: relative;
  text-overflow: ellipsis;
  font-family: inherit;
  cursor: pointer;
  color: #5f646e;
  font-size: 0.65rem;
  width: 100%;
  height: 2rem;
  line-height: 2rem;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  border: 0;
}
.top-buttons-tab .t-tab-btn:after {
  content: "";
  position: absolute;
  right: 0;
  top: 0.3rem;
  height: 1.4rem;
  width: 1px;
  background-color: #ddd;
}
.top-buttons-tab .t-tab-btn.active {
  color: #419ffc;
}
.top-buttons-tab .t-tab-btn:nth-last-child(1):after {
  display: none;
}
.top-buttons-tab .t-tab-btn .curLv {
  display: block;
}
.top-buttons-tab .t-tab-btn .curLv:after {
  position: absolute;
  font-size: 0.5rem;
  top: 0;
  right: 1.2rem;
  font-family: "iconfont-sm" !important;
  font-style: normal;
  display: inline-block;
  vertical-align: middle;
  background-size: 100% auto;
  background-position: center;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  content: "\e611";
}
.top-buttons-tab .t-tab-btn .navLvs {
  list-style: none;
  position: absolute;
  display: none;
  top: 2rem;
  left: 0;
  margin: 0;
  padding: 0;
  width: 100%;
  box-shadow: 0px 2px 7px 0px rgba(170, 170, 170, 0.35);
}
.top-buttons-tab .t-tab-btn .navLvs li {
  background-color: #fff;
  line-height: 2rem;
  text-align: center;
  color: #333;
}
.top-buttons-tab .t-tab-btn .navLvs li a {
  display: block;
  color: #333;
}
.top-buttons-tab .t-tab-btn .navLvs li.active {
  color: #fff;
  background-color: #419ffc;
}
.top-buttons-tab .t-tab-btn .navLvs li.active a {
  color: #fff;
}
.top-buttons-tab .t-tab-btn.open .curLv:after {
  content: "\e612";
}
.top-buttons-tab .t-tab-btn.open .navLvs {
  display: block;
}
.red-checkbox {
  border-color: #419ffc;
  color: #419ffc;
}
label.label-checkbox input[type="checkbox"]:checked + .item-media i.red-checkbox,
label.label-checkbox input[type="radio"]:checked + .item-media i.red-checkbox {
  border: 1px solid #419ffc;
  color: #419ffc !important;
}
/* 幻灯�? */
.swiper-container-horizontal > .swiper-pagination {
  bottom: 5px;
  left: 0;
  width: 100%;
}
.swiper-container {
  padding-bottom: 0;
}
.swiper-pagination-bullet-active {
  opacity: 1;
  background: #419ffc;
}
.swiper-pagination {
  text-align: right;
}
/* 折叠面板 */
.toggle-panel {
  margin-bottom: 0.5rem;
}
.toggle-panel .toggle-panel-header {
  position: relative;
  width: 100%;
  line-height: 1.9rem;
  height: 1.9rem;
  background-color: #fff;
  padding: 0 0.5rem;
}
.toggle-panel .toggle-panel-header h1 {
  line-height: 1.9rem;
  font-weight: 500;
  padding: 0;
  margin: 0;
  font-size: 0.6rem;
  color: #7e7e7e;
}
.toggle-panel .toggle-panel-header .toggle-panel-closebtn {
  position: absolute;
  display: block;
  width: 1.5rem;
  text-align: center;
  top: 0;
  right: 0.5rem;
  font-family: "iconfont-sm" !important;
  font-style: normal;
  display: inline-block;
  vertical-align: middle;
  background-size: 100% auto;
  background-position: center;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  -moz-osx-font-smoothing: grayscale;
}
.toggle-panel .toggle-panel-header .toggle-panel-closebtn:before {
  content: "\e611";
}
.toggle-panel .toggle-panel-content {
  padding: 0.5rem;
}
.toggle-panel.close .toggle-panel-closebtn:before {
  content: "\e612";
}
.toggle-panel.close .toggle-panel-content {
  display: none;
}
.list-block.red-list ul:after {
  opacity: 0;
}
/* 基础布局样式 */
html,
body {
  background-color: #efefef;
  font-size: 13px;
}
.sui-container {
  background-color: #efefef;
}
.nav-top-form {
  position: relative;
}
.nav-top-form .search-input {
  min-width: 180px;
}
.nav-top-form .search-btn {
  position: absolute;
  right: 0;
  top: 0;
}
@media (min-width: 1440px) {
  .nav-top-form .search-input {
    min-width: 580px;
  }
}
@media (min-width: 1240px) {
  .nav-top-form .search-input {
    min-width: 380px;
  }
}
.sui-layout {
  height: 100%;
}
.sui-layout .sidebar {
  width: 196px;
  height: 100%;
  position: relative;
  overflow: hidden;
  /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#40a9ff+0,4761e6+100 */
  background: #40a9ff;
  /* Old browsers */
  background: -moz-linear-gradient(top, #40a9ff 0%, #4761e6 100%);
  /* FF3.6-15 */
  background: -webkit-linear-gradient(top, #40a9ff 0%, #4761e6 100%);
  /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, #40a9ff 0%, #4761e6 100%);
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#40a9ff', endColorstr='#4761e6', GradientType=0);
  /* IE6-9 */
}
.sui-layout .content {
  margin-left: 196px;
  height: 100%;
  overflow: auto;
  position: relative;
  /*  .sui-navbar{
	   			    position: fixed;
	   			    left: 196px;
	   			    top: 0;
	   			    width: 100%;
	   			    right: 0;
	   			    z-index: 555;
	   
	   }
	   .main-content{
	   	padding-top: 66px;
	   } */
}
.sui-navbar {
  height: 66px;
  border-bottom: 1px solid #ddd;
  margin-bottom: 5px;
}
.sui-navbar .navbar-inner {
  max-height: 66px;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
  border-radius: 0;
}
.sui-navbar .sui-nav.easitline-nav > li.active > a {
  background-color: #fff;
  color: #419ffc;
  border-bottom: 3px solid #419ffc;
}
.sui-navbar .sui-nav.easitline-nav > li > a {
  padding: 24px 15px 21px;
  font-size: 14px;
  border-bottom: 3px solid transparent;
}
.sui-navbar .mar-top {
  margin-top: 13px;
}
.side-nav-userhead {
  width: 48px;
  height: 48px;
  overflow: hidden;
  border: 1px solid #fff;
  background-color: #fff;
  display: block;
  margin: 0 auto 2px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
}
.side-nav-userhead img {
  display: block;
  width: 100%;
}
.side-nav-username {
  font-size: 14px;
  text-align: center;
  font-weight: normal;
  color: #fff;
  padding: 4px 0;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
}
.side-nav-btns {
  text-align: center;
  color: #fff;
  padding: 6px 0;
  font-size: 14px;
}
.side-nav-btns a {
  margin: 0 5px;
  padding: 0 5px;
  color: #fff;
}
.side-nav-navs {
  margin-bottom: 100px;
}
.side-copy {
  text-align: center;
  color: #fff;
  padding-top: 10px;
  padding-bottom: 10px;
  line-height: 20px;
}
/* 导航�? */
.nav-group {
  color: #fff;
  border-top: 1px solid rgba(0, 55, 110, 0.2);
  box-shadow: inset -1px 0px 0px -1px rgba(223, 238, 255, 0.35);
}
.nav-group .nav-group-title {
  border-top: 1px solid rgba(223, 238, 255, 0.35);
  padding: 8px 20px;
  position: relative;
  font-size: 16px;
  line-height: 24px;
}
.nav-group .nav-group-title:after {
  content: "";
  height: 1px;
  width: 55px;
  border-bottom: 1px dashed #fff;
  position: absolute;
  top: 50%;
  right: 20px;
  margin-top: -1px;
}
.nav-group .nav-group-title h3 {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  line-height: 24px;
  padding: 0;
  margin: 0;
  font-weight: normal;
  font-size: 15px;
}
.nav-group .nav-group-title h3 span {
  padding-top: 2px;
}
.nav-group .nav-group-title .sui-icon {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  font-size: 14px;
  margin-right: 5px;
  vertical-align: middle;
  margin-top: -2px;
}
.nav-group ul > li {
  font-size: 13px;
  padding-left:30px;
  line-height: 12px;
}
.nav-group ul > li.active a {
  background: rgba(27, 85, 150, 0.3) url("../images/side-nav-active.png") right center no-repeat;
  box-shadow: inset 0px -1px 1px 0px rgba(223, 238, 255, 0.35);
}
.nav-group ul > li > a {
  padding: 10px 20px;
  color: #fff;
  display: block;
  text-decoration: none;
}
.sui-container-fluid{margin-bottom: 30px;}
.nav-group ul > li > a:hover {
  background: rgba(27, 85, 150, 0.3) url("../images/side-nav-active.png") right center no-repeat;
  box-shadow: inset 0px -1px 1px 0px rgba(223, 238, 255, 0.35);
}
.sui-msg.msg-large{font-size: 13px;}
/* 面包�? */
.easitline-breadcrumb {
  color: #7e7e7e;
  font-size: 13px;
  padding: 10px 20px;
}
/* 主内容区�? */
.main-content {
  margin: 0px 0px 0px 15px;
}
.green-dot {
  padding-left: 38px;
  background: url("../images/green-dot.png") 18px center no-repeat;
  color: #419ffc;
}
.text-underline,
.underline {
  text-decoration: underline;
}
/* 首页图表显示 */
.data-box {
  padding: 20px 0;
}
.data-box .data-show {
  min-height: 140px;
  float: left;
  width: 16.5%;
  text-align: center;
  border-right: 1px solid #ddd;
}
.data-box .data-show:nth-last-child(1) {
  border-right: 0;
}
.data-box .data-show .data-img {
  position: relative;
  width: 100px;
  height: 100px;
  display: block;
  margin: 0 auto;
  margin-bottom: 10px;
}
.data-box .data-show .data-img img {
  width: 100%;
  display: block;
}
.data-box .data-show .data-text {
  color: #333;
  line-height: 1.2;
  font-size: 14px;
}
.progress-box {
  padding: 10px 20px;
}
.sui-progress.progress-mini {
  height: 12px;
  line-height: 12px;
  font-size: 12px;
}
.sui-progress.progress-mini .sui-progress .bar {
  padding: 0 14px;
}
.progress-text {
  margin-bottom: 2px;
}
.sui-progress.progress-mini .bar-text {
  padding: 0;
}
.bar-top {
  opacity: 0.5;
  border-radius: 0 4px 4px 0;
}
#date-rows-panel .easitline-panel {
  min-height: 325px;
}
.progress-overload .bar,
.sui-progress .bar-overload {
  background-color: #5f3f9f;
}
.first-td {
  padding-left: 38px;
}
.text-center {
  text-align: center!important;
}
.control-label.control-label-larger {
  width: 196px!important;
}
.content-padded {
  padding: 20px;
}
.content-padded2 {
  padding: 20px 38px;
}
.pageFrame {
  border: none;
  outline: none;
  width: 100%;
  height: 100%;
}
.pageFrame {
  border: none;
  outline: none;
  width: 100%;
  height: 100%;
}
#main-content {
  position: absolute;
  top: 68px;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}
/* 面板组件 */
.easitline-panel {
  display: block;
  margin-bottom: 25px;
  margin-top: 0;
  padding: 0;
  border: 1px solid #e2e2e2;
  border-radius: 2px;
  background-color: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(239, 239, 239, 0.5);
  min-height: 300px;
}
.easitline-panel .easitline-panel-title {
  position: relative;
  background-color: #ffffff;
  border-color: #e7eaec;
  -webkit-border-image: none;
  -o-border-image: none;
  border-image: none;
  border-style: solid solid none;
  border-width: 1px 0px 0;
  color: inherit;
  margin-bottom: 0;
  padding: 17px 0 17px;
  min-height: 50px;
  box-sizing: border-box;
  border-bottom: 1px solid #e2e2e2;
}
.easitline-panel .easitline-panel-heading {
  background-color: #f3f6fb;
  border-bottom: none;
}
.easitline-panel .easitline-panel-heading h3 {
  font-weight: 200;
  font-size: 24px;
}
.easitline-panel .easitline-panel-title h5 {
  display: inline-block;
  font-size: 16px;
  margin: 0;
  padding-left: 35px;
  font-weight: 300;
  background: url("../images/panel-title.png") 25px center no-repeat;
  text-overflow: ellipsis;
  float: left;
}
.easitline-panel .easitline-panel-title .label {
  float: left;
  margin-left: 4px;
}
.easitline-panel .easitline-panel-tools {
  margin-top: 0;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  display: inline-block;
  *display: inline;
  *zoom: 1;
  padding: 10px;
  border-left: 1px solid #e2e2e2;
}
.easitline-panel .easitline-panel-tools a.easitline-panel-tools-icon {
  cursor: pointer;
  margin-left: 5px;
  font-size: 22px;
  color: #419ffc;
  text-decoration: none;
}
.easitline-panel .easitline-panel-tools-icon {
  line-height: 30px;
  font-size: 22px;
  color: #419ffc;
}
.sui-table.table-zebra2 tbody > tr:nth-child(even) > td,
.sui-table.table-zebra2 tbody > tr:nth-child(even) > th {
  background-color: #f8f8f8;
}
.sui-table {
  margin-bottom: 0;
}
.sui-table .no-top-border td {
  border-top: 0;
}
.sui-table th {
  background-color: #f8f8f8;
}
.sui-table th,
.sui-table td {
  padding: 9px 8px;
}
.sui-btn.btn-mini {
  line-height: 14px;
}
.table-list {
  background: url("../images/table-list.png") left center no-repeat;
  color: #419ffc;
}
.table-list span {
  padding-left: 38px;
}
