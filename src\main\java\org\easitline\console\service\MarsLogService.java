package org.easitline.console.service;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.console.log.LogServer;
import com.alibaba.fastjson.JSONObject;

/**
 * {
	"ID":"记录唯一ID",  -- 这个可以不用
	"CREATE_ACC":"操作人账号",
	"CREATE_NAME":"操作人名称",
	"MARS_NODE_NAME":"mars名称，对应平台参数配置里的节点名称",  -- 这个不用
	"CREATE_TIME":"操作时间，格式到毫秒，如 2022-03-28 17:27:40.351",  --这个不用
	"STACK_TRACE":"调用堆栈信息",
	"TIMESTAMP":"长整型，时间戳",  --这个不用
	"CONTENT":"操作内容",
	"JSON":"请求体数据，json",
	"MODULE":"模块，如yc-mediagw",
	"BUSINESS":"业务场景，对应模块的分组，如在线、话务等，用英文",
	"DIRECTION":"数据方向，相对于服务器，属于出还是进，<<  , >> "
	}
 * <AUTHOR>
 *
 */
public class MarsLogService extends IService {
	
	@Override
	public JSONObject invoke(JSONObject logObj) throws ServiceException {
		if(logObj == null) return new JSONObject();
		//如果没有开启日志采集，则不处理。
		if(!"1".equals(ServerContext.getProperties("mqCollectLog", "0"))) return new JSONObject(); 
		EasyCalendar cal = EasyCalendar.newInstance();
		logObj.put("ID", RandomKit.uniqueStr());
		logObj.put("CREATE_TIME", cal.getDateTime("-")+"."+(System.currentTimeMillis()%1000));
		logObj.put("MARS_NODE_NAME", ServerContext.getServerName());
		logObj.put("TIMESTAMP", System.currentTimeMillis());
		logObj.put("STACK_TRACE", getStackTrace());
		LogServer.addLog(logObj.toJSONString());
		return new JSONObject();
	}
	
	public static String getStackTrace(){
		StackTraceElement[] stackTrace  = Thread.currentThread().getStackTrace();
		if(stackTrace == null) return "";
		StackTraceElement e = null;
		int length = stackTrace.length;
		boolean impl = false;
		for(int i=0;i<length;i++){
			e = stackTrace[i];
			String className = e.getClassName();
			if(className.startsWith("org.easitline") || className.indexOf("LogUtil")!=-1){
				impl = true;
				continue;
			}
			if(impl){
				return e.toString();
			}
		}
		return e.toString();
	}
	
	public static void main(String[] args) throws ServiceException {
		System.out.println(getStackTrace());
		MarsLogService service = new MarsLogService();
		JSONObject json = service.invoke(new JSONObject());
		System.out.println(json);
	}
}