package org.easitline.console.dao;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.Globals;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.apache.commons.lang3.StringUtils;
import org.easitline.console.base.AppDaoContext;
import org.easitline.console.service.ConfigCryptorService;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

@WebObject(name="app")
public class ApplicationDao extends AppDaoContext {
	
	@WebControl(name="list",type= Types.LIST)
	public JSONObject list(){
		return queryForList("SELECT t1.*, COALESCE(b1.conf_count, 0) AS conf_count FROM EASI_APP_INFO t1 LEFT JOIN ( SELECT APP_ID, COUNT(1) AS conf_count FROM EASI_APP_CONF GROUP BY APP_ID ) b1 ON t1.APP_ID = b1.APP_ID ORDER BY t1.DEPLOY_TIME DESC", null,new MapRowMapperImpl());
	}
	
	@WebControl(name="stroeList",type= Types.LIST)
	public JSONObject stroeList(){
		return queryForList("select * from EASI_APP_STROE order by LAST_MODIFIED desc", null,new MapRowMapperImpl());
	}
	
	@WebControl(name="hisList",type= Types.LIST)
	public JSONObject hisList(){
		EasyQuery query=this.getQuery();
		String appId=param.getString("appId");
		if(StringUtils.isBlank(appId)) {
			query.setMaxRow(100);
			this.setQuery(query);
			return queryForList("select * from EASI_APP_INFO_DEPLOY_HIS order by DEPLOY_TIME desc limit 100", new Object[]{},new MapRowMapperImpl());
		}else {
			query.setMaxRow(80);
			this.setQuery(query);
			return queryForList("select * from EASI_APP_INFO_DEPLOY_HIS where APP_ID = ? order by DEPLOY_TIME desc limit 80", new Object[]{appId},new MapRowMapperImpl());
		}
	}
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		String appId = param.getString("appId");
		return queryForRecord("select * from EASI_APP_INFO where APP_ID = ?", new Object[]{appId},new MapRowMapperImpl());
	}
	
	@WebControl(name="appDsList",type=Types.LIST)
	public JSONObject appDsList(){
		String appId = param.getString("appId");
		return queryForList("select * from EASI_APP_DS where APP_ID = ?", new Object[]{appId},new MapRowMapperImpl());
	}
	
	@WebControl(name="appConfigs",type=Types.LIST)
	public JSONObject appConfigs(){
		String appId = param.getString("appId");
		JSONObject result=null;
		try {
			result=getJsonResult(this.getQuery().queryForList("select * from EASI_APP_CONF where APP_ID = ? order by ORDER_INDEX", new Object[]{appId},new JSONMapperImpl()));
		} catch (Exception e) {
			this.error(null, e);
			result= queryForList("select * from EASI_APP_CONF where APP_ID = ?", new Object[]{appId},new MapRowMapperImpl());
		}
		result.put("key",ServerContext.getProperties("SECURITY_KEY", ""));
		
		if(!checkState()) {
			return emptyPage();
		}
		this.decryptConfig(result);
		return result;
	}
	
	@WebControl(name="appConfList",type=Types.LIST)
	public JSONObject appConfList(){
		JSONObject result= new JSONObject();
		try {
			EasySQL sql = new EasySQL();
			sql.append("select t1.*,t2.APP_NAME from EASI_APP_CONF t1 INNER JOIN EASI_APP_INFO t2 on t1.app_id = t2.app_id where 1=1");
			sql.append(param.getString("appId"),"and t1.APP_ID = ?");
			sql.appendLike(param.getString("appName"),"and t2.APP_NAME like ?");
			sql.appendLike(param.getString("itemKey"),"and t1.ITEM_KEY like ?");
			sql.appendLike(param.getString("itemName"),"and t1.ITEM_NAME like ?");
			sql.appendLike(param.getString("itemDesc"),"and t1.ITEM_DESC like ?");
			sql.appendLike(param.getString("itemValue"),"and t1.ITEM_VALUE like ?");
			sql.append("order by t1.UPDATE_TIME desc,t1.BASE_FLAG,t1.APP_ID,t1.ORDER_INDEX");
			result = queryForPageList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			this.decryptConfig(result);
		} catch (Exception e) {
			this.error(e.getMessage(), e);
		}
		return result;
	}
	
	private void decryptConfig(JSONObject result){
		if(result!=null) {
			JSONArray array  =  result.getJSONArray("data");
			for(int i=0;i<array.size();i++) {
				JSONObject row = array.getJSONObject(i);
				String value = row.getString("ITEM_VALUE");
				String newValue = ConfigCryptorService.decryptString(value,"AV");
				row.put("ITEM_VALUE",newValue);
			}
		}
	}
	
	@WebControl(name="histConfList",type=Types.TEMPLATE)
	public JSONObject histConfList(){
		String appId = param.getString("appId");
		String appVersioin = param.getString("appVersioin");
		JSONObject histConfList = null;
		try {
			histConfList = getJsonResult(this.getQuery().queryForList("select * from  FROM EASI_APP_CONF_HIS WHERE APP_ID = ? and APP_VERSIOIN = ?", new Object[]{appId,appVersioin},new JSONMapperImpl()));
			this.decryptConfig(histConfList);
		} catch (Exception e) {
			this.error(null, e);
		}
		return histConfList;
	}
	
	@WebControl(name = "warDeployApp",type = Types.DICT)
	public JSONObject warDeployApp() {
		String path = Globals.SERVER_DIR+File.separator +"war";
		File file = new File(path);
		if(!file.exists()){
			file.mkdirs();
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		
		List<String> list = new ArrayList<String>();
		File[] files  = file.listFiles();
		for(File warFile:files) {
			String fileName  = warFile.getName();
			if(fileName.lastIndexOf(".war")>-1) {
				list.add(warFile.getAbsolutePath());
				list.add(fileName+"_"+sdf.format(new Date(warFile.lastModified())));
			}
		}
		return getDictByArray(list.toArray(new String[list.size()]));
	}
	
}
