package org.easitline.console.servlet;

import java.io.File;
import java.io.IOException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketAddress;
import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.console.base.ConsoleBaseServlet;
import org.easitline.console.utils.ApusicUtils;
import org.easitline.console.utils.ConsoleUtils;

import com.alibaba.fastjson.JSONObject;

@WebServlet("/servlet/monitor/server/*")
@MultipartConfig
public final class ServerMonitorServlet extends  ConsoleBaseServlet{
	private static final long serialVersionUID = 1L;

	
	public void actionForPage() {
		if("apusic".equals(ConsoleUtils.getServerType())) {
			forward("/pages/monitor/apusic-monitor.jsp");
		}else {
			forward("/pages/monitor/app-monitor.jsp");
		}
	}
	
	public void actionForApusicApp() {
		renderJson(EasyResult.ok(ApusicUtils.getList()));
	}
	
	public void actionForApusic() {
		String operator = getPara("operator");
		String appName = getPara("appName");
		File warFile = new File(ApusicUtils.deployDir()+File.separator+appName);
		if(!warFile.exists()) {
			renderJson(EasyResult.fail("文件不存在>"+warFile.getAbsolutePath()));
			return;
		}
		if("start".equals(operator)||"reload".equals(operator)) {
			warFile.setLastModified(System.currentTimeMillis());
			this.addOperateLog(operator+">"+appName,null);
		}else if("stop".equals(operator)) {
			File deployFile = new File(ApusicUtils.deployDir()+File.separator+appName+"_deployed");
			if(deployFile.exists()) {
				if(deployFile.delete()) {
					String dirPath = ApusicUtils.deployDir()+File.separator+appName;
					dirPath = dirPath.replace("autodeploy", "applications");
					dirPath = dirPath.replace(".war", "");
					File deployDir = new File(dirPath);
					ConsoleUtils.deleteDirectory(deployDir);
					this.addOperateLog("停止应用>"+appName,null);
				}else {
					renderJson(EasyResult.fail("手动停止失败>"+warFile.getAbsolutePath()));
				}
			}else {
				renderJson(EasyResult.fail("停止失败>"+warFile.getAbsolutePath()));
				return;
			}
		}
		renderJson(EasyResult.ok());
	}
	
	public EasyResult actionForList(){
		try {
			List<JSONObject> list=this.getConsoleQuery().queryForList("select * from EASI_SERVER_MONITOR", null,new JSONMapperImpl());
			return EasyResult.ok(list);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return EasyResult.fail();
	}
	public EasyResult actionForAdd(){
		EasyRecord record=new EasyRecord();
		record.set("MONITOR_ID", RandomKit.randomStr());
		record.setColumns(getJSONObject());
		try {
			this.getConsoleQuery().save(record);
		} catch (SQLException e) {
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdate(){
		EasyRecord record=new EasyRecord();
		record.setColumns(getJSONObject());
		try {
			this.getConsoleQuery().update(record);
		} catch (SQLException e) {
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	/** 当返回值是true时，说明host是可用的，false则不可。*/  
    public static boolean ping(String ipAddress) throws Exception {  
        int timeOut = 3000; // 超时应该在3钞以上  
        boolean status = InetAddress.getByName(ipAddress).isReachable(timeOut);  
        return status;  
    }  
    private static boolean validePort(String location, int port) {  
        Socket s = new Socket();  
        try {  
            SocketAddress add = new InetSocketAddress(location, port);  
            s.connect(add, 2000);  
            return true;  
        } catch (IOException e) {  
            return false;  
        }finally{  
            try {  
                s.close();  
            } catch (IOException ex) {  
            }  
        }  
    } 
	
    public static void main(String[] args) throws Exception {
		System.out.println(">>>>:"+ping("*************"));
		System.out.println(">>>>:"+validePort("*************",9019));
	}
	@Override
	protected String getResId() {
		return null;
	}
	
	
	
    
    
}
