package org.easitline.console.servlet;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.cache.impl.RedisImpl;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.kit.RandomKit;
import org.apache.commons.lang3.StringUtils;
import org.easitline.console.base.ConsoleBaseServlet;
import org.easitline.console.service.ConfigCryptorService;
import org.easitline.console.utils.AesUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;

@WebServlet("/servlet/cacheMgr/*")
public class CacheMgrServlet extends ConsoleBaseServlet {

	private static final long serialVersionUID = 7201718476039020390L;

	private Object getCacheObject(String key) {
		//兼容原来的
		EasyCache cache = CacheManager.getMemcache();
		try {
			Object obj = cache.get(key);
			if (obj != null){
				return "(Object)"+JSON.toJSONString(obj);
			}
		} catch (Exception ex) {
			this.error(ex.getMessage(),ex);
		}
		//如果获取的内容为空，从其他对象上进行获取
		RedisImpl redis = (RedisImpl)cache;
		Jedis jedis = redis.getJedis();
		if (jedis!=null) {
			try {
				
				try {
					String val = jedis.get(key);  
					this.info("jedis.get("+key+")->"+val,null);
					if (StringUtils.isNotBlank(val)) {
						return "(String)"+val;
					}
				} catch (Exception ex) {
					this.error(ex.getMessage(),ex);
				}
				
				try {
					Map<String,String> map = jedis.hgetAll(key);  
					this.info("jedis.hgetAll("+key+")->"+JSON.toJSONString(map),null);
					if (map != null & map.size()>0) {
						return "(Map)"+JSON.toJSONString(map);
					}
				} catch (Exception ex) {
					this.error(ex.getMessage(),ex);
				}
				
				try {
					Set<String> set  = jedis.smembers(key);
					this.info("jedis.smembers("+key+")->"+JSON.toJSONString(set),null);
					if (set != null & set.size()>0) {
						return "(Set)"+JSON.toJSONString(set);
					}
				} catch (Exception ex) {
					this.error(ex.getMessage(),ex);
				}
				
				List<String> list  = jedis.lrange(key, 0, 1000);
				this.info("jedis.lrange("+key+")->"+JSON.toJSONString(list),null);
				if (list != null & list.size()>0) {
					return "(List)"+JSON.toJSONString(list);
				}
				
			} catch (Exception ex) {
				this.error(ex.getMessage(),ex);
			} finally {
				jedis.close();
			}
		}else{
			JedisCluster cluster = redis.getCluster();
			try {
				try {
					Map<String,String> map = this.hgetAllByCluster(cluster, key);  
					this.info("cluster.hgetAll("+key+")->"+JSON.toJSONString(map),null);
					if (map != null & map.size()>0) {
						return "(Map)"+JSON.toJSONString(map);
					}
				} catch (Exception ex) {
					this.error(ex.getMessage(),ex);
				}
				
				try {
					Set<String> set  = cluster.smembers(key);
					this.info("cluster.smembers("+key+")->"+JSON.toJSONString(set),null);
					if (set != null & set.size()>0) {
						return "(Set)"+JSON.toJSONString(set);
					}
				} catch (Exception ex) {
					this.error(ex.getMessage(),ex);
				}
				
				
				List<String> list  = cluster.lrange(key, 0, -1);
				this.info("jedis.lrange("+key+")->"+JSON.toJSONString(list),null);
				if (list != null & list.size()>0) {
					return "(List)"+JSON.toJSONString(list);
				}
				
			} catch (Exception ex) {
				this.error(ex.getMessage(),ex);
			}
		}
		return null;
	}
	
	
	public Map<String, String> hgetAllByCluster(JedisCluster cluster,String key) {
			Set<String> hkeys = cluster.hkeys(key);
			Map<String, String> result = new HashMap<>();
			for (String k : hkeys) {
				result.put(k, cluster.hget(key, k));
			}
			return result;
	}
	
	
	public EasyResult actionForExcute(){
		String type = getPara("type");
		JSONObject jsonObject = getJSONObject();
		EasyCache cache = CacheManager.getMemcache();
		String cacheKey=jsonObject.getString("cacheKey");
		String cacheValue=jsonObject.getString("cacheValue");
		int cacheTime=jsonObject.getIntValue("cacheTime");
		if(StringUtils.isBlank(cacheKey)){
			return EasyResult.fail("key not is null.");
		}
		if("search".equals(type)){
			Object object = null;
			if("redis".equals(ServerContext.getCacheType())){
				object = getCacheObject(cacheKey);
			}else {
				object = cache.get(cacheKey);
			}
			this.addOperateLog("执行Redis查询>"+cacheKey,null);
			if(object==null){
				return EasyResult.fail("没用找到>"+cacheKey);
			}
			return EasyResult.ok(object);
		}else if("add".equals(type)){
			cache.put(cacheKey, cacheValue,cacheTime);
			Object object = cache.get(cacheKey);
			this.addOperateLog("执行Redis新增>"+cacheKey,null);
			if(object==null){
				return EasyResult.fail("新增失败");
			}
		}else if("del".equals(type)){
			cache.delete(cacheKey);
			Object object=cache.get(cacheKey);
			if(object!=null){
				return EasyResult.fail("删除失败");
			}
			this.addOperateLog("执行Redis删除>"+cacheKey,null);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForGetConf2(){
		try {
			String v1 = getConsoleQuery().queryForString("select CONF_VALUE from EASI_CONF where CONF_KEY = ?", "CACHE_TYPE");
			String v2 = getConsoleQuery().queryForString("select CONF_VALUE from EASI_CONF where CONF_KEY = ?", "G_MEMCACHE_ADDR");
			JSONObject object=new JSONObject();
			object.put("CACHE_TYPE", decryptStr(v1));
			object.put("G_MEMCACHE_ADDR", decryptStr(v2));
			return EasyResult.ok(AesUtils.getInstance().encrypt(object.toJSONString()));
		} catch (SQLException e) {
			this.error(e,e);
			return EasyResult.fail();
		}
	}
	
	private String decryptStr(String str) {
		return ConfigCryptorService.decryptString(str,"GV");
	}
	
	private String encryptStr(String str) {
		return ConfigCryptorService.encryptString(str,"GV");
	}
	
	public EasyResult actionForRedisInfo(){
		if("redis".equals(ServerContext.getCacheType())){
			try {
				EasyCache cache = CacheManager.getMemcache();
				RedisImpl redis = (RedisImpl)cache;
				Jedis jedis = redis.getJedis();
				Map<String, String> map = parseInfoString(jedis.info());
				return EasyResult.ok(map);
			} catch (Exception e) {
				this.error(e,e);
			}
		}
		return EasyResult.ok();
		
	}
	
	 private static Map<String, String> parseInfoString(String info) {
	        Map<String, String> infoMap = new HashMap<>();
	        String[] lines = info.split("\\r?\\n");
	        for (String line : lines) {
	            // Ignore comments and empty lines
	            if (!line.startsWith("#") && !line.isEmpty()) {
	                String[] keyValue = line.split(":", 2);
	                if (keyValue.length == 2) {
	                    infoMap.put(keyValue[0], keyValue[1]);
	                }
	            }
	        }
	        return infoMap;
	    }
	
	public EasyResult actionForGetConf(){
		String confValue;
		try {
			confValue = getConsoleQuery().queryForString("select CONF_VALUE from EASI_CONF where CONF_KEY = ?", "redisConf");
			if(StringUtils.isNotBlank(confValue)) {
				confValue = decryptStr(confValue);
				JSONObject row = JSONObject.parseObject(confValue);
				row.remove("auth");
				confValue = row.toJSONString();
			}
			return EasyResult.ok(AesUtils.getInstance().encrypt(confValue));
		} catch (SQLException e) {
			return EasyResult.fail();
		}
		
	}
	public EasyResult actionForSaveConf2(){
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		JSONObject params = getJSONObject();
		String data = params.getString("encryptStr");
		data = AesUtils.getInstance().decrypt(data);
		data = data.replaceAll("\"", "'");
		
		JSONObject jsonObject = JsonKit.getJSONObject(data,null);

		String memcacheAddr = jsonObject.getString("G_MEMCACHE_ADDR");
		memcacheAddr = encryptStr(memcacheAddr);
		String  sql = "";
		try {
			if(getConsoleQuery().queryForExist("select count(1) from EASI_CONF where CONF_KEY = ?", "CACHE_TYPE")){
				sql = "update EASI_CONF set CONF_VALUE = ? , CAN_DELETE =3 where CONF_KEY = ?";
				this.getConsoleQuery().executeUpdate(sql, new Object[]{StringUtils.trimToEmpty(jsonObject.getString("CACHE_TYPE")),"CACHE_TYPE"});
			}else{
				sql = "insert into  EASI_CONF(CONF_KEY,CONF_VALUE,CAN_DELETE) values(?,?,3)";
				this.getConsoleQuery().execute(sql, new Object[]{"CACHE_TYPE",StringUtils.trimToEmpty(jsonObject.getString("CACHE_TYPE"))});
			}
			if(getConsoleQuery().queryForExist("select count(1) from EASI_CONF where CONF_KEY = ?", "G_MEMCACHE_ADDR")){
				sql = "update EASI_CONF set CONF_VALUE = ? , CAN_DELETE = 3 where CONF_KEY = ?";
				this.getConsoleQuery().executeUpdate(sql, new Object[]{memcacheAddr,"G_MEMCACHE_ADDR"});
			}else{
				sql = "insert into  EASI_CONF(CONF_KEY,CONF_VALUE,CAN_DELETE) values(?,?,3)";
				this.getConsoleQuery().execute(sql, new Object[]{"G_MEMCACHE_ADDR",memcacheAddr});
			}
			this.addOperateLog("修改Redis配置",jsonObject.toJSONString());
		} catch (SQLException e) {
			return EasyResult.fail(e.getMessage());
		}
		ServerContext.reload();
		return EasyResult.ok();
	}
	
	public EasyResult actionForSaveConf(){
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		JSONObject params = getJSONObject();
		String data = params.getString("encryptStr");
		data = AesUtils.getInstance().decrypt(data);
		data = data.replaceAll("\"", "'");
		
		
		EasyRecord record=new EasyRecord("EASI_CONF","CONF_KEY");
		record.set("CAN_DELETE", 3);
		record.set("CONF_KEY", "redisConf");
		record.set("CONF_VALUE",encryptStr(data));
		try {
			if(getConsoleQuery().queryForExist("select count(1) from EASI_CONF where CONF_KEY = ?", "redisConf")){
				getConsoleQuery().update(record);
			}else{
				getConsoleQuery().save(record);
			}
			this.addOperateLog("修改Redis参数配置",data);
			ServerContext.reload();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForCacheTest(){
		String type=getJsonPara("type");
		try {
			EasyCache cache = null;
			if("redis".equals(type)){
				 cache = CacheManager.getRedis();
			}else{
				 cache = CacheManager.getMemcache();
			}
			if(cache==null){
				return EasyResult.fail();
			}
			cache.put("test",RandomKit.uuid());
			String uuid=cache.get("test");
			if(StringUtils.isNotBlank(uuid)){
				cache.delete("test");
				return EasyResult.ok();
			}else{
				return EasyResult.fail("连接失败");
			}
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public EasyResult actionForReloadMemcache(){
		try {
			//CacheManager.getMemcache().reload();
			CacheManager.getRedis().reload();
			this.addOperateLog("重载Redis",null);
		} catch (Exception ex) {
			ex.printStackTrace();
			return EasyResult.error(500, ex.getMessage());
		}
		
		return EasyResult.ok();
	}
	
	
	@Override
	protected String getResId() {
		return null;
	}

}
