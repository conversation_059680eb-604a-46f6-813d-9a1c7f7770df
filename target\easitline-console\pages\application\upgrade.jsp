<%@page pageEncoding="UTF-8"%>
<div>
	<blockquote class="layui-elem-quote">
		升级Mars平台已有的应用，请上传符合Mars平台规范的应用，当前版本只支持WAR的应用。
       <button type="button" class="layui-btn layui-btn-primary layui-btn-xs layui-hide" id="upload-code" onclick="uploadCode()"><i class="layui-icon">&#xe67c;</i>上传代码</button>
		
	</blockquote>

	<fieldset class="layui-elem-field layui-field-title">
		<legend>应用升级，当前应用id： ${param.appId }</legend>
	</fieldset>

	<form class="layui-form" action="" autocomplete="off">
	  <div class="layui-form-item">
	    <label class="layui-form-label">选择应用</label>
	    <div class="layui-input-block" style="width: calc(100% - 220px);">
	        <button type="button" class="layui-btn layui-btn-small" onclick="uploadWar()"><i class="layui-icon">&#xe67c;</i>上传应用</button>
	   		<div class="layui-upload-drag" style="display: block;margin-top: 10px;" id="upload-drag">
				<i class="layui-icon layui-icon-upload"></i> <div>点击上传，或将文件拖拽到此处</div>
			</div>
	    </div>
	  </div>
	</form>
</div>

<script>
layui.use('upload', function(){
	  var upload = layui.upload;
	  var appId = "${param.appId}"; 
	  //执行实例
		  var uploadInst = upload.render({
			 elem: '#upload-drag'
		    ,url: '${ctxPath}/servlet/application?action=runUpgrade&appId='+appId //上传接口
		    ,number: 1
		    ,accept: 'file'
		    ,exts:'war|jar'
		    ,size:1024*150
		    ,field: 'appFile'//设定文件域的字段名
		    ,data: {appId:appId,action:'runUpgrade'}
		    ,done: function(res, index, upload){
		      //上传完毕回调
			    if(res&&res.state==1){
			    	layer.msg(res.msg,{icon: 1,time:800},function(){
						layer.closeAll();
						loadPage("application/list.jsp",{});
					}); 
				}else{
					layer.alert(res.msg,{icon: 5});
				}
		    },before:function(){
		    	
		    }
		    ,error: function(res, index, upload){
		      //请求异常回调
		    	layer.alert("上传文件请求异常！",{icon: 5});
		    }
      });
	  
	  var uploadInst2 = upload.render({
		    elem: '#upload-code'
		    ,url: '${ctxPath}/servlet/application?action=uploadCode'
		    ,number: 1
		    ,accept: 'file'
		    ,exts:'zip|tar|rar'
		    ,size:1024*150
		    ,field: 'appFile'
		    ,data: {appId:appId}
		    ,done: function(res, index, upload){
			    if(res&&res.state==1){
			    	layer.alert(res.msg,{icon: 1},function(){
						layer.closeAll();
					}); 
				}else{
					layer.alert(res.msg,{icon: 5});
				}
		    },before:function(){
		    	
		    },error: function(res, index, upload){
		    	layer.alert("上传文件请求异常！",{icon: 5});
		    }
      });
		  
	});
	
	function uploadWar(){
		layer.confirm("请确认是否升级到<br><font size=5 color=red>"+window.location.host+"?</font>",{title:'升级提醒',offset:'50px',success:function(){
			 $(document).keydown(function (event){
				 if(event.keyCode==13){ 
					 layer.closeAll();
					 $("#upload-drag").click();
				 }
			 });
		}},function(index){
	  		  layer.close(index);
	  		 $("#upload-drag").click();
	    });
	 }
</script>
