package org.easitline.console.monitor;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.service.IService;
import com.alibaba.fastjson.JSONObject;

/**
 * mars运行时告警
 * <AUTHOR>
 *
 */
public class MarsAlarm {

	private static Logger logger 		= LogEngine.getLogger("easitline-console","easitline-alarm");
	private static String appName 		= "easitline-console.war";
	private static String moduleName 	= "easitline-console";
	private static String EVENT_LEVEL_WARN 	= "warn";
	private static String EVENT_LEVEL_FATAL 	= "fatal";
	
	
	/**
	 * SQL执行时间超过60秒告警
	 * @param MaxTimespan
	 * @param warnSQLObj
	 */
	public static void sql60sAlarm(int MaxTimespan,JSONObject warnSQLObj){
		String eventCode = "MARS-00001";
		String eventName = "SQL执行时间超过60秒";
		String eventDesc = "SQL执行时间为："+(MaxTimespan/1000)+"秒，超过60秒，请检查应用SQL性能。";
		String exception = warnSQLObj.toJSONString();
		JSONObject params = new JSONObject();
		params.put("moduleName", moduleName);
		params.put("appName", appName);
		params.put("eventValue", MaxTimespan/1000);
		params.put("eventLevel", EVENT_LEVEL_WARN);
		params.put("eventCode", eventCode);
		params.put("eventName", eventName);
		params.put("eventDesc", eventDesc);
		params.put("exception", exception);
		doAlarm(params);
	}
	
	/**
	 * SQL平均执行时间超过3秒
	 * @param AvgExecuteTime
	 * @param ExecuteCount
	 * @param warnSQLObj
	 */
	public static void sql3sAlarm(int AvgExecuteTime,int ExecuteCount,JSONObject warnSQLObj){
		String eventCode = "MARS-00002";
		String eventName = "SQL平均执行时间超过3秒";
		String eventDesc = "SQL执行次数"+ExecuteCount+"次，平均执行时间["+(AvgExecuteTime/1000)+"]超过3秒，请检查应用SQL性能。";
		String exception = warnSQLObj.toJSONString();
		JSONObject params = new JSONObject();
		params.put("moduleName", moduleName);
		params.put("appName", appName);
		params.put("eventLevel", EVENT_LEVEL_WARN);
		params.put("eventValue", AvgExecuteTime/1000);
		params.put("eventCode", eventCode);
		params.put("eventName", eventName);
		params.put("eventDesc", eventDesc);
		params.put("exception", exception);
		doAlarm(params);
	}
	
	/**
	 * SQL平均执行时间超过3秒
	 * @param AvgExecuteTime
	 * @param ExecuteCount
	 * @param warnSQLObj
	 */
	public static void sql10sAlarm(int AvgExecuteTime,int ExecuteCount,JSONObject warnSQLObj){
		String eventCode = "MARS-00003";
		String eventName = "SQL平均执行时间超过10秒";
		String eventDesc = "SQL执行次数"+ExecuteCount+"次，平均执行时间["+(AvgExecuteTime/1000)+"]超过10秒，请检查应用SQL性能。";
		String exception = warnSQLObj.toJSONString();
		JSONObject params = new JSONObject();
		params.put("moduleName", moduleName);
		params.put("appName", appName);
		params.put("eventValue", AvgExecuteTime/1000);
		params.put("eventLevel", EVENT_LEVEL_WARN);
		params.put("eventCode", eventCode);
		params.put("eventName", eventName);
		params.put("eventDesc", eventDesc);
		params.put("exception", exception);
		doAlarm(params);
	}
	
	

	/**
	 * 数据源活跃连接数占比超过70%
	 * @param dsName
	 * @param ActiveCount
	 * @param MaxActive
	 * @param dsObj
	 */
	public static void ds70percentAlarm(String dsName,int ActiveCount,int MaxActive,JSONObject dsObj){
		String eventCode = "MARS-00004";
		String eventName = "连接池活跃连接数占比超过70%";
		String eventDesc = "链接池["+dsName+"]，活跃链接数为："+MaxActive+"，配置链接数为："+ActiveCount+"，占比超过70%，请检查数据库情况。";
		String exception = dsObj.toJSONString();
		JSONObject params = new JSONObject();
		params.put("moduleName", moduleName);
		params.put("appName", appName);
		params.put("eventLevel", EVENT_LEVEL_WARN);
		params.put("eventValue", 0.7);
		params.put("eventCode", eventCode);
		params.put("eventName", eventName);
		params.put("eventDesc", eventDesc);
		params.put("exception", exception);
		doAlarm(params);
	}
	
	/**
	 * 数据源连接数为0
	 * @param dsName
	 * @param MaxActive
	 * @param dsObj
	 */
	public static void ds0connAlarm(String dsName,int MaxActive,JSONObject dsObj){
		String eventCode = "MARS-00005";
		String eventName = "链接池可用链接数为0";
		String eventDesc = "链接池["+dsName+"]，配置链接数为："+MaxActive+"，活跃链接数已到达连接池上限，请检查数据库性能。";
		String exception = dsObj.toJSONString();
		JSONObject params = new JSONObject();
		params.put("moduleName", moduleName);
		params.put("appName", appName);
		params.put("eventLevel", EVENT_LEVEL_FATAL);
		params.put("eventCode", eventCode);
		params.put("eventName", eventName);
		params.put("eventDesc", eventDesc);
		params.put("exception", exception);
		doAlarm(params);
	}
	
	/**
	 * 数据源连接数为0
	 * @param dsName
	 * @param MaxActive
	 * @param dsObj
	 */
	public static void dsFailAlarm(String dsName,JSONObject dsObj){
		String eventCode = "MARS-00006";
		String eventName = "链接池创建数据库链接失败";
		String eventDesc = "连接池["+dsName+"]，创建数据库连接失败，请检查数据源配置。";
		String exception = dsObj.toJSONString();
		JSONObject params = new JSONObject();
		params.put("moduleName", moduleName);
		params.put("appName", appName);
		params.put("eventLevel", EVENT_LEVEL_FATAL);
		params.put("eventCode", eventCode);
		params.put("eventName", eventName);
		params.put("eventDesc", eventDesc);
		params.put("exception", exception);
		doAlarm(params);
	}
	
	public static void diskAlarm(String dirName,String total,String used){
		String eventCode = "MARS-00014";
		String eventName = "MARS服务器磁盘空间不足";
		String eventDesc = "磁盘["+dirName+"]空间为"+total+"，已使用空间"+used+"，可用率少于90%。";
		JSONObject params = new JSONObject();
		params.put("moduleName", moduleName);
		params.put("appName", appName);
		params.put("eventLevel", EVENT_LEVEL_WARN);
		params.put("eventCode", eventCode);
		params.put("eventName", eventName);
		params.put("eventDesc", eventDesc);
		params.put("exception", "");
		doAlarm(params);
	}
	
	/**
	 * 线程数超过指定
	 * @param threadCount
	 */
	public static void threadAlarm(int threadCount){
		String eventCode = "MARS-00015";
		String eventName = "MARS节点线程数过大";
		String eventDesc = "线程数["+threadCount+"]超过3000，请检查mars运行情况。";
		JSONObject params = new JSONObject();
		params.put("moduleName", moduleName);
		params.put("appName", appName);
		params.put("eventLevel", EVENT_LEVEL_WARN);
		params.put("eventCode", eventCode);
		params.put("eventName", eventName);
		params.put("eventDesc", eventDesc);
		params.put("exception", "MARS创建线程数过大，可能存在程序线程泄漏的情况。");
		doAlarm(params);
	}
	
	/**
	 * 内存分配过少
	 * @param sysMem
	 * @param marsMem
	 */
	public static void memoryAlarm(String sysMem,String marsMem){
		String eventCode = "MARS-00016";
		String eventName = "MARS节点分配内存过少";
		String eventDesc = "系统内存为"+sysMem+"，MARS分配内存为"+marsMem+"，分配内存过少。";
		JSONObject params = new JSONObject();
		params.put("moduleName", moduleName);
		params.put("appName", appName);
		params.put("eventLevel", EVENT_LEVEL_WARN);
		params.put("eventCode", eventCode);
		params.put("eventName", eventName);
		params.put("eventDesc", eventDesc);
		params.put("exception", "若单独运行MARS则分配系统内存2/3物理内存给mars，单个mars的内存建议不低于5G");
		doAlarm(params);
	}
	
	/**
	 * JVM可用内存过少告警
	 */
	public static void jvmAlarm(String jvmPer,int jvmCheckTimes){
		String eventCode = "MARS-00017";
		String eventName = "MARS节点JVM可用内存过少";
		String eventDesc = "JVM可用内存使用率["+jvmPer+"]持续检查次数["+jvmCheckTimes+"]超过90%，请检查MARS控制台JVM内存的使用情况。";
		JSONObject params = new JSONObject();
		params.put("moduleName", moduleName);
		params.put("appName", appName);
		params.put("eventLevel", EVENT_LEVEL_WARN);
		params.put("eventCode", eventCode);
		params.put("eventName", eventName);
		params.put("eventDesc", eventDesc);
		params.put("exception", "持续可用内存少，可能出现内存泄漏情况。");
		doAlarm(params);
	}
	
	public static void cpuAlarm(int size ,String idleAvg){
		String eventCode = "MARS-00018";
		String eventName = "MARS节点CPU占用率过高";
		String eventDesc = "CPU[个数："+size+"]的平均空闲率为:"+idleAvg+"，低于20%，请检查MARS运行情况。";
		JSONObject params = new JSONObject();
		params.put("moduleName", moduleName);
		params.put("appName", appName);
		params.put("eventLevel", EVENT_LEVEL_WARN);
		params.put("eventCode", eventCode);
		params.put("eventName", eventName);
		params.put("eventDesc", eventDesc);
		params.put("exception", "MARS节点的CPU过高，主要原因是由于频繁执行JVM垃圾回收或者程序死循环导致。");
		doAlarm(params);
	}
	
	
	private static void doAlarm(JSONObject params){
		IService service;
		try {
			logger.warn("[Alarm] << " +params);
			service = ServiceContext.getService("MARS-ALARM-SERVICE");
			if(service != null) service.invoke(params);
			else{
				logger.warn("未找到告警服务[MARS-ALARM-SERVICE]注册信息！");
			}
		} catch (Exception ex) {
			logger.warn("执行告警服务[MARS-ALARM-SERVICE]失败， params："+params.toJSONString()+",cause:"+ex.getMessage(),ex);
		}
	}
	
}
