package org.easitline.console.base;


import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalTime;

import javax.servlet.http.HttpServletResponse;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.web.EasyBaseServlet;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.utils.string.StringUtils;
import org.easitline.console.service.ConfigCryptorService;
import org.easitline.console.utils.ConsoleUtils;
import org.easitline.console.utils.SafeRequestWrapper;

import com.alibaba.fastjson.JSONObject;

public abstract class ConsoleBaseServlet extends EasyBaseServlet {
	private static final long serialVersionUID = 1L;

	@Override
	protected String getAppDatasourceName() {
		return "default-ds";  //console缺省访问系统数据源
	}

	@Override
	protected String getAppName() {
		return "easitline-console";
	}

	@Override
	protected String getLoggerName() {
		return "easitline-console";
	}
	protected   EasyQuery getConsoleQuery(){
		return Constants.getDb();
	}
	
	protected EasyQuery getSysDefaultQuery() {
		return EasyQuery.getQuery(getAppDatasourceName());
	}
	
	protected String getConsoleUpdateConfTime() {
		String profileActive  = ServerContext.getProperties("PROFILE_ACTIVE", "");
		if(!"prod".equals(profileActive)) {
			return null;
		}
		return appContext.getProperty("updateConfTime","");
	}
	
	@Override
	protected JSONObject getJSONObject() {
		String data = getRequest().getParameter("data");
		if(StringUtils.isNotBlank(data)&&data.contains("@type")) {
			return new JSONObject();
		}
		return super.getJSONObject();
	}
	
	protected boolean checkState() {
		if(!ConfigCryptorService.checkState()) {
			this.error("当前加密不可用，console不允许操作，请启动加密服务机器。", null);
			return false;
		}
		return true;
	}
	
	protected boolean hasOperateAuth() {
		if("2".equals(getRoleType())||"9".equals(getRoleType())) {
			return false;
		}
		return true;
	}
	
	protected EasyResult checkOperateTime() {
		if(!hasOperateAuth()){
			this.warn("无权操作>roleType>"+getRoleType()+">consoleUser>"+getConsoleUser(), null);
			return EasyResult.fail("无权操作");
		}
		if(!checkState()) {
			return EasyResult.fail("当前加密不可用，console不允许操作，请启动加密服务");
		}
		try {
			String date = getConsoleUpdateConfTime();
			if(StringUtils.isNotBlank(date)) {
				String[] hourRanges = date.split(",");
				for(String hourRange:hourRanges) {
					Integer beginHour = Integer.parseInt(hourRange.split("-")[0]);
					Integer endHour = Integer.parseInt(hourRange.split("-")[1]);
					int currentHour = LocalTime.now().getHour();
					if(currentHour >= beginHour && currentHour <= endHour) {
			            return EasyResult.ok();
			        }
				}
				return EasyResult.fail("当前时间不在【"+date+"】小时段内");
			}
		} catch (Exception e) {
			this.getLogger().error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	@Override
	protected String requestMethod(){
		String methodName = super.requestMethod();
		if("actionForIndex".equals(methodName)) {
			methodName = SafeRequestWrapper.getParameter(getRequest(), "action");
			if(StringUtils.isBlank(methodName)) {
				methodName = "actionForIndex";
			}else {
				methodName = "actionFor"+StringUtils.firstCharToUpperCase(methodName); 
			}
		}
		return methodName;
	}
	
	
	protected String getRoleType() {
		Object result = getRequest().getSession().getAttribute("MARS_CONSOLE_ROLE");
		if(result!=null) {
			return result.toString();
		}
		return null;
	}
	
	protected String getConsoleUser() {
		Object result = getRequest().getSession().getAttribute("MARS_CONSOLE_USER");
		if(result!=null) {
			return result.toString();
		}
		return null;
	}
	
	public void responseWriter(String conent){
		HttpServletResponse response = getResponse();
		response.setCharacterEncoding("UTF-8");
		PrintWriter writer = null;
		try {
			writer = response.getWriter();
			writer.write(conent);
			writer.flush();
			writer.close();
		} catch (IOException e) {
			e.printStackTrace();
		}finally {
			if(writer!=null){
				writer.close();
			}
		}
	}
	
	protected void addOperateLog(String msg,String params) {
		ConsoleUtils.addOperateLog(getRequest(), msg, params);
	}
	
	@Override
	protected void renderJson(Object obj){
		String content = null;
		if(obj!=null) {
			if(obj instanceof String){
				content = obj.toString();
			}else{
				content = JSONObject.toJSONString(content);
			}
			this.info(content, null);
		}
		super.renderJson(obj);
	}
	
	@Override
	protected String getResId() {
		return null;
	}

}
