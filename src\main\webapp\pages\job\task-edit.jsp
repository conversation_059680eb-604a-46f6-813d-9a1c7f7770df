<%@page pageEncoding="UTF-8"%>
<style>
   #month-radios .layui-input-inline {
      width: 80px
   }
</style>

<div>
   <div class="layui-tab" lay-filter="task-tab">
      <ul class="layui-tab-title">
         <li name="MinutesTab">定时</li>
         <li name="DailyTab" class="layui-this">每天</li>
         <li name="WeeklyTab">每周</li>
         <li name="MonthlyTab">每月</li>
         <li name="OneTimeTab">执行一次</li>
         <li name="CronTxt">CRON表达式</li>
      </ul>
      <div class="layui-tab-content">
         <div id="MinutesTab" class="layui-tab-item">
         	<form class="layui-form" action="">
	            <div class="layer-pannel">
	               <div class="layer-pannel-header">
	                    <h4>定时设置</h4>
	               </div>
	               <div class="layer-pannel-content">
	                    <div class="layui-form-item" style="margin-bottom: 0px;">
                            <div class="layui-inline">
	                           <div class="layui-input-inline">
	                              <input type="radio" id="executeTypeRadio1" name="executeTypeRadio" value="now" title="立即执行" lay-filter="executeTypeRadio">
	                           </div>
	                           <div class="layui-input-inline">
	                               <input type="radio" id="executeTypeRadio2" name="executeTypeRadio" value="time" title="延迟执行" checked="checked" lay-filter="executeTypeRadio">
	                           </div>
	                        </div>
                            <br>
                            <div class="layui-inline" style="width:  260px;">
                              <label class="layui-form-label" style="text-align: left;width:60px;">执行间隔</label>
                              <div class="layui-input-inline" style="width: 60px;">
                                 <input type="text" id="MinutesTab-intervalInput" class="layui-input" value="1">
                              </div>
                              <div class="layui-input-inline" style="width: 80px">
                                 <select id="MinutesTab-intervalTypeSel" lay-filter="intervalTypeSel">
			                         <option value="hour">小时</option>
					                 <option value="minute">分钟</option>
					                 <option value="second">秒</option>
					              </select>
                              </div>
                           </div>
	                        <!-- <div class="layui-inline">
                              <label class="layui-form-label" style="text-align: left;width:60px;padding-left:0px">定时频率</label>
                              <div class="layui-input-inline" style="width: 60px;">
                                 <input type="Number" id="minutesInput" class="layui-input" value="0" max-length="2"> 
                              </div>
                              <label class="layui-form-label" style="text-align: left;padding:9px 0px;width: 35px;">分钟</label>
                              <div class="layui-input-inline" style="width: 60px;">
                                 <input type="Number" id="secondsInput" class="layui-input" value="1" max-length="2"> 
                              </div>
                              <label class="layui-form-label" style="text-align: left;padding-left:0px">秒 执行一次</label>
                            </div> -->
	                        <br>
	                        <div class="layui-inline" style="margin-top:10px" id="lateTime">
                              <label class="layui-form-label" style="text-align: left;width:100px;padding-left:0px">延迟执行时间：</label>
                              <div class="layui-input-inline" style="width: 350px;">
                              	  <input type="text" name="" class="layui-input" value="00:00:00" id="lateTimeVal">
                                  <!-- <input type="Number" id="beginHour" class="layui-input" value="0" style="width: 80px;display:inline-block"><span> 小时</span>
                                  &nbsp;&nbsp;<input type="Number" id="beginMinute" class="layui-input" value="1" style="width: 80px;display:inline-block"><span> 分钟</span>
                                  &nbsp;&nbsp;<input type="Number" id="beginSeconds" class="layui-input" value="1" style="width: 80px;display:inline-block"><span> 秒</span> -->
                              </div>
                            </div>
	                    </div>
	               </div>
	            </div>
	        </form>
         </div>
         
         <div id="DailyTab" class="layui-tab-item layui-show">&nbsp;</div>
         
         <div  id="WeeklyTab" class="layui-tab-item">
            <form class="layui-form" action="">
               <div class="layer-pannel" style="margin-bottom:10px">
                  <div class="layer-pannel-header">
                     <h4>每周频率</h4>
                  </div>
                  <div class="layer-pannel-content">
                     <div class="layui-form-item" style="margin-bottom: 0px;">
                        <div class="layui-inline">
                              <input type="checkbox" lay-skin="primary" value="SUN" txt="周日" title="周日" lay-filter="weekCheckbox">
                              <input type="checkbox" lay-skin="primary" value="MON" txt="周一" title="周一" lay-filter="weekCheckbox">
                              <input type="checkbox" lay-skin="primary" value="TUE" txt="周二" title="周二" lay-filter="weekCheckbox">
                              <input type="checkbox" lay-skin="primary" value="WED" txt="周三" title="周三" lay-filter="weekCheckbox">
                              <input type="checkbox" lay-skin="primary" value="THU" txt="周四" title="周四" lay-filter="weekCheckbox">
                              <input type="checkbox" lay-skin="primary" value="FRI" txt="周五" title="周五" lay-filter="weekCheckbox">
                              <input type="checkbox" lay-skin="primary" value="SAT" txt="周六" title="周六" lay-filter="weekCheckbox">
                        </div>
                     </div>
                  </div>
               </div>
            </form>
         </div>
         
         <div id="MonthlyTab" class="layui-tab-item">
            <form class="layui-form" action="">
               <div class="layer-pannel" style="margin-bottom:10px">
                  <div class="layer-pannel-header">
                     <h4>每月频率</h4>
                  </div>
                  <div class="layer-pannel-content">
                     <div class="layui-form-item" style="margin-bottom: 0px;">
                        <div class="layui-inline" id="month-radios">
                        </div>
                     </div>
                  </div>
               </div>
            </form>
         </div>
         
         <div id="OneTimeTab" class="layui-tab-item">
            <form class="layui-form" action="">
               <div class="layer-pannel">
                  <div class="layer-pannel-header">
                     <h4>执行时间</h4>
                  </div>
                  <div class="layer-pannel-content">
                     <div class="layui-form-item" style="margin-bottom: 0px;">
                        <div class="layui-inline">
                        	<label class="layui-form-label" style="text-align:  left;width: 60px;">日期</label>
                        	<div class="layui-input-inline" style="width:100px;">
                           		<input type="text" class="layui-input" id="oneTimeDate">
                            </div>
                        </div>
                        <div class="layui-inline">
                        	<label class="layui-form-label" style="text-align:  left;width: 60px;">时间</label>
                        	<div class="layui-input-inline" style="width:100px;">
	                           <input type="text" class="layui-input" id="oneTimeTime">
                            </div>
                        </div>
                     </div>
                  </div>
               </div>
            </form>
         </div>
         <div id="CronTxt" class="layui-tab-item">
            <form class="layui-form" action="">
               <div class="layer-pannel">
                  <div class="layer-pannel-header">
                     <h4>CRON表达式</h4>
                  </div>
                  <div class="layer-pannel-content">
                     <div class="layui-form-item" style="margin-bottom: 0px;">
                        <div class="layui-inline">
                        	<div class="layui-input-inline" style="width:300px;">
                           		<input type="text" class="layui-input" id="cronContent">
                            </div>
                            <div class="layui-input-inline" style="padding: 5px 0px;">
                            	<a href="http://cron.qqe2.com/" target="_blank" style="color: #1E9FFF;">参考</a>
                            </div>
                        </div>
                     </div>
                  </div>
               </div>
            </form>
         </div>
         
         <div id="commonDiv" class="layui-tab-common">
         	<form class="layui-form" action="">
               <div class="layer-pannel" id="everyDayTable">
                  <div class="layer-pannel-header">
                     <h4>每天频率</h4>
                  </div>
                  <div class="layer-pannel-content">
                     <div class="layui-form-item" style="margin-bottom: 0px;">
                        <div class="layui-inline">
                           <div class="layui-input-inline">
                              <input type="radio" name="timeTypeRadio" value="oneTime" title="每天执行一次" lay-filter="timeTypeRadio">
                           </div>
                           <div class="layui-input-inline">
                              <input type="radio" name="timeTypeRadio" value="moreTime" title="每天定时执行" checked="checked" lay-filter="timeTypeRadio">
                           </div>
                        </div>
                        <div style="margin-top:10px;display: none;" id="method1">
                        	<div class="layui-inline">
                              <label class="layui-form-label" style="text-align: left;width:90px;">每天执行时间</label>
                              <div class="layui-input-inline" style="width: 100px;">
                                 <input type="text" id="oneTimeTimer" class="layui-input">
                              </div>
                            </div>
                        </div>
                        <div style="margin-top:10px" id="method2">
                           <div class="layui-inline" style="width:  260px;">
                              <label class="layui-form-label" style="text-align: left;width:60px;">执行间隔</label>
                              <div class="layui-input-inline" style="width: 60px;">
                                 <input type="text" id="intervalInput" class="layui-input" value="1">
                              </div>
                              <div class="layui-input-inline" style="width: 80px">
                                 <select id="intervalTypeSel" lay-filter="intervalTypeSel">
			                         <option value="hour">小时</option>
					                 <option value="minute">分钟</option>
					                 <option value="second">秒</option>
					              </select>
                              </div>
                           </div>
                           <div class="layui-inline">
                              <label class="layui-form-label" style="text-align:  left;width: 60px;">开始时间</label>
                              <div class="layui-input-inline" style="width:100px;">
                                 <input type="text" name="" class="layui-input" value="00:00:00" id="beginTime">
                              </div>
                           </div>
                           <div class="layui-inline">
                              <label class="layui-form-label" style="text-align:  left;width: 60px;">结束时间</label>
                              <div class="layui-input-inline" style="width:100px;">
                                 <input type="text" name="" class="layui-input" value="23:59:59" id="endTime">
                              </div>
                           </div>
                        </div>

                     </div>
                  </div>
               </div>
               <div class="layer-pannel"  id="keepTimeTable">
                  <div class="layer-pannel-header">
                     <h4>持续时间</h4>
                  </div>
                  <div class="layer-pannel-content">
                     <div class="layui-form-item" style="margin-bottom: 0px;">
                        <div class="layui-inline">
                           <label class="layui-form-label">开始时间</label>
                           <div class="layui-input-inline">
                              <input type="text" class="layui-input" id="beginDate" value="">
                           </div>
                        </div>
                        <div class="layui-inline">
                           <label class="layui-form-label">结束时间</label>
                           <div class="layui-input-inline">
                              <input type="text" class="layui-input" id="endDate" value="">
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="layer-pannel">
                  <div class="layer-pannel-header">
                     <h4>摘要</h4>
                  </div>
                  <div class="layer-pannel-content">
                     <p class="summary-text" id="summarySpan">每天在00:00:00和23:59:59之间，每1小时执行。将从2018-02-05开始使用计划。</p>
                  </div>
               </div>
            </form>         	
        </div>
         
      </div>
   </div>
</div>

<script>
   layui.use(['form', 'laydate','element'], function() {
      var monthRadio = [];
      for(var i = 1; i <= 31; i++) {
         var date = i < 10 ? '0' + i.toString() : i;
         var html = '<input type="checkbox" lay-skin="primary" value="'+i+'" title="' + date + '"  lay-filter="monthCheckbox">';
         monthRadio.push(html);
      }
      $("#month-radios").empty().html(monthRadio.join(""))
      var form = layui.form;
      form.render('select'); //刷新select选择框渲染
      form.render('radio'); 
      form.render('checkbox'); 
      
      var laydate = layui.laydate;

      //执行一个laydate实例
      laydate.render({
         elem: '#oneTimeDate' //指定元素
        ,done: function(value, date, endDate){
        	changeDesc();
     	}
      });
      laydate.render({
          elem: '#oneTimeTime' //指定元素
          ,type: 'time'
          ,done: function(value, date, endDate){
             changeDesc();
          }
       });
      //延迟时间（时间）
      laydate.render({
         elem: '#lateTimeVal' //指定元素
         ,type: 'time'
         ,done: function(value, date, endDate){
             changeDesc();
         }
      });
	  //开始时间（时间）
      laydate.render({
         elem: '#beginTime' //指定元素
         ,type: 'time'
         ,done: function(value, date, endDate){
             changeDesc();
         }
      });
      laydate.render({
         elem: '#endTime' //指定元素
         ,type: 'time'
         ,done: function(value, date, endDate){
             changeDesc();
         }
      });
      //持续时间（日期）
      laydate.render({
          elem: '#beginDate' //指定元素
          ,done: function(value, date, endDate){
             changeDesc();
          }
       });
      laydate.render({
          elem: '#endDate' //指定元素
          ,done: function(value, date, endDate){
              changeDesc();
          }
       });
      
      laydate.render({
         elem: '#oneTimeTimer' //指定元素
         ,type: 'time'
         ,done: function(value, date, endDate){
             	changeDesc();
          }
      });
      laydate.render({
          elem: '#timingLatetime' //指定元素
          ,type: 'time'
        	,done: function(value, date, endDate){
              	changeDesc();
           	}
       });
      
      //切换每天频率类型
      form.on('radio(timeTypeRadio)', function(data){
		  var method = data.value;
		  if(method=="oneTime"){
		  	  $("#method1").show();
		  	  $("#method2").hide();
		  }else if(method=="moreTime"){
		  	  $("#method2").show();
		  	  $("#method1").hide();
		  }
		  changeDesc();
	  });  
      
      form.on('radio(executeTypeRadio)', function(data){
		  var method = data.value;
		  if(method=="now"){
		  	  $("#lateTime").hide();
		  }else if(method=="time"){
		  	  $("#lateTime").show();
		  }
		  changeDesc();
	  });
      
      form.on('checkbox(weekCheckbox)', function(data){
		  changeDesc();
	  });
      form.on('checkbox(monthCheckbox)', function(data){
		  changeDesc();
	  }); 
      form.on('select(intervalTypeSel)', function(data){
		  changeDesc();
	  });
      
   });
</script>
<script type="text/javascript" src="${ctxPath}/pages/job/cron-validate.js"></script>
<script type="text/javascript" src="${ctxPath}/pages/job/input-cron.js"></script>
