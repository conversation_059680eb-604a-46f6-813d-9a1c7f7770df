<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://www.easitline.org/easitline-taglib" prefix="EasyTag"%>
<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<title>SQL查询</title>
		 <LINK rel="Shortcut Icon" href="${ctxPath}/favicon.ico" />
		<link rel="stylesheet" href="${staticPath}/lib/layui/css/layui.css">
		<link rel="stylesheet" href="${ctxPath}/static/css/site.css">
		<script type="text/javascript" src="${staticPath}/js/jquery.min.js"></script> 
		<style type="text/css">
		 body{padding: 10px 30px;margin: 10px 30px;}
		 .layui-input-block{width: 80%;}
		 .layui-textarea{height: 130px;}
	     	pre {outline: 1px solid #ccc; padding: 5px; margin: 5px;max-height: 200px;overflow-y: scroll; }
           .string { color: green; }
           .number { color: darkorange; }
           .boolean { color: blue; }
           .null { color: magenta; }
           .key { color: red; }
           #sqlToJson{display: none;}
           #sqlMsg{display: none;}
		</style>
	</head>
<body>
	<form id="easyform" class="layui-form">
		<div>
				<blockquote class="layui-elem-quote">
					查询SQL语句,符号;分割执行待查询的SQL
				</blockquote>
				<div class="layui-form-item layui-form-text">
				     <div class="layui-input-block" id="sqlMsg">
				     </div>
			     </div>
				<div class="layui-form-item layui-form-text">
				     <div class="layui-input-block" id="sqlToJson">
				     	<!--  -->
				     </div>
			     </div>
				<div class="layui-form-item layui-form-text">
				    <label class="layui-form-label">SQL</label>
				     <div class="layui-input-block">
				     	 <textarea id="sqlContent" placeholder="请输入SQL" class="layui-textarea"></textarea>
				     </div>
			     </div>
			     <div class="layui-form-item">
				    <div class="layui-input-block">
				      <button class="layui-btn" type="button" onclick="excute()">立即提交</button>
				      <button type="reset" class="layui-btn layui-btn-primary">重置</button>
				    </div>
				  </div>
		</div>
		 
	</form>
	<script type="text/javascript" src="${staticPath}/js/jsrender.min.js"></script>
	<script type="text/javascript" src="${staticPath}/lib/layer/layer.js"></script>
	<script type="text/javascript" src="${staticPath}/lib/layui/layui.js"></script>
	<script type="text/javascript" src="${staticPath}/js/easitline.core-2.0.0.js"></script>
	<script type="text/javascript" src="${staticPath}/js/requreLib.js"></script>
	<script type="text/javascript">
		function excute(){
			$("#sqlToJson").hide();
			$("#sqlMsg").hide();
			var data={};
			data.sqlStr=$("#sqlContent").val();
			data.dsName='${param.dsName}';
			ajax.remoteCall("${ctxPath}/servlet/datasource?action=querySql", data,function(result){  
				$("#sqlMsg").show();
				if(result.state==1){
					$("#sqlToJson").show();
					$("#sqlMsg").html(result.msg);
				}else{
					$("#sqlMsg").html(result.msg);
				}
			},{then:function(result){
				$("#sqlToJson").html("");
				for(var i in result.data){
					$("#sqlToJson").append("<pre>"+syntaxHighlight(result.data[i])+"</pre>");
				}
			}});
		}
		 function syntaxHighlight(json) {
	            if (typeof json != 'string') {
	                json = JSON.stringify(json, undefined, 2);
	            }
	            json = json.replace(/&/g, '&').replace(/</g, '<').replace(/>/g, '>');
	            return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function(match) {
	                var cls = 'number';
	                if (/^"/.test(match)) {
	                    if (/:$/.test(match)) {
	                        cls = 'key';
	                    } else {
	                        cls = 'string';
	                    }
	                } else if (/true|false/.test(match)) {
	                    cls = 'boolean';
	                } else if (/null/.test(match)) {
	                    cls = 'null';
	                }
	                return '<span class="' + cls + '">' + match + '</span>';
	            });
	        }
	</script>
</body>
</html>
