<%@page import="org.easitline.common.core.resource.ServiceResource"%>
<%@page import="java.util.List"%>
<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE HTML>
 <%
   List<ServiceResource> list = (List<ServiceResource>)request.getAttribute("list");
 %>
<div class="modal-header">
	<button class="sui-close" aria-hidden="true" type="button"
		data-dismiss="modal">×</button>
	<h4 class="modal-title" id="mgrs_label">选择ESB服务</h4>
</div>
<div class="modal-body">
 <!-- 面板1 -->
 <div class="panel mb-20">
   <div class="easitline-panel">
         <div class="easitline-panel-content">

           <!-- table添加table-zebra2 class即可实现隔行换色 -->
           <table class="sui-table table-zebra2">
             <thead>
               <tr> 
                 <th class="text-center">选择</th>
                 <th width="150" class="text-center">服务ID</th>
                 <th class="text-center">服务名称</th>
                 <th class="text-center">服务实现类</th>
               </tr>
             </thead>
             <tbody>
             <%for(ServiceResource resource:list){ %>
               <tr class="">
                 <td class="text-center"><%=resource.serviceId%></td>
                 <td class="text-center"><%=resource.serviceName%></td>
                 <td class="text-center"><%=resource.className%></td>
                 <td class="text-center">
                     <input name="servieId" type="radio"  serviceId='<%=resource.serviceId%>' serviceName='<%=resource.serviceName%>'  onclick='selectService(this);' />
                 </td>
                </tr>
            <%}%>
             </tbody>
           </table>
         </div>
   </div>
 </div>
 </div>
<input type="hidden" id="serviceIdHide" value="" />
<input type="hidden" id="serviceNameHide" value="" />
<div class="modal-footer">					
	<button class="sui-btn btn-primary btn-large" type="button" id="mgrs_ok">确定</button>
	<button class="sui-btn btn-default btn-large" type="button" data-dismiss="modal">关闭</button>
</div>
<script type="text/javascript">
$(function(){
	
	 $("#mgrs_ok").click(function(){		
	    	var serviceId = $("#serviceIdHide").val();
	    	var serviceName = $("#serviceNameHide").val();
	    	var data = {serviceId:serviceId,serviceName:serviceName};
	    	callbackService(data);
	    	$("#lookup_modal").modal("hide");
	    });
});

//选择服务
function selectService(e) {
	var serviceId = $(e).attr("serviceId");
	var serviceName = $(e).attr("serviceName");
	$("#serviceNameHide").val(serviceName);
	$("#serviceIdHide").val(serviceId);
}
</script>