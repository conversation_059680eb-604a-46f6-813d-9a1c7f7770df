<%@page pageEncoding="UTF-8"%>
<style>
	.layui-input-block{width: 93%;}
	.layui-input-block{margin-left: 30px;}
</style>
<div>
	<form class="layui-form" method="post" id="easyform"  autocomplete="off">
		  <div class="layui-form-item">
		    <div class="layui-input-block">
		      <br>
		      <input type="text" name="codeKey" value="ps -ef|grep ${param.warName}" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <div class="layui-input-block">
		    	<pre  lay-height="200px" lay-title="代码" class="layui-code" lay-skin="notepad">
				 </pre>
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <div class="layui-input-block">
		      <button type="button" onclick="doExcute('search')" class="layui-btn layui-btn-sm">执行</button>&nbsp;&nbsp;
		    </div>
		  </div>
	</form>
</div>

<script type="text/javascript" src="${staticPath}/lib/jquery/jquery.base64.min.js"></script>
<script>
	
	$(function(){
		doExcute();
	});
	
	function doExcute() {
		var data = form.getJSONObject("easyform"); 
		data['codeKey'] = $.base64.encode(data['codeKey']);
		ajax.remoteCall("${ctxPath}/servlet/serverInfo?action=getServerInfo",data,function(result) { 	
			if(result.state==1){
				layer.msg(result.msg,{icon: 1,time:800},function(){
					var str = result.data || '执行完毕';
					$(".layui-code").html(str);
					
					layui.use(['code'], function(){
						layui.code({about:true});
						
						}
					);
					
				});
			}else{
				layer.msg(result.msg,{icon: 5});
			}
		},{timeout:2000});
	}
	
	
</script>