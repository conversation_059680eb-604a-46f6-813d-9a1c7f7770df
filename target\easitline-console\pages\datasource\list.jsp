<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<form id="easyform">
<div>
	<blockquote class="layui-elem-quote">
		数据源管理用于配置Mars平台数据库连接池，这里新增的数据源为系统数据源，系统数据源和应用数据源的对应关系在应用配置中执行指定。
		<a href="javascript:;" onclick="datasourceInfo('')">连接池信息</a>  
		<c:if test="${param.showExcute=='1'}">
			<a href="javascript:void(0)" onclick="openConsoleDs();">管理</a>
		</c:if>
		<br><small style="color: red;">慢SQL执行时间过长会使数据库连接资源被长时间占用，导致数据库连接池中的连接被耗尽，无法为其他请求提供连接，从而造成系统瓶颈。建议每周/每月优化出现的慢SQL</small>
	</blockquote>

	<fieldset class="layui-elem-field layui-field-title">
		<legend>系统数据源列表</legend>
	</fieldset>

	<div class="layui-form" style="padding-left:15px">
		<table class="layui-table text-l" data-mars="datasource.list">
			<thead>
				<tr>
					<th>序号</th>
					<th>数据源名称</th>
					<th>数据库类型</th>
					<th>IP</th>
					<th>端口</th>
					<th>最少连接数</th>
					<th>最大连接数</th>
					<th class="druid">池中连接数/峰值</th>
					<th class="druid">活跃连接数/峰值</th>
					<th>服务名/数据源名/数据库名</th>
					<th>登陆账号</th>
					<th>操作</th>
				</tr>
			</thead>
			<tbody id="dataList">
				<script id="list-template" type="text/x-jsrender">
					{{for  list}}
						<tr>
							<td>{{:#index+1}}</td>
							<td title="点击编辑数据源" onclick="edit('{{:SYS_DS_NAME}}')"><a href="javascript:void(0);">{{:SYS_DS_NAME}}</a></td>
							<td>{{:DB_TYPE}} {{if DB_TYPE_NAME!=DB_TYPE}}/{{:DB_TYPE_NAME}}{{/if}}</td> 
							<td>{{:IP_ADDR}}</td>  
							<td>{{:IP_PORT}}</td>
							<td>{{:MIN_CONN}}</td>
							<td>{{:MAX_CONN}}</td>
							<td class="druid PoolingCount_{{:SYS_DS_NAME}}"></td>
							<td class="druid ActiveCount_{{:SYS_DS_NAME}}"></td>
							<td>{{:DB_NAME}}</td>
							<td>{{:USERNAME}}</td>
							<td>
								<div class="sqlExcute">
								 <a  href="javascript:openTools('${ctxPath}/pages/datasource/tools.jsp?dsName={{:SYS_DS_NAME}}&dbType={{:DB_TYPE}}')">tools</a>
								</div>	
								<div class="druid sqlView_{{:SYS_DS_NAME}}"></div>						
							</td>
						</tr>
					{{/for}}					         
				</script>
			</tbody>
		</table>
		<button type="button" style="margin-top:6px;margin-left:10px" class="layui-btn layui-btn-small layui-btn-normal" onclick="edit()">新增数据源</button>
		<a href="${ctxPath}/servlet/datasource?action=reloadAll" target="_blank" style="margin-top:6px;margin-right:10px;float: right;color: #28a3ef" >重载所有数据源</a>
		<br>
		<br>
		<br>
		<br>
		<br>
	</div>
</div>
</form>
<script type="text/javascript">
	$(function(){
		$("#easyform").searchData({success:function(){
			var showExcute='${param.showExcute}';
			if(showExcute==1){
				$(".sqlExcute").show();
			}else{
				$(".sqlExcute").hide();
			}
			getDruidJson();
		}});
	});
	function openTools(url){
		  var ww = window.screen.width-100;
	      var hh = window.screen.height - 100;
	      window.open(url);//,"","resizable=yes,top=30,left=30,width="+ww+",height="+hh+",status=no,menubar=no,toolbar=no,Scrollbars=no,Location=no,Direction=no,resizable=no");
		//var index=layer.open({type:2,title:'sql',maxmin:true,area:['80%','90%'],offset:'r',shade:false,content:url});
		//layer.full(index);
	}
	//添加或修改
	function edit(dName){
		loadPage("datasource/edit.jsp",{pkId:dName,dName:dName});
    }
	
	function openConsoleDs(){
		window.open("${ctxPath}/pages/datasource/tools.jsp?dsName=consoleDs","内置数据源管理");
	}
	
	
	function getDruidJson(){
		 $.ajax({
			  url:'${ctxPath}/servlet/monitor/druid/datasource.json',
			  data: {},
			  timeout:5000,
			  success: function(data){
				  console.log(data);
				  if(data.ResultCode=='1'){
					  var arrayList = data.Content;
					  for(var index in arrayList){
						  var json = arrayList[index];
						  var dsId = json['Identity'];
						  $(".ActiveCount_"+json['Name']).text(json['ActiveCount']+"/"+json['ActivePeak']);
						  $(".PoolingCount_"+json['Name']).html('<a href="javascript:;" onclick="connectionInfo(\''+dsId+'\')">'+json['PoolingCount']+"/"+json['PoolingPeak']+'</a>');
						  $(".sqlView_"+json['Name']).append('<a href="javascript:;" onclick="viewSql(\''+dsId+'\')">sql列表</a>');
						  $(".datasourceInfo_"+json['Name']).append('<a href="javascript:;" onclick="datasourceInfo(\''+dsId+'\')">sql列表</a>');
					  }
				  }else{
					  layer.msg('请求失败');
				  }
			  },
			  dataType: 'json'
			});	
	}
	
	function viewSql(id){
		layer.open({title:'sql列表',content:'/easitline-console/servlet/monitor/druid/sql.html?dataSourceId='+id,type:2,maxmin:true,area:['85%','85%']})
	}
	function connectionInfo(id){
		layer.open({title:'连接池信息',content:'/easitline-console/servlet/monitor/druid/connectionInfo.html?datasourceId='+id,type:2,maxmin:true,area:['85%','85%']})
	}
	
	function datasourceInfo(id){
		layer.open({title:'数据源信息',content:'/easitline-console/servlet/monitor/druid/datasource.html?datasourceId='+id,type:2,maxmin:true,area:['85%','85%']})
	}
	
</script>
