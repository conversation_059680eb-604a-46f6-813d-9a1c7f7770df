<%@page import="org.easitline.common.core.context.ServerContext"%>
<%@page import="org.easitline.common.db.EasyQuery"%>
<%@page pageEncoding="UTF-8"%>
<%
	EasyQuery query = ServerContext.getConsoleQuery();

	boolean bl = query.queryForExist("select count(1) from EASI_USER where LOGIN_ACCT = ?", new Object[]{"admin@mars"});
	if(bl){
		query.executeUpdate("delete from EASI_USER where LOGIN_ACCT = ?", new Object[]{"admin"});
	}
	query.executeUpdate("delete from EASI_USER where LOGIN_ACCT = ?", new Object[]{"admin@console"});
	out.print("ok");
	

%>