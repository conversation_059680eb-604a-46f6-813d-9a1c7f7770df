package org.easitline.console.vo;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.easitline.common.db.EasyRowMapper;

public class DatasourceRowMapper implements EasyRowMapper<DatasourceModel> {

	@Override
	public DatasourceModel mapRow(ResultSet rs, int rowNum) {
		// TODO Auto-generated method stub
		DatasourceModel vo = new DatasourceModel();
		try {
			vo.setDataBaseName(rs.getString("DB_NAME"));
			vo.setDataBaseType(rs.getString("DB_TYPE"));
			vo.setDataSourceName(rs.getString("SYS_DS_NAME"));
			vo.setIpAddress(rs.getString("IP_ADDR"));
			vo.setMaxConnection(rs.getString("MAX_CONN"));
			vo.setMinConnection(rs.getString("MIN_CONN"));
			vo.setPassword(rs.getString("PASSWORD"));
			vo.setPort(rs.getString("IP_PORT"));
			vo.setUserName(rs.getString("USERNAME"));
		} catch (SQLException ex) {
			//ConsoleLogger.
			ex.printStackTrace();
		}
		return vo;
		
	}

}
