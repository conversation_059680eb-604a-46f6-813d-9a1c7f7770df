package org.easitline.console.utils;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.SecretKeySpec;

import org.easitline.common.utils.crypt.BASE64Util;
import org.apache.commons.lang3.StringUtils;
 
/**
 * AES的加密和解密
 */
public class AesUtils{
	
    //16位密钥 (需要前端和后端保持一致)
    private String KEY = "mLHgjzdkhkxAFY6f";  
    
    public static String USER_KEY = "mLHgjzdkhkxAFY6f";  
   
    //算法
    private static final String ALGORITHMSTR = "AES/ECB/PKCS5Padding";
    
    public static AesUtils getInstance() {
    	return new AesUtils();
    }
    
    public static AesUtils getInstance(String key) {
    	return new AesUtils(key);
    }
    
    public AesUtils() {
    	
    }
    
    public AesUtils(String key) {
    	if(StringUtils.isNotBlank(key)) {
    		this.KEY = key;
    	}
    }
    
    public String getKEY() {
		return KEY;
	}


	/** 
     * aes解密 
     * @param encrypt   内容 
     * @return 
     * @throws Exception 
     */  
    public  String decrypt(String encrypt) {  
        try {
            return decrypt(encrypt, getKEY());
        } catch (Exception e) {
            return "";
        }  
    }  
      
    /** 
     * aes加密 
     * @param content 
     * @return 
     * @throws Exception 
     */  
    public  String encrypt(String content) {  
        try {
            return encrypt(content, getKEY());
        } catch (Exception e) {
            return "";
        }  
    }
    
    /** 
     * base 64 decode 
     * @param base64Code 待解码的base 64 code 
     * @return 解码后的byte[] 
     * @throws Exception 
     */  
    private static byte[] base64Decode(String base64Code) throws Exception{  
        return StringUtils.isEmpty(base64Code) ? null : BASE64Util.decode(base64Code);  
    }  
  
      
    /** 
     * AES加密 
     * @param content 待加密的内容 
     * @param encryptKey 加密密钥 
     * @return 加密后的byte[] 
     * @throws Exception 
     */  
    public static byte[] encryptToBytes(String content, String encryptKey) throws Exception {  
        KeyGenerator kgen = KeyGenerator.getInstance("AES");  
        kgen.init(128);  
        Cipher cipher = Cipher.getInstance(ALGORITHMSTR);  
        cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(encryptKey.getBytes(), "AES"));  
  
        return cipher.doFinal(content.getBytes("utf-8"));  
    }  
  
  
    /** 
     * AES加密为base 64 code 
     * @param content 待加密的内容 
     * @param encryptKey 加密密钥 
     * @return 加密后的base 64 code，若加密异常则返回原文
     */  
    public static String encrypt(String content, String encryptKey) {  
        try {
            return BASE64Util.encode(encryptToBytes(content, encryptKey));  
        } catch (Exception e) {
            return content;
        }
    }  
  
    /** 
     * AES解密 
     * @param encryptBytes 待解密的byte[] 
     * @param decryptKey 解密密钥 
     * @return 解密后的String 
     * @throws Exception 
     */  
    public static String decryptByBytes(byte[] encryptBytes, String decryptKey) throws Exception {  
        KeyGenerator kgen = KeyGenerator.getInstance("AES");  
        kgen.init(128);  
  
        Cipher cipher = Cipher.getInstance(ALGORITHMSTR);  
        cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(decryptKey.getBytes(), "AES"));  
        byte[] decryptBytes = cipher.doFinal(encryptBytes);  
        return new String(decryptBytes);  
    }  
  
  
    /** 
     * 将base 64 code AES解密 
     * @param encryptStr 待解密的base 64 code 
     * @param decryptKey 解密密钥 
     * @return 解密后的string，若解密异常则返回原文
     */  
    public static String decrypt(String encryptStr, String decryptKey) {  
        try {
            return StringUtils.isEmpty(encryptStr) ? null : decryptByBytes(base64Decode(encryptStr), decryptKey);  
        } catch (Exception e) {
            return encryptStr;
        }
    }  
    
}