<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
	 <blockquote class="layui-elem-quote" onclick="getGlobalConfig()">
		<a href="javascript:void(0)" onclick="loginLogPage()">登录日志</a>
		<a style="margin-left: 20px;" href="javascript:void(0)" onclick="appLogPage()">应用日志</a>
	 </blockquote>
	 <fieldset class="layui-elem-field layui-field-title">
		<legend>操作日志</legend>
	  </fieldset>
		<form id="easyform-log-list">
			<div style="padding:0 15px">
				<table class="layui-table text-left" data-mars="server.operateLog" data-container="#opData" data-template="opListTemp">
					<thead>
						<tr>
							<th>序号</th>
							<th>操作用户</th>
							<th>URL</th>
							<th>操作IP</th>
							<th>操作时间</th>
							<th>操作结果</th>
							<th>操作</th>
						</tr>
					</thead>
					<tbody id="opData">
					</tbody>
				</table>
					<script id="opListTemp" type="text/x-jsrender">
					{{for data}}
							<tr>
								<td>{{:#index+1}}</td>
								<td>{{:USERNAME}}</td>
								<td>{{:URL}}</td>
								<td>{{:IP}}</td>
								<td>{{:OPERATE_TIME}}</td>
								<td>{{:MSG}}</td>
								<td>
									<textarea style="display:none;">{{:PARAMS}}</textarea>
									<a href="javascript:void(0);" onclick="layer.alert($(this).prev().val());">参数</a>
									<a style="display:none;" href="javascript:void(0);" onclick="clearOperateLog('{{:LOG_ID}}')">删除</a>
								</td>
							</tr>
					{{/for}}					         
					</script>
		    </div>
		</form>

<script>
	$(function(){
		$("#easyform-log-list").render();
	});
	
	function clearOperateLog(id){
		ajax.remoteCall("${ctxPath}/servlet/serverInfo?action=delOperateLog",{logId:id},function(result) { 	
			if(result.state==1){
				layer.msg(result.msg,{icon: 1,time:800},function(){
					layer.closeAll();
					loadPage("logger/operate-log-list.jsp");
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
	
	function loginLogPage(){
		window.location.hash = "logger/login-log-list.jsp";
	}
	
	function appLogPage(){
		window.location.hash = "logger/app-log-list.jsp";
	}
	
</script>

