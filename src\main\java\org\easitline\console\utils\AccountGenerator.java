package org.easitline.console.utils;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class AccountGenerator {
	 // 账号字符集：8位小写字母
    private static final String ACCOUNT_CHARS = "abcdefghijklmnopqrstuvwxyz";

    // 密码字符集：各部分控制
    private static final String LOWERCASE = "abcdefghijklmnopqrstuvwxyz";
    private static final String UPPERCASE = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String DIGITS = "**********";
    private static final String SYMBOLS = "!@#$%&*";  // 常用符号，避免太多特殊字符

    private static final SecureRandom SECURE_RANDOM = new SecureRandom();

    /**
     * 从指定字符集中随机选择一个字符
     */
    private static char getRandomChar(String charSet) {
        return charSet.charAt(SECURE_RANDOM.nextInt(charSet.length()));
    }

    /**
     * 生成账号：8位小写字母
     */
    public static String generateAccount() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 8; i++) {
            sb.append(getRandomChar(ACCOUNT_CHARS));
        }
        return sb.toString();
    }

    /**
     * 生成安全密码：12位，至少包含一个小写、大写、数字、常用符号
     */
    public static String generateSecurePassword() {
        // 强制包含每种类型的一个字符
        char lower = getRandomChar(LOWERCASE);
        char upper = getRandomChar(UPPERCASE);
        char digit = getRandomChar(DIGITS);
        char symbol = getRandomChar(SYMBOLS);

        StringBuilder sb = new StringBuilder();
        sb.append(lower).append(upper).append(digit).append(symbol);

        // 剩下的8个字符从所有字符集中随机选取
        String allChars = LOWERCASE + UPPERCASE + DIGITS + SYMBOLS;
        for (int i = 4; i < 12; i++) {
            sb.append(getRandomChar(allChars));
        }

        // 打乱顺序
        List<Character> passwordChars = new ArrayList<>();
        for (char c : sb.toString().toCharArray()) {
            passwordChars.add(c);
        }
        Collections.shuffle(passwordChars, SECURE_RANDOM);

        // 构建最终密码
        StringBuilder password = new StringBuilder();
        for (Character c : passwordChars) {
            password.append(c);
        }

        return password.toString();
    }

    public static void main(String[] args) {
        String account = generateAccount();
        String password = generateSecurePassword();

        System.out.println("生成的账号: " + account);
        System.out.println("生成的密码: " + password);
    }
}
