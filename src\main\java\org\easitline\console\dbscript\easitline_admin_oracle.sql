

/*==============================================================*/
/* Table: EASI_OPERATOR_LOG                                             */
/*==============================================================*/
create table EASI_OPERATOR_LOG  (
   SERIAL_ID            VARCHAR2(32)   not null,
   USER_ID              VARCHAR2(32)   not null,
   USER_ACCT            VARCHAR2(50)   not null,
   OPR_TIME             VARCHAR2(19)      not null,
   IP_ADDR              VARCHAR2(32),
   OPR_TYPE             INTEGER,
   OPR_MODULE           VARCHAR2(100),
   OPR_CONTENT          VARCHAR2(2000),
   constraint  PK_EASI_OPERATOR_LOG primary key (SERIAL_ID)
);

/*==============================================================*/
/* Table: EASI_DEPT                                             */
/*==============================================================*/
create table EASI_DEPT 
(
   DEPT_ID              VARCHAR2(32)         not null,
   DEPT_CODE            VARCHAR2(90),
   DEPT_NAME            VARCHAR2(100),
   DEPT_PATH_NAME       varchar2(500),
   P_DEPT_CODE          VARCHAR2(90),
   DEPT_TYPE            VARCHAR2(30),
   LINK_MAN             VARCHAR2(30),
   LINK_MOBILE          VARCHAR2(255),
   QRCODE_URL           VARCHAR2(255),
   RESERVE1             VARCHAR2(100),
   RESERVE2             VARCHAR2(100),
   RESERVE3             VARCHAR2(100),
   RESERVE4             VARCHAR2(100),
   RESERVE5             VARCHAR2(100),
   RESERVE6             VARCHAR2(100),
   RESERVE7             VARCHAR2(100),
   RESERVE8             VARCHAR2(100),
   RESERVE9             VARCHAR2(100),
   RESERVE10            VARCHAR2(100),
   IDX_ORDER            INTEGER,
   constraint PK_EASI_DEPT primary key (DEPT_ID)
);

comment on column EASI_DEPT.DEPT_ID is
'部门ID，唯一';

comment on column EASI_DEPT.DEPT_CODE is
'部门代码，格式：001002003....';

comment on column EASI_DEPT.DEPT_NAME is
'部门名称';

comment on column EASI_DEPT.P_DEPT_CODE is
'父部门代码';

comment on column EASI_DEPT.DEPT_TYPE is
'部门类型，参考：EASI_DEPT_TYPE';

comment on column EASI_DEPT.RESERVE1 is
'保留字段1';

comment on column EASI_DEPT.RESERVE2 is
'保留字段2';

comment on column EASI_DEPT.RESERVE3 is
'保留字段3';

comment on column EASI_DEPT.RESERVE4 is
'保留字段4';

comment on column EASI_DEPT.RESERVE5 is
'保留字段5';

comment on column EASI_DEPT.RESERVE6 is
'保留字段6';

comment on column EASI_DEPT.RESERVE7 is
'保留字段7';

comment on column EASI_DEPT.RESERVE8 is
'保留字段8';

comment on column EASI_DEPT.RESERVE9 is
'保留字段9';

comment on column EASI_DEPT.RESERVE10 is
'保留字段10';

/*==============================================================*/
/* Index: IDX_FD_DEPT_NAME                                      */
/*==============================================================*/
create index IDX_FD_DEPT_NAME on EASI_DEPT (
   DEPT_NAME DESC
);

/*==============================================================*/
/* Index: IDX_FD_DEPT_CODE                                      */
/*==============================================================*/
create index IDX_FD_DEPT_CODE on EASI_DEPT (
   DEPT_CODE ASC
);

/*==============================================================*/
/* Table: EASI_DEPT_REF                                         */
/*==============================================================*/
create table EASI_DEPT_REF 
(
   F_DEPT_TYPE          VARCHAR2(20)         not null,
   N_DEPT_TYPE          VARCHAR2(20)         not null,
   constraint PK_EASI_DEPT_REF primary key (F_DEPT_TYPE, N_DEPT_TYPE)
);

comment on table EASI_DEPT_REF is
'定义部门之间的关系,  例如：全国中心-省中心， 通常由应用来定义';

comment on column EASI_DEPT_REF.F_DEPT_TYPE is
'上级部门';

comment on column EASI_DEPT_REF.N_DEPT_TYPE is
'下级部门';

/*==============================================================*/
/* Table: EASI_DEPT_RES                                         */
/*==============================================================*/
create table EASI_DEPT_RES 
(
   DEPT_ID              VARCHAR2(32)         not null,
   RES_ID               VARCHAR2(100)        not null,
   constraint PK_EASI_DEPT_RES primary key (DEPT_ID, RES_ID)
);

comment on column EASI_DEPT_RES.DEPT_ID is
'部门ID，唯一';

comment on column EASI_DEPT_RES.RES_ID is
'资源ID，全局唯一，由应用本身定义';

/*==============================================================*/
/* Table: EASI_DEPT_TYPE                                        */
/*==============================================================*/
create table EASI_DEPT_TYPE 
(
   DEPT_TYPE            VARCHAR2(20)         not null,
   DEPT_TYPE_NAME       VARCHAR2(50)         not null,
   NODE_TYPE            INTEGER,
   constraint PK_EASI_DEPT_TYPE primary key (DEPT_TYPE)
);

comment on table EASI_DEPT_TYPE is
'用于定义部门的类型，缺省：部门

';

comment on column EASI_DEPT_TYPE.DEPT_TYPE is
'部门类型';

comment on column EASI_DEPT_TYPE.DEPT_TYPE_NAME is
'部门类型名称';

comment on column EASI_DEPT_TYPE.NODE_TYPE is
'节点类型，1：节点　　２：单位　　３：部门';


INSERT INTO EASI_DEPT_TYPE(DEPT_TYPE,DEPT_TYPE_NAME,NODE_TYPE) VALUES('SYS_SF','省份',1)
;

INSERT INTO EASI_DEPT_TYPE(DEPT_TYPE,DEPT_TYPE_NAME,NODE_TYPE) VALUES('SYS_DS','地市',1)
;

INSERT INTO EASI_DEPT_TYPE(DEPT_TYPE,DEPT_TYPE_NAME,NODE_TYPE) VALUES('SYS_ZQ','镇区',1)
;

INSERT INTO EASI_DEPT_TYPE(DEPT_TYPE,DEPT_TYPE_NAME,NODE_TYPE) VALUES('SYS_JD','街道',1)
;

INSERT INTO EASI_DEPT_TYPE(DEPT_TYPE,DEPT_TYPE_NAME,NODE_TYPE) VALUES('SYS_DW','单位',2)
;

INSERT INTO EASI_DEPT_TYPE(DEPT_TYPE,DEPT_TYPE_NAME,NODE_TYPE) VALUES('SYS_BM','部门',3)
;

/*==============================================================*/
/* Table: EASI_DEPT_USER                                        */
/*==============================================================*/
create table EASI_DEPT_USER 
(
   DEPT_ID              VARCHAR2(32)         not null,
   USER_ID              VARCHAR2(32)         not null,
   POST_TYPE            INTEGER,
   IDX_ORDER            INT,
   constraint PK_EASI_DEPT_USER primary key (DEPT_ID, USER_ID)
);


comment on column EASI_DEPT_USER.DEPT_ID is
'部门ID';

comment on column EASI_DEPT_USER.USER_ID is
'用户账号，全局唯一';

/*==============================================================*/
/* Table: EASI_FILES                                            */
/*==============================================================*/
create table EASI_FILES 
(
   FILE_ID              VARCHAR2(32)         not null,
   FS_TYPE              INTEGER,
   GROUP_ID             VARCHAR2(32)         not null,
   FILE_NAME            VARCHAR2(255),
   FILE_TYPE            VARCHAR2(50),
   FILE_SIZE            NUMBER(12),
   FILE_MD5             VARCHAR2(50),
   CONTENT_TYPE         VARCHAR2(255),
   FILE_URI             VARCHAR2(255),
   LOGIN_ACCESS         INTEGER,
   CREATOR              VARCHAR2(32),
   CREATOR_NAME         VARCHAR2(50),
   CREATE_TIME          VARCHAR2(19),
   DATA1                VARCHAR2(255),
   DATA2                VARCHAR2(255),
   DATA3                VARCHAR2(255),
   DATA4                VARCHAR2(255),
   DATA5                VARCHAR2(255),
   constraint PK_EASI_FILES primary key (FILE_ID)
);

comment on table EASI_FILES is
'文件存储';

comment on column EASI_FILES.FILE_ID is
'文件ID';

comment on column EASI_FILES.FS_TYPE is
'1：本地存储  2:阿里云OSS存储';

comment on column EASI_FILES.GROUP_ID is
'分组，0：不指定分组下的缺省分组 其它：代表其他的应用分组';

comment on column EASI_FILES.FILE_NAME is
'文件名';

comment on column EASI_FILES.FILE_TYPE is
'文件类型，根据文件的后缀进行判定';

comment on column EASI_FILES.FILE_SIZE is
'文件大小';

comment on column EASI_FILES.FILE_MD5 is
'文件MD5签名';

comment on column EASI_FILES.CONTENT_TYPE is
'CONTENT_TYPE，主要用于HTTP的头部分对文件的类型描述';

comment on column EASI_FILES.FILE_URI is
'文件访问URL';

comment on column EASI_FILES.LOGIN_ACCESS is
'1：是 必须登录才能访问  0：可以公网访问';

comment on column EASI_FILES.CREATOR is
'创建人';

comment on column EASI_FILES.CREATOR_NAME is
'创建人姓名';

comment on column EASI_FILES.CREATE_TIME is
'创建时间';

/*==============================================================*/
/* Index: IDX_EASI_FILES_1                                      */
/*==============================================================*/
create index IDX_EASI_FILES_1 on EASI_FILES (
   CREATE_TIME ASC
);

/*==============================================================*/
/* Index: IDX_EASI_FILES_2                                      */
/*==============================================================*/
create index IDX_EASI_FILES_2 on EASI_FILES (
   FILE_NAME ASC
);

/*==============================================================*/
/* Table: EASI_JOB_LOG                                          */
/*==============================================================*/
create table EASI_JOB_LOG 
(
   DATE_KEY             VARCHAR2(32)         not null,
   JOB_ID               VARCHAR2(32)         not null,
   DATE_ID              INTEGER,
   JOB_STATE            INTEGER,
   FIRST_TIME           VARCHAR2(19),
   LAST_TIME            VARCHAR2(19),
   EXEC_COUNT           INTEGER,
   EXCEPTION_INFO       CLOB,
   constraint PK_EASI_JOB_LOG primary key (DATE_KEY, JOB_ID)
);

comment on table EASI_JOB_LOG is
'调度日志';

comment on column EASI_JOB_LOG.DATE_KEY is
'date_key';

comment on column EASI_JOB_LOG.JOB_ID is
'调度ID';

comment on column EASI_JOB_LOG.DATE_ID is
'调度日期，格式：YYYYMMDD';

comment on column EASI_JOB_LOG.JOB_STATE is
'调度状态，0:调度中  1：成功  -1：失败';

comment on column EASI_JOB_LOG.FIRST_TIME is
'首次调度时间';

comment on column EASI_JOB_LOG.LAST_TIME is
'最后执行时间';

comment on column EASI_JOB_LOG.EXEC_COUNT is
'执行次数';

comment on column EASI_JOB_LOG.EXCEPTION_INFO is
'异常信息';

/*==============================================================*/
/* Table: EASI_JOB_STEP                                         */
/*==============================================================*/
create table EASI_JOB_STEP 
(
   JOB_ID               VARCHAR2(32)         not null,
   JOB_NAME             VARCHAR2(100),
   CRON                 VARCHAR2(20),
   JOB_TYPE             INTEGER,
   SERVER_NAME          VARCHAR2(50),
   JOB_STATE            INTEGER,
   SERVICE_ID           VARCHAR2(50),
   SERVICE_NAME         VARCHAR2(100),
   BEGIN_DATE           VARCHAR2(10),
   END_DATE             VARCHAR2(10),
   BEGIN_TIME           VARCHAR2(5),
   END_TIME             VARCHAR2(5),
   NEXT_TIME            VARCHAR2(19),
   OK_COUNT             INTEGER,
   FAIL_COUNT           INTEGER,
   LAST_OK_TIME         VARCHAR2(32),
   LAST_FAIL_TIME       VARCHAR2(32),
   DEPEND_JOBS          VARCHAR2(200),
   constraint PK_EASI_JOB_STEP primary key (JOB_ID)
);

comment on table EASI_JOB_STEP is
'调度任务';

comment on column EASI_JOB_STEP.JOB_ID is
'调度ID';

comment on column EASI_JOB_STEP.JOB_NAME is
'调度名称';

comment on column EASI_JOB_STEP.CRON is
'CRON';

comment on column EASI_JOB_STEP.JOB_TYPE is
'度类型 1:串行 2:并行';

comment on column EASI_JOB_STEP.SERVER_NAME is
'执行MARS服务名称，为空时，缺省采用当前的mars进行调度';

comment on column EASI_JOB_STEP.JOB_STATE is
'状态  1:正常 0:暂停';

comment on column EASI_JOB_STEP.SERVICE_ID is
'ESB服务ID';

comment on column EASI_JOB_STEP.SERVICE_NAME is
'ESB服务名';

comment on column EASI_JOB_STEP.BEGIN_DATE is
'任务开始日期';
   
comment on column EASI_JOB_STEP.END_DATE is
'任务结束日期';

comment on column EASI_JOB_STEP.BEGIN_TIME is
'任务开始时间';

comment on column EASI_JOB_STEP.END_TIME is
'任务结束时间';

comment on column EASI_JOB_STEP.NEXT_TIME is
'下次执行时间';

comment on column EASI_JOB_STEP.OK_COUNT is
'成功执行次数';

comment on column EASI_JOB_STEP.FAIL_COUNT is
'失败次数';

comment on column EASI_JOB_STEP.LAST_OK_TIME is
'最后执行成功时间';

comment on column EASI_JOB_STEP.LAST_FAIL_TIME is
'最后执行失败时间';

comment on column EASI_JOB_STEP.DEPEND_JOBS is
'依赖调度，多个依赖之间采用"|"分隔，调度必须是月和日的调度才生效';

/*==============================================================*/
/* Table: EASI_LABEL                                            */
/*==============================================================*/
create table EASI_LABEL 
(
   LABEL_ID             VARCHAR2(32)         not null,
   LABEL_NAME           VARCHAR2(30)         not null,
   OWNER_DEPT_ID        VARCHAR2(32),
   constraint PK_EASI_LABEL primary key (LABEL_ID)
);

comment on column EASI_LABEL.LABEL_ID is
'标签ID';

comment on column EASI_LABEL.LABEL_NAME is
'标签名称';

/*==============================================================*/
/* Table: EASI_LABEL_DEPT                                       */
/*==============================================================*/
create table EASI_LABEL_DEPT 
(
   LABEL_ID             VARCHAR2(32)         not null,
   DEPT_ID              VARCHAR2(32)         not null,
   IDX_ORDER            INTEGER,
   constraint PK_EASI_LABEL_DEPT primary key (LABEL_ID, DEPT_ID)
);

comment on column EASI_LABEL_DEPT.LABEL_ID is
'标签ID';

comment on column EASI_LABEL_DEPT.DEPT_ID is
'部门ID';

comment on column EASI_LABEL_DEPT.IDX_ORDER is
'索引排序号';

/*==============================================================*/
/* Table: EASI_LABEL_ROLE                                       */
/*==============================================================*/
create table EASI_LABEL_ROLE 
(
   LABEL_ID             VARCHAR2(32)         not null,
   ROLE_ID              VARCHAR2(32)         not null,
   IDX_ORDER            INTEGER,
   constraint PK_USER_LABEL_ROLE primary key (LABEL_ID, ROLE_ID)
);

comment on column EASI_LABEL_ROLE.LABEL_ID is
'标签ID';

comment on column EASI_LABEL_ROLE.ROLE_ID is
'角色ID';

comment on column EASI_LABEL_ROLE.IDX_ORDER is
'索引排序号';

/*==============================================================*/
/* Table: EASI_LABEL_USER                                       */
/*==============================================================*/
create table EASI_LABEL_USER 
(
   USER_ID              VARCHAR2(32)         not null,
   LABEL_ID             VARCHAR2(32)         not null,
   IDX_ORDER            INTEGER,
   constraint PK_EASI_LABEL_USER primary key (USER_ID, LABEL_ID)
);

comment on column EASI_LABEL_USER.USER_ID is
'用户账号，全局唯一';

comment on column EASI_LABEL_USER.LABEL_ID is
'标签ID';

comment on column EASI_LABEL_USER.IDX_ORDER is
'索引';

/*==============================================================*/
/* Table: EASI_LOGIN_LOG                                        */
/*==============================================================*/
create table EASI_LOGIN_LOG 
(
   LOGIN_LOG_ID         VARCHAR2(32)         not null ,
   USER_ID              VARCHAR2(32)         not null,
   USER_ACCT            VARCHAR2(50)         not null,
   LOGIN_TYPE           INTEGER,
   SESSION_ID           VARCHAR2(50),
   LOGIN_TIME           VARCHAR2(19),
   LOGIN_STATE          INTEGER,
   LOGIN_IP             VARCHAR2(30),
   LOGIN_CLIENT_TYPE    INTEGER,
   LOGOUT_TIME          VARCHAR2(19),
   REMARK               VARCHAR2(200),
   constraint PK_EASI_LOGIN_LOG primary key (LOGIN_LOG_ID)
);

comment on column EASI_LOGIN_LOG.USER_ID is
'用户账号，全局唯一';

comment on column EASI_LOGIN_LOG.LOGIN_TYPE is
'1：系统账号（人工设定）
2：手机号码
3：邮箱
4：微信扫描登陆';

comment on column EASI_LOGIN_LOG.LOGIN_TIME is
'格式:YYYY-MM-DD HH:MM:DD';

comment on column EASI_LOGIN_LOG.LOGIN_STATE is
'1：成功 0：失败';

/*==============================================================*/
/* Index: IDX_EASI_LOGIN_LOG_1                                  */
/*==============================================================*/
create index IDX_EASI_LOGIN_LOG_1 on EASI_LOGIN_LOG (
   USER_ID ASC
);

/*==============================================================*/
/* Index: IDX_EASI_LOGIN_LOG_2                                  */
/*==============================================================*/
create index IDX_EASI_LOGIN_LOG_2 on EASI_LOGIN_LOG (
   USER_ACCT ASC
);

/*==============================================================*/
/* Index: IDX_EASI_LOGIN_LOG_3                                  */
/*==============================================================*/
create index IDX_EASI_LOGIN_LOG_3 on EASI_LOGIN_LOG (
   LOGIN_TIME ASC
);

/*==============================================================*/
/* Table: EASI_PC_DICT                                          */
/*==============================================================*/
create table EASI_PC_DICT 
(
   DICT_TYPE_ID         VARCHAR2(50)         not null,
   DICT_ID              VARCHAR2(50)         not null,
   DICT_NAME            VARCHAR2(100),
   DICT_DESC            VARCHAR2(100),
   P_DICT_ID            VARCHAR2(50),
   IDX_ORDER            INTEGER,
   constraint PK_EASI_PC_DICT primary key (DICT_TYPE_ID, DICT_ID)
);

comment on table EASI_PC_DICT is
'字典表';

comment on column EASI_PC_DICT.DICT_TYPE_ID is
'字典类型';

comment on column EASI_PC_DICT.DICT_ID is
'字典ID';

comment on column EASI_PC_DICT.DICT_NAME is
'字典名称';

comment on column EASI_PC_DICT.DICT_DESC is
'字典描述';

comment on column EASI_PC_DICT.P_DICT_ID is
'父字典ID';

comment on column EASI_PC_DICT.IDX_ORDER is
'排序';

create table EASI_PC_DICT_TYPE 
(
   DICT_TYPE_ID         VARCHAR2(50)         not null,
   DICT_TYPE_NAME       VARCHAR2(50),
   IDX_ORDER            INTEGER,
   DICT_RES             VARCHAR2(50),
   APP_ID               VARCHAR2(50),
   constraint PK_EASI_PC_DICT_TYPE primary key (DICT_TYPE_ID)
);

comment on table EASI_PC_DICT_TYPE is
'字典表类型';

comment on column EASI_PC_DICT_TYPE.DICT_TYPE_ID is
'字典类型ID';

comment on column EASI_PC_DICT_TYPE.DICT_TYPE_NAME is
'字典类型名称';

comment on column EASI_PC_DICT_TYPE.IDX_ORDER is
'排序';

comment on column EASI_PC_DICT_TYPE.DICT_RES is
'字典来源，sys:系统　　app:应用　user:用户自定义';


/*==============================================================*/
/* Table: EASI_POSI_TYPE                                        */
/*==============================================================*/
create table EASI_POSI_TYPE 
(
   POST_TYPE            INTEGER              not null,
   POST_TYPE_NAME       VARCHAR2(20),
   constraint PK_EASI_POSI_TYPE primary key (POST_TYPE)
);

comment on table EASI_POSI_TYPE is
'1：负责人
9：员工';


insert into EASI_POSI_TYPE(POST_TYPE,POST_TYPE_NAME)   values(1,'负责人')
;
insert into EASI_POSI_TYPE(POST_TYPE,POST_TYPE_NAME)   values(9,'员工')
;

/*==============================================================*/
/* Table: EASI_RES                                              */
/*==============================================================*/
create table EASI_RES 
(
   RES_ID               VARCHAR2(100)        not null,
   RES_NAME             VARCHAR2(50)         not null,
   P_RES_ID             VARCHAR2(20)         not null,
   RES_URL              VARCHAR2(200),
   RES_ICON             VARCHAR2(50),
   RES_TYPE             INTEGER              default 1,
   RES_TARGET           VARCHAR2(20),
   RES_STATE            INTEGER              default 0,
   IDX_ORDER            INTEGER,
   APP_ID               VARCHAR2(30),
   PORTAL               varchar2(30),
   constraint PK_EASI_RES primary key (RES_ID)
);

comment on table EASI_RES is
'对系统的应用、菜单、功能进行进行统一的定义。';

comment on column EASI_RES.RES_ID is
'资源ID，全局唯一，由应用本身定义';

comment on column EASI_RES.RES_NAME is
'资源名称';

comment on column EASI_RES.P_RES_ID is
'父资源ID';

comment on column EASI_RES.RES_URL is
'URL访问地址';

comment on column EASI_RES.RES_TYPE is
'资源类型，1：应用 ，2：菜单，3：功能，9: 其它';

comment on column EASI_RES.RES_TARGET is
'打开方式，_blank 在新的窗口打开，否则在portal中打开';

comment on column EASI_RES.RES_STATE is
'状态，0:正常，缺省 ，1：暂停';

comment on column EASI_RES.IDX_ORDER is
'排序';



/*==============================================================*/
/* Index: SYS_FUNLIST_MENU_IDX                                  */
/*==============================================================*/
create index SYS_FUNLIST_MENU_IDX on EASI_RES (
   RES_TYPE ASC
);


insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN', '系统管理', '2000', null, 2, null, 0, 99, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_USER', '用户管理', 'EASI_ADMIN', '/easitline-admin/servlet/user?action=List', 2, '_default', 0, 11, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_ROLE', '角色管理', 'EASI_ADMIN', '/easitline-admin/servlet/role?action=List', 2, '_default', 0, 21, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_FRAMEWORK', '组织架构管理', 'EASI_ADMIN', '/easitline-admin/servlet/framework?action=main', 2, '_default', 0, 31, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_RES', '资源管理', 'EASI_ADMIN', '/easitline-admin/servlet/resource?action=main', 2, '_default', 0, 41, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_LABEL', '标签管理', 'EASI_ADMIN', '/easitline-admin/servlet/label?action=main', 2, '_default', 0, 32, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_PC_DICT', '数据字典管理', 'EASI_ADMIN', '/easitline-admin/servlet/dict?action=dictTypeList', 2, '_default', 0, 61, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_LOG_LOGIN', '登录日志', 'EASI_ADMIN', '/easitline-admin/pages/log/login-log.jsp', 2, '_default', 0, 71, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_LOG_OPERATOR', '操作日志', 'EASI_ADMIN', '/easitline-admin/pages/log/operator-log.jsp', 2, '_default', 0, 81, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_USER_NEW', '添加用户', 'EASI_ADMIN_USER', null, 3, '_default', 0, 12, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_ROLE_NEW', '添加角色', 'EASI_ADMIN_ROLE', null, 3, '_default', 0, 22, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_FRAMEWORK_USER_BDEL', '批量删除成员分管部门', 'EASI_ADMIN_FRAMEWORK', null, 3, '_default', 0, 37, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_ROLE_DEL', '删除角色', 'EASI_ADMIN_ROLE', null, 3, '_default', 0, 23, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_ROLE_MOD', '修改角色', 'EASI_ADMIN_ROLE', null, 3, '_default', 0, 24, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_ROLE_DIS_USER', '分配用户', 'EASI_ADMIN_ROLE', null, 3, '_default', 0, 25, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_ROLE_DIS_RES', '分配资源', 'EASI_ADMIN_ROLE', null, 3, '_default', 0, 26, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_FRAMEWORK_DEPT_NEW', '新增部门', 'EASI_ADMIN_FRAMEWORK', null, 3, '_default', 0, 32, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_FRAMEWORK_DEPT_MOD', '修改部门信息', 'EASI_ADMIN_FRAMEWORK', null, 3, '_default', 0, 33, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_FRAMEWORK_DEPT_DEL', '删除部门', 'EASI_ADMIN_FRAMEWORK', null, 3, '_default', 0, 34, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_LABEL_LABEL_NEW', '新增标签', 'EASI_ADMIN_LABEL', null, 3, '_default', 0, 52, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_LABEL_MEMBER_NEW', '添加标签成员', 'EASI_ADMIN_LABEL', null, 3, '_default', 0, 53, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_LABEL_MEMBER_DEL', '移除标签成员', 'EASI_ADMIN_LABEL', null, 3, '_default', 0, 54, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_RES_RES_MOD', '修改资源', 'EASI_ADMIN_RES', null, 3, '_default', 0, 62, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_RES_RES_DEL', '删除资源', 'EASI_ADMIN_RES', null, 3, '_default', 0, 63, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_FRAMEWORK_DUSER_NEW', '新增部门成员', 'EASI_ADMIN_FRAMEWORK', null, 3, '_default', 0, 35, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_FRAMEWORK_DUSER_DEL', '移除部门成员', 'EASI_ADMIN_FRAMEWORK', null, 3, '_default', 0, 36, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_FRAMEWORK_UDEPT_NEW', '添加成员分管部门', 'EASI_ADMIN_FRAMEWORK', null, 3, '_default', 0, 38, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_FRAMEWORK_UDEPT_DEL', '移除成员分管部门', 'EASI_ADMIN_FRAMEWORK', null, 3, '_default', 0, 39, 'easitline-admin', '2000');



/*==============================================================*/
/* Table: EASI_ROLE                                             */
/*==============================================================*/
create table EASI_ROLE 
(
   ROLE_ID              VARCHAR2(32)         not null,
   ROLE_NAME            VARCHAR2(30)         not null,
   ROLE_TYPE            INTEGER              not null,
   APP_ID               VARCHAR2(30),
   ROLE_DESC            VARCHAR2(100),
   OWNER_DEPT_ID        VARCHAR2(32),
   IDX_ORDER            INTEGER              default 99,
   constraint PK_EASI_ROLE primary key (ROLE_ID)
);

comment on column EASI_ROLE.ROLE_ID is
'角色ID';

comment on column EASI_ROLE.ROLE_NAME is
'角色名称';

comment on column EASI_ROLE.ROLE_TYPE is
'角色类型： 0:系统角色，SYS_开头，不可更改，1：业务角色，由应用根据需要定义，2：自定义角色，由系统提供维护';

comment on column EASI_ROLE.APP_ID is
'所属应用，这里指针对应用的自定义业务角色来确定';

comment on column EASI_ROLE.ROLE_DESC is
'角色描述';

insert into EASI_ROLE(ROLE_ID,ROLE_NAME,ROLE_TYPE,APP_ID ,ROLE_DESC,OWNER_DEPT_ID,IDX_ORDER) values('SYS_SUPER_USER','超级管理员',0,'SYSTEM','系统超级用户,拥有最高权限!','0',1)
;

insert into EASI_ROLE(ROLE_ID,ROLE_NAME,ROLE_TYPE,APP_ID ,ROLE_DESC,OWNER_DEPT_ID,IDX_ORDER) values('SYS_NODE_ADMIN','单位管理员',0,'SYSTEM','维护单位人员及部门信息!','0',2)
;


/*==============================================================*/
/* Table: EASI_ROLE_RES                                         */
/*==============================================================*/
create table EASI_ROLE_RES 
(
   ROLE_ID              VARCHAR2(32)         not null,
   RES_ID               VARCHAR2(100)        not null,
   constraint PK_EASI_ROLE_RES primary key (ROLE_ID, RES_ID)
);

comment on column EASI_ROLE_RES.ROLE_ID is
'角色ID';

comment on column EASI_ROLE_RES.RES_ID is
'资源ID，全局唯一，由应用本身定义';

/*==============================================================*/
/* Table: EASI_ROLE_USER                                        */
/*==============================================================*/
create table EASI_ROLE_USER 
(
   ROLE_ID              VARCHAR2(32)         not null,
   USER_ID              VARCHAR2(32)         not null,
   CREATE_TIME          VARCHAR2(19) ,
   constraint PK_EASI_ROLE_USER primary key (ROLE_ID, USER_ID)
);

comment on column EASI_ROLE_USER.ROLE_ID is
'角色ID';

comment on column EASI_ROLE_USER.USER_ID is
'用户账号，全局唯一';


insert into EASI_ROLE_USER(ROLE_ID,USER_ID) values ('SYS_SUPER_USER','9432e4c15d464bfcbb8078f28187f17e')
;

/*==============================================================*/
/* Table: EASI_USER                                             */
/*==============================================================*/
create table EASI_USER 
(
   USER_ID              VARCHAR2(32)         not null,
   USERNAME             VARCHAR2(20)         not null,
   ROOT_ID              VARCHAR2(30),
   NIKE_NAME            VARCHAR2(20),
   SEX                  VARCHAR2(10),
   EDU                  VARCHAR2(10),
   BORN                 VARCHAR2(10),
   NATION               varchar2(20),
   MAJOR                varchar2(20),
   UNIVERSITY           varchar2(50),
   OFFICE               VARCHAR2(50),
   MOBILE               VARCHAR2(50),
   EMAIL                VARCHAR2(50),
   ADDR                 VARCHAR2(200),
   IDCARD               VARCHAR2(20),
   EMAIL_SIGN           INTEGER              default 0,
   SMS_SIGN             INTEGER              default 0,
   STATE                INTEGER              default 0,
   IP_RANGE             VARCHAR2(200),
   PIC_URL              VARCHAR2(100),
   LOGIN_TIME           VARCHAR2(20),
   LOGIN_IP             VARCHAR2(20),
   PLAFORM              VARCHAR2(50),
   IMEI                 VARCHAR2(50),
   QRCODE_URL           VARCHAR2(255) ,
   DEPTS                VARCHAR2(100),
   ROLES                VARCHAR2(100),
   DESCRIPTION          VARCHAR2(500),
   DATA1                VARCHAR2(100),
   DATA2                VARCHAR2(100),
   DATA3                VARCHAR2(100),
   DATA4                VARCHAR2(100),
   DATA5                VARCHAR2(100),
   DATA6                VARCHAR2(100),
   DATA7                VARCHAR2(100),
   DATA8                VARCHAR2(100),
   DATA9                VARCHAR2(100),
   DATA10               VARCHAR2(100),
   APP_ID               VARCHAR2(100),
   OPEN_ID              VARCHAR2(100),
   CREATE_TIME          VARCHAR2(19),
   CREATE_MAN           VARCHAR2(50),
   UPDATE_TIME          VARCHAR2(19),
   UPDATE_MAN           VARCHAR2(50),
   OWNER_DEPT_ID        VARCHAR2(32),
   constraint PK_EASI_USER primary key (USER_ID)
);

comment on column EASI_USER.USER_ID is
'用户账号，全局唯一';

comment on column EASI_USER.USERNAME is
'用户名';

comment on column EASI_USER.NIKE_NAME is
'昵称';

comment on column EASI_USER.SEX is
'性别：男 女  保密';

comment on column EASI_USER.EDU is
'学历';

comment on column EASI_USER.BORN is
'出生年月，格式：yyyy-mm-dd';

comment on column EASI_USER.OFFICE is
'办公电话';

comment on column EASI_USER.MOBILE is
'手机';

comment on column EASI_USER.EMAIL is
'EMAIL';

comment on column EASI_USER.ADDR is
'地址';

comment on column EASI_USER.IDCARD is
'身份证';

comment on column EASI_USER.EMAIL_SIGN is
'EMAIL通知标志， 1：通知 0：不通知';

comment on column EASI_USER.SMS_SIGN is
'短信通知标志，1：通知  0：不通知';

comment on column EASI_USER.STATE is
'用户状态，0：正常 1：暂停 2：锁定 9：删除';

comment on column EASI_USER.PIC_URL is
'个人图片保存路径';

comment on column EASI_USER.PLAFORM is
'JSON格式,格式：{PC:1,MOBILE:1}';

comment on column EASI_USER.DEPTS is
'所属部门，描述当前用户所在的组织架构位置';

comment on column EASI_USER.ROLES is
'系统冗余，用于显示不用维护，系统自动根据用户角色的关系进行填写。';

comment on column EASI_USER.DESCRIPTION is
'用户备注';

comment on column EASI_USER.DATA1 is
'保留字段1';

comment on column EASI_USER.DATA2 is
'保留字段2';

comment on column EASI_USER.DATA3 is
'保留字段3';

comment on column EASI_USER.DATA4 is
'保留字段4';

comment on column EASI_USER.DATA5 is
'保留字段5';

comment on column EASI_USER.DATA6 is
'保留字段6';

comment on column EASI_USER.DATA7 is
'保留字段7';

comment on column EASI_USER.DATA8 is
'保留字段8';

comment on column EASI_USER.DATA9 is
'保留字段9';

comment on column EASI_USER.DATA10 is
'保留字段10';

/*==============================================================*/
/* Index: IDX_EASI_USER_1                                       */
/*==============================================================*/
create index IDX_EASI_USER_1 on EASI_USER (
   OPEN_ID ASC,
   APP_ID ASC
);

/*==============================================================*/
/* Index: IDX_EASI_USER_2                                       */
/*==============================================================*/
create index IDX_EASI_USER_2 on EASI_USER (
   USERNAME ASC
);


insert  into EASI_USER(USER_ID,USERNAME) values('9432e4c15d464bfcbb8078f28187f17e','系统管理员')
;



/*==============================================================*/
/* Table: EASI_USER_LOGIN                                       */
/*==============================================================*/
create table EASI_USER_LOGIN 
(
   USER_ID              VARCHAR2(32),
   USER_ACCT            VARCHAR2(50)         not null,
   USER_PWD             VARCHAR2(32),
   LOGIN_TYPE           INTEGER              not null,
   ACCT_STATE           INTEGER,
   LOCK_STATE           INTEGER,
   LAST_LOGIN_TIME      VARCHAR2(19),
   TRY_LOGIN_TIMES      INTEGER,
   LOGIN_IP_ADDR        VARCHAR2(200),
   constraint PK_EASI_USER_LOGIN primary key (USER_ACCT, LOGIN_TYPE)
);

comment on column EASI_USER_LOGIN.USER_ID is
'用户账号，全局唯一';

comment on column EASI_USER_LOGIN.USER_PWD is
'MD5加密';

comment on column EASI_USER_LOGIN.LOGIN_TYPE is
'1：系统账号（人工设定）
2：手机号码
3：邮箱
4：微信扫描登陆';

comment on column EASI_USER_LOGIN.ACCT_STATE is
'1：正常   0：暂停使用';

comment on column EASI_USER_LOGIN.LOCK_STATE is
'0：正常  1：已锁定';

/*==============================================================*/
/* Table: EASI_USER_LOGIN_SECURITY                                       */
/*==============================================================*/
create table EASI_USER_LOGIN_SECURITY
(
   USER_ACCT                VARCHAR2(50),
   HISTORY_PWDS             VARCHAR2(500),
   LOGIN_TIMES          INTEGER,
   LAST_UPDATE_PWD_TIME     VARCHAR2(19),
   LOGIN_FAILED_TIME        VARCHAR2(19),
   LOGIN_FAILED_COUNT       INTEGER,
   LOGIN_LOCK           INTEGER,
   LOCK_TIME            VARCHAR2(19),
   LOGIN_MSG            VARCHAR2(255),
   UPDATE_TIME            VARCHAR2(19),
   primary key (USER_ACCT)
);

/*==============================================================*/
/* Index: IDX_EASI_USER_LOGIN_1                                 */
/*==============================================================*/
create index IDX_EASI_USER_LOGIN_1 on EASI_USER_LOGIN (
   USER_ID ASC
);

/*==============================================================*/
/* Table: EASI_USER_RES                                         */
/*==============================================================*/
create table EASI_USER_RES 
(
   USER_ID              VARCHAR2(32)         not null,
   RES_ID               VARCHAR2(100)        not null,
   constraint PK_EASI_USER_RES primary key (USER_ID, RES_ID)
);

comment on column EASI_USER_RES.USER_ID is
'用户账号，全局唯一';

comment on column EASI_USER_RES.RES_ID is
'资源ID，全局唯一，由应用本身定义';


insert into EASI_USER_LOGIN(USER_ID,USER_ACCT,USER_PWD,LOGIN_TYPE,ACCT_STATE,LOCK_STATE)   values('9432e4c15d464bfcbb8078f28187f17e','admin','21232F297A57A5A743894A0E4A801FC3',1,0,0)
;




/*==============================================================*/
/* Table: EASI_USER_DEPT                                        */
/*==============================================================*/
create table EASI_USER_DEPT 
(
   USER_ID              VARCHAR2(32)         not null,
   DEPT_ID              VARCHAR2(32)         not null,
   constraint PK_EASI_USER_DEPT primary key (USER_ID, DEPT_ID)
);

comment on table EASI_USER_DEPT is
'用户分管部门表';

comment on column EASI_USER_DEPT.USER_ID is
'用户ID';

comment on column EASI_USER_DEPT.DEPT_ID is
'部门ID';




/*==============================================================*/
/* Index: 用户资源视图                                                                                       */
/*==============================================================*/

create or replace view V_EASI_USER_RES as 

select t2.USER_ID ,t1.RES_ID,t1.RES_NAME,t1.P_RES_ID,t1.RES_URL,t1.RES_TYPE,t1.RES_TARGET,t1.RES_STATE,
t1.IDX_ORDER,t1.APP_ID ,t1.PORTAL,t1.RES_ICON from EASI_RES t1  , EASI_USER_RES  t2 where t1.RES_ID = t2.RES_ID 

union

select distinct  t3.USER_ID ,t1.RES_ID,t1.RES_NAME,t1.P_RES_ID,t1.RES_URL,t1.RES_TYPE,t1.RES_TARGET,t1.RES_STATE,
t1.IDX_ORDER,t1.APP_ID ,t1.PORTAL,t1.RES_ICON from EASI_RES t1 ,EASI_ROLE_RES t2 , EASI_ROLE_USER t3  where t1.RES_ID = t2.RES_ID and 
t2.ROLE_ID = t3.ROLE_ID
;


/*==============================================================*/
/* 用户视图                                                                                              */
/*==============================================================*/
create or replace view V_EASI_USER as
select
       t1.USER_ID,t1.USERNAME,t1.NIKE_NAME,t1.SEX,t1.EDU,t1.BORN,t1.OFFICE,t1.MOBILE,t1.EMAIL,t1.ADDR,t1.IDCARD,t1.EMAIL_SIGN,t1.SMS_SIGN,t1.STATE,t1.IP_RANGE,t1.PIC_URL,t1.LOGIN_TIME,t1.LOGIN_IP,t1.PLAFORM,t1.DEPTS,t1.ROLES,t1.DESCRIPTION,t1.DATA1,t1.DATA2,t1.DATA3,t1.DATA4,t1.DATA5,t1.DATA6,t1.DATA7,t1.DATA8,t1.DATA9,t1.DATA10,t1.APP_ID,t1.OPEN_ID,t1.CREATE_TIME,t1.CREATE_MAN,t1.QRCODE_URL,t1.IMEI,t2.USER_ACCT
from
       EASI_USER t1 left join EASI_USER_LOGIN t2 on t1.USER_ID = t2.USER_ID and t2.LOGIN_TYPE = 1;


/*==============================================================*/
/* 部门用户表                                                                                                        */
/*==============================================================*/

create view V_EASI_DEPT_USER as 
select
  t3.DEPT_ID,t3.DEPT_CODE,t3.DEPT_NAME,t3.P_DEPT_CODE,t3.DEPT_TYPE, t2.POST_TYPE, t4.POST_TYPE_NAME,t1.USER_ID,t1.USERNAME,
  t1.STATE,t1.MOBILE,t1.ROLES ,t1.OPEN_ID,t1.APP_ID,t2.IDX_ORDER
from
  EASI_USER  t1 ,EASI_DEPT_USER  t2  left join EASI_POSI_TYPE t4 on t2.POST_TYPE = t4.POST_TYPE
  ,EASI_DEPT  t3
where
  t2.USER_ID = t1.USER_ID and t2.DEPT_ID = t3.DEPT_ID;
;

/*==============================================================*/
/* 标签用户 视图                                                                                               */
/*==============================================================*/
create view V_EASI_LABEL_USER  as 
select s2.LABEL_ID ,s2.LABEL_NAME ,s1.* from   V_EASI_USER s1  ,(
select  t1.LABEL_ID ,t1.LABEL_NAME ,t2.USER_ID  from EASI_LABEL t1 , EASI_LABEL_USER t2   where t1.LABEL_ID = t2.LABEL_ID
union 
select  t1.LABEL_ID ,t1.LABEL_NAME,t3.USER_ID  from EASI_LABEL t1 , EASI_LABEL_DEPT t2, EASI_DEPT_USER t3  where t1.LABEL_ID = t2.LABEL_ID and t2.DEPT_ID = t3.DEPT_ID  
union 
select t1.LABEL_ID ,t1.LABEL_NAME,t3.USER_ID  from EASI_LABEL t1 , EASI_LABEL_ROLE t2, EASI_ROLE_USER t3   where t1.LABEL_ID = t2.LABEL_ID and t2.ROLE_ID = t3.ROLE_ID  
) s2  where s1.USER_ID = s2.USER_ID
;


 

