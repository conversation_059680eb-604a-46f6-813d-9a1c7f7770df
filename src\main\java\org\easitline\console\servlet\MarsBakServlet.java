package org.easitline.console.servlet;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.console.base.ConsoleBaseServlet;
import org.easitline.console.service.MarsBakService;

import com.alibaba.fastjson.JSONObject;

@WebServlet("/servlet/marsbak")
public class MarsBakServlet extends ConsoleBaseServlet {
	private static final long serialVersionUID = 1L;

	public EasyResult actionForBakWar(){
		JSONObject jsonObject = this.getJSONObject();
		String warName = jsonObject.getString("warName");
		MarsBakService.getService().bakWar(warName);
		return EasyResult.ok();
	}
	
	public EasyResult actionForBakDb(){
		MarsBakService.getService().bakConfDb();
		return EasyResult.ok();
	}

}
