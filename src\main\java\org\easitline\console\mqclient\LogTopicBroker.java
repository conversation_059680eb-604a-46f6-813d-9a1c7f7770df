package org.easitline.console.mqclient;

import org.apache.log4j.Logger;
import org.easitline.common.core.activemq.Broker;

import org.easitline.common.core.activemq.BrokerFactory;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;


public class LogTopicBroker {
	
	public final static String BROKER_NAME = "mars-log-broker";
	
	private static Logger logger = LogEngine.getLogger("easitline-console","mars-log");
	
	private static  Broker broker = null;
	
	/**
	 * 获得用户所对应的MARS。
	 * @param agentId
	 * @return
	 */
	public  static void sendMessage(String logString){
		if(broker==null){
			broker =  BrokerFactory.getProducerTopicBroker(ServerContext.getMQAddr(),BROKER_NAME,ServerContext.getMQUser(),ServerContext.getMQPassword());
		}
		try {
			if(broker != null){
				broker.sendMessage(logString);
				logger.info("["+BROKER_NAME+"] >> "+logString);
			}
		} catch (Exception ex) {
			logger.error("send log to "+BROKER_NAME+" error,cause:"+ex.getMessage()+"log->"+logString,ex);
			if(broker != null) broker.close();
			broker = null;
		}
	}
	
	public static void close(){
		try {
			if(broker!=null) broker.close();
		} catch (Exception e) {
			// TODO: handle exception
		}
	}
	
}
