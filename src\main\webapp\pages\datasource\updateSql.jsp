<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://www.easitline.org/easitline-taglib" prefix="EasyTag"%>
<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<title>更新SQL</title>
		 <LINK rel="Shortcut Icon" href="${ctxPath}/favicon.ico" />
		<link rel="stylesheet" href="${staticPath}/lib/layui/css/layui.css">
		<link rel="stylesheet" href="${ctxPath}/static/css/site.css">
		<script type="text/javascript" src="${staticPath}/js/jquery.min.js"></script> 
		<style type="text/css">
		 body{padding: 20px;margin: 20px;}
		 .layui-input-block{width: 80%}
		</style>
	</head>
<body>
	<form id="easyform" class="layui-form">
		<div>
			<blockquote class="layui-elem-quote">
				执行SQL语句,符号;分割执行待执行的SQL
			</blockquote>
		
			<fieldset class="layui-elem-field layui-field-title">
				<legend id="sqlMsg">请输入合法的SQL语句</legend>
			</fieldset>
				<div class="layui-form-item layui-form-text">
				    <label class="layui-form-label">SQL</label>
				     <div class="layui-input-block">
				     	 <textarea id="sqlContent" placeholder="请输入SQL" class="layui-textarea"></textarea>
				     </div>
			     </div>
			     <div class="layui-form-item">
				    <div class="layui-input-block">
				      <button class="layui-btn" type="button" onclick="excute()">立即提交</button>
				      <button type="reset" class="layui-btn layui-btn-primary">重置</button>
				    </div>
				  </div>
		</div>
	</form>
	<script type="text/javascript" src="${staticPath}/js/jsrender.min.js"></script>
	<script type="text/javascript" src="${staticPath}/lib/layer/layer.js"></script>
	<script type="text/javascript" src="${staticPath}/lib/layui/layui.js"></script>
	<script type="text/javascript" src="${staticPath}/js/easitline.core-2.0.0.js"></script>
	<script type="text/javascript" src="${staticPath}/js/requreLib.js"></script>
	<script type="text/javascript">
		function excute(){
			layer.confirm("确认是否要执行？",function(index){
				layer.close(index);
				var data={};
				data.sqlStr=$("#sqlContent").val();
				data.dsName='${param.dsName}';
				ajax.remoteCall("${ctxPath}/servlet/datasource?action=updateSql", data,function(result){  
					if(result.state==1){
						$("#sqlMsg").html(result.msg);
					}else{
						$("#sqlMsg").html(result.msg);
					}
				});
			});
		}
	</script>
</body>
</html>
