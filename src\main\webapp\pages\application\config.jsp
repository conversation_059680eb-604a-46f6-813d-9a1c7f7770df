<%@page pageEncoding="UTF-8"%>
<style>
	#updatedut{
		display: none;
	}
	.layui-form-item .layui-input-inline{
		width: 500px;
		max-width: 800px;
		min-width: 200px;
	}
	label.layui-form-item{
		width: 200px;
	}
	.layui-form-label{width: 350px;}
</style>
<div>
	<blockquote class="layui-elem-quote"  onclick="getAppConfig()">
		动态修改应用的配置信息，修改完成后立即生效无需重启，注意：修改应用的配置信息有可能直接影响应用的正常运行，请谨慎修改！
		<br><span>配置支持类型：string,select,radio,textarea,json</span>
	</blockquote>

	<fieldset class="layui-elem-field layui-field-title">
		<legend>修改应用配置参数（${param.appId}）</legend>
	</fieldset>

	<form class="layui-form" id="easyform" autocomplete="off">
	  <input type="hidden" id="appId" name="appId" value="${param.appId}" >
	  <input type="hidden" id="nodeId" name="nodeId">
	  <input type="hidden" id="nodeUrl" name="nodeUrl">
	  <input type="hidden" id="currentKey" name="currentKey">
	  <div class="layui-form-item layui-hide">
		     <label class="layui-form-label" style="color: #e73e2d;">选择节点</label>
		     <div class="layui-input-inline">
		      <select lay-filter="nodeSelect" class="layui-select" data-mars="NodeDao.dict">
		        <option value="0" selected="selected">当前节点</option>
		      </select>
		    </div>
	  </div>
	  <div id="dataList" data-mars="app.appConfigs"></div>
	  <br><br><br>
	  <script id="list-template" type="text/x-jsrender">
			{{for list}}
			{{if P_ITEM_KEY =='1'}}
				<fieldset class="layui-elem-field layui-field-title">
					<legend style="text-align:center;">{{:ITEM_NAME}} {{if DESCRIPTION}}_{{:DESCRIPTION}}{{/if}}</legend>
				</fieldset>	
			{{else}}			
			  <div class="layui-form-item" title="{{:ITEM_NAME}}">
			    <label class="layui-form-label" title="{{:ITEM_NAME}}">{{:ITEM_NAME}}/{{:ITEM_KEY}}</label>
			    <div class="layui-input-inline">
				   {{if ITEM_TYPE=='radio'&&JSON_STRING}}
			      		{{call: fn='renderRadio'}}
				    {{else ITEM_TYPE=='select'&&JSON_STRING}}
			      		{{call: fn='renderSelect'}}
				    {{else ITEM_TYPE=='number'}}
						<input type="number" name="conf_{{:ITEM_KEY}}" value="{{:ITEM_VALUE}}" placeholder="{{:ITEM_NAME}}" class="layui-input">
				    {{else ITEM_TYPE=='textarea'}}
						<textarea style="min-height:60px;" name="conf_{{:ITEM_KEY}}" placeholder="{{:ITEM_NAME}}" class="layui-textarea">{{:ITEM_VALUE}}</textarea>
				    {{else ITEM_TYPE=='json'}}
						<textarea style="min-height:60px;" readonly="readonly" name="conf_{{:ITEM_KEY}}" placeholder="{{:ITEM_NAME}}" class="layui-textarea">{{:ITEM_VALUE}}</textarea>
				    	<button type="button" class="layui-btn layui-btn-xs layui-btn-warm mt-5" onclick="jsonConf(this)">配置json</button>
					{{else}}
						<input type="text" name="conf_{{:ITEM_KEY}}" value="{{:ITEM_VALUE}}" placeholder="{{:ITEM_NAME}}" class="layui-input">
				    {{/if}}


			    </div>
			    <div class="layui-form-mid layui-word-aux">{{:ITEM_DESC}}</div>
			  </div>
			{{/if}}
		{{/for}}
	  </script>
	  <div class="layui-form-item">
	    <div class="layui-input-block" style="margin-left: 380px;">
	      <button type="button" id="updatedut" class="layui-btn layui-btn-small layui-btn-normal" onclick="">保存修改</button>
	    </div>
	  </div>
	</form>
	
	
</div>
<div id="smsContent" style="display: none;">
	<table class="layui-table" style="margin: 15px;width: 400px">
		<tr>
			<td>电话号码</td>
			<td>
				<span id="mobile"></span>
				<button class="layui-btn layui-btn-xs" style="float: right;" type="button" onclick="getSmsCode(this)">获取验证码</button>
			</td>
		</tr>
		<tr>
			<td>验证码</td>
			<td>
				<input name="smsCode" id="smsCode" type="number" class="layui-input" />
			</td>
		</tr>
	</table>
</div>
<script type="text/javascript">
	$(function(){
		$("#easyform").render({success:function(result){
			var appConfigs = result["app.appConfigs"];
    		if(result&&appConfigs&&appConfigs.data&&appConfigs.data.length>0){
    			$("#updatedut").show();
    		}else{
    			$("#updatedut").hide();
    		}
    		
    	 	layui.use('form', function(){
		         var layuiform = layui.form;
		         layuiform.render('select');
		         layuiform.render('radio'); 
		         
		         layuiform.on('select(nodeSelect)', function(data){
			         var nodeId = data.value;
		        	 $('#nodeId').val(nodeId);
		        	 var appId = '${param.appId}';
			         if(nodeId=='0'){
			        	 $('#nodeUrl').val(location.origin);
		 				 $('#currentKey').val(appConfigs.key);
			        	 getNodeAppConfigInfo(location.origin,appConfigs.key);
			        	 return;
			         }
		        	 ajax.remoteCall("${ctxPath}/webcall?action=NodeDao.record",{nodeId:nodeId},function(result) { 
	 					var url = result.data.NODE_URL;
	 					$('#nodeUrl').val(url);
	 					$('#currentKey').val(result.securityKey);
	 					getNodeAppConfigInfo(url,result.securityKey);
		 			});
					function getNodeAppConfigInfo(url,securityKey){
			        	 $.ajax({
			    			  url: url+"${ctxPath}/node/server?action=getAppConfig",
			    			  data: {nodeId:nodeId,appId:appId,key:securityKey},
			    			  dataType: 'jsonp',
			                  jsonp: "callbackFunc",
			                  jsonpCallback: "jsonpCallback",
			                  contentType: "application/x-www-form-urlencoded; charset=UTF-8",
			    			  timeout:2000,
			    			  success: function(rs){
			    				  if(rs.state==1){
			    					    var tmp = $.templates("#list-template");
										$('#dataList').html(tmp.render({list:rs.data}));
										layui.use('form', function(){
									         var layuiform = layui.form;
									         layuiform.render('select');
									         layuiform.render('radio'); 
									     });
			    				  }else{
			    					  layer.alert(rs.msg);
			    				  }
			    			  },
			    			  error:function(e){
									console.error(e);			    				  
			    				    alert(url+'网络不通');
			    			  },
			    			  before:function(){
			    				  
			    			  }
			    	     });
					}
		         });
		     });
    	}});
		
		//绑定点击测试连接的方法的单击事件
		$("#updatedut").click(function() {
			ajax.remoteCall('${ctxPath}/servlet/node?action=querySmsSwitch',{},function(result) {
				if(result.state==1){
					var data = result.data;
					if(data.smsSwitch=='1'){
						$('#mobile').text(data.mobile);
						popup.layerShow({id:'smsLayer',title:'短信验证',content:$('#smsContent'),offset:'20px',shade:false,shadeClose:false,area:['450px','250px'],btn:['验证','取消'],yes:function(){
							ajax.remoteCall('${ctxPath}/servlet/node?action=checkSms',{smsCode:$('#smsCode').val()},function(rs) {
								if(rs.state=='1'){
									doSubmit();
								}else{
									layer.msg(rs.msg,{icon:7});
								}
							});
							
						}});
					}else{
						layer.confirm('请谨慎操作，确认无误提交吗?',{icon:3,offset:'30px',title:'操作提醒'},function(){
							doSubmit();
						});
					}
					
				}
			});
			
		  function doSubmit(){
			$("#easyform input").each(function(){
				var t=$(this);
				var val=t.val();
				if(val.indexOf("{")>-1){
					t.val(val.replaceAll("'",'"'));
				}
			});
			var appId = '${param.appId}';
			var nodeUrl = $('#nodeUrl').val();
			var nodeId = $('#nodeId').val();
			var currentKey = $('#currentKey').val();
			
			if(nodeId==''||nodeId=='0'){
				var url = '${ctxPath}/servlet/application?action=updateConfig';
				var data = form.getJSONObject("easyform");
				ajax.remoteCall(url,data, function(result) {
					if(result.state == 1){
						layer.msg(result.msg,{icon: 1,time:800},function(){
							layer.closeAll();
							loadPage("application/list.jsp",{});
						});
					}else{
						layer.alert(result.msg);
					}
				});
			}else{
				 var url = nodeUrl+"${ctxPath}/node/server?action=updateConfig";
				 var postdata = form.getJSONObject("easyform");
				 $.ajax({
	    			  url: url,
	    			  data: {nodeId:nodeId,appId:appId,key:currentKey,data:JSON.stringify(postdata)},
	    			  dataType: 'jsonp',
	                  jsonp: "callbackFunc",
	                  jsonpCallback: "jsonpCallback",
	                  contentType: "application/x-www-form-urlencoded; charset=UTF-8",
	    			  timeout:5000,
	    			  success: function(rs){
	    				  if(rs.state==1){
	    					  layer.msg(rs.msg,{icon: 1,time:800},function(){
	  							 layer.closeAll();
	  						  });
	    				  }else{
	    					  layer.alert(rs.msg);
	    				  }
	    			  },
	    			  error:function(e){
						  console.error(e);			    				  
	    				  alert(url+'网络不通');
	    			  },
	    			  before:function(){
	    				  
	    			  }
	    	     });
			}
			  
		  }
			
		});
	});
	String.prototype.replaceAll = function(s1,s2){ 
		return this.replace(new RegExp(s1,"gm"),s2); 
	}
	function renderRadio(row){
		try{
			var v1=row['JSON_STRING'];
			var v2=row['ITEM_KEY'];
			var v3=row['ITEM_VALUE'];
			var v4=row['ITEM_NAME'];
			var html="";
			v1=v1.replaceAll("{","");
			v1=v1.replaceAll("}","");
			var array=v1.split(",");
			for(var index in array){
				var item=array[index];
				var itemArray=item.split(":");
				var val=itemArray[0];
				var checked="";
				if(val==v3)checked="checked";
				if(itemArray.length==1){
					html+="<input type='radio' name='conf_"+v2+"' title='"+itemArray[0]+"' "+checked+" value='"+itemArray[0]+"'/>";
				}
				if(itemArray.length==2){
					html+="<input type='radio' name='conf_"+v2+"' title='"+itemArray[1]+"' "+checked+" value='"+itemArray[0]+"'/>";
				}
			}
			return html;
		}catch(error){
			console.error(error);
	        return '<input type="number" name="conf_'+v2+'" value="'+v3+'" placeholder="'+v4+'" class="layui-input">';
		}
		
	}
   function renderSelect(row){
	   try{
			var v1=row['JSON_STRING'];
			var v2=row['ITEM_KEY'];
			var v3=row['ITEM_VALUE'];
			var v4=row['ITEM_NAME'];
			var html="<select name='conf_"+v2+"'>";
			v1=v1.replaceAll("{","");
			v1=v1.replaceAll("}","");
			var array=v1.split(",");
			for(var index in array){
				var item=array[index];
				var itemArray=item.split(":");
				var val=itemArray[0];
				var selected="";
				if(val==v3)selected="selected";
				if(itemArray.length==1){
					html+="<option "+selected+" value='"+itemArray[0]+"'>"+itemArray[0]+"</option>";
				}
				if(itemArray.length==2){
					html+="<option "+selected+" value='"+itemArray[0]+"'>"+itemArray[1]+"</option>";
				}
			}
			html+="</select>";
			return html;
		}catch(error){
			console.error(error);
	        return '<input type="number" name="conf_'+v2+'" value="'+v3+'" placeholder="'+v4+'" class="layui-input">';
		}
   }
   function getAppConfig(){
		ajax.remoteCall("${ctxPath}/servlet/application?action=getAppConfig",{appId:'${param.appId}'}, function(result) { 
	 		layer.alert(JSON.stringify(result));
		});
  }
   
	function getSmsCode(el){
		ajax.remoteCall('${ctxPath}/servlet/node?action=sendSms',{},function(rs) {
			if(rs.state=='1'){
				$(el).text('已发送');
				$(el).addClass('layui-btn-disabled');
				$(el).attr('disabled',true);
				layer.msg(rs.msg);
			}else{
				layer.msg(rs.msg);
			}
		});
	}
	
	function jsonConf(el){
		var jsonStr = $(el).prev().val();
		var html = [];
		var json = eval("("+jsonStr+")");
		html.push("<table id='param-config-json' class='layui-table'><thead><tr><th style='width:45%;'>键</th><th>值</th></tr></thead>");
		for(var key in json){
			html.push("<tr><td>"+key+"</td>");
			html.push("<td><input class='layui-input' name='"+key+"' value='"+json[key]+"'></td></tr>");
		}
		html.push("</table>");
		layer.open({id:'jsonConf',title:'修改配置',content:html.join(''),area:['500px','500px'],yes:function(index){
			var postdata = form.getJSONObject("#param-config-json");
			$(el).prev().val(JSON.stringify(postdata));
			layer.close(index);
		}});
	}
	
</script>

