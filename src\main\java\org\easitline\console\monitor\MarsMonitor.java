package org.easitline.console.monitor;

import org.easitline.common.core.log.LogEngine;
import org.easitline.console.listener.GlobalContextListener;

/**
 * 上报当前MARS接口的运行时信息到redis
 */
public class MarsMonitor implements Runnable {
	
	private static String logName = "console-alarm";
	
	private static  long timer = 0;
	
	public void run() {
		LogEngine.getLogger(logName).info("Start MarsMonitor...");
		//延时启动
		try {
			Thread.sleep(60*1000);
		} catch (Exception ex) {
			LogEngine.getLogger(logName).error(ex,ex);
		}
		
		while(GlobalContextListener.runState){
			//每3分钟检查一次
			if(System.currentTimeMillis() - timer < 180*1000){
				try {
					Thread.sleep(5*1000);
				} catch (Exception ex) {
					LogEngine.getLogger(logName).error(ex,ex);
				}
				continue;
			}
			timer = System.currentTimeMillis();
			try {
				LogEngine.getLogger(logName).info("执行Mars监控情况检查...");
				MarsStatService.getInstance().servcie();
			} catch (Exception ex) {
				LogEngine.getLogger(logName).error(ex,ex);
			}
			
		}
		
	}
}
