package org.easitline.console.vo;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.easitline.common.db.EasyRowMapper;

public class ApplicationHisRowMapper implements EasyRowMapper<ApplicationModel> {

	@Override
	public ApplicationModel mapRow(ResultSet rs, int rowNum) {
		ApplicationModel vo = new ApplicationModel();
		try {
			vo.setId(rs.getString("ID"));
			vo.setAppId(rs.getString("APP_ID"));
			vo.setAppName(rs.getString("APP_NAME"));
			vo.setAppVersion(rs.getString("APP_VERSION"));
			vo.setWarName(rs.getString("WAR_NAME"));
			vo.setDeployTime(rs.getString("DEPLOY_TIME"));
			vo.setAppFilePath(rs.getString("APP_FILE_PATH"));
		} catch (SQLException ex) {
			ex.printStackTrace();
		}
		return vo;
		
	}

}
