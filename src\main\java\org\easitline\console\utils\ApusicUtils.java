package org.easitline.console.utils;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.easitline.common.core.Globals;
import org.easitline.console.deploy.vo.AppContextInfo;

public class ApusicUtils {

	public static String deployDir() {
		return System.getProperty("deployDir", Globals.WEBAPPS_DIR);
	}
	
	public static List<AppContextInfo> getList(){
		List<AppContextInfo> list = new ArrayList<AppContextInfo>();
		File deployDir = new File(deployDir());
		File[] files = deployDir.listFiles();
		for(File file:files) {
			String name = file.getName();
			if(name.endsWith(".war")) {
				AppContextInfo appContextInfo = new AppContextInfo();
				appContextInfo.setContextPath(name);
				appContextInfo.setAppName(name);
				appContextInfo.setAppId(name);
				appContextInfo.setWarName(file.getAbsolutePath());
				appContextInfo.setReloadUrl(file.getAbsolutePath());
				appContextInfo.setStateName("未部署");
				
				
				Date date = new Date(file.lastModified());
		        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		        String formattedDate = sdf.format(date);
		        appContextInfo.setDeployTime(formattedDate);    
				
				File deployed = new File(file.getAbsolutePath()+"_deployed");
				if(deployed.exists()) {
					appContextInfo.setState(true);
					appContextInfo.setStateName("正常");
				}
				File deployFailed = new File(file.getAbsolutePath()+"_deployFailed");
				if(deployFailed.exists()) {
					appContextInfo.setState(false);
					appContextInfo.setStateName("异常");
				}
				list.add(appContextInfo);
			}
		}
		return list;
	}
}
