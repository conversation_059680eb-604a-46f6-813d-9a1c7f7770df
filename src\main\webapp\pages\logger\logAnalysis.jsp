<%@ page language="java" contentType="text/html; charset=UTF-8"  pageEncoding="UTF-8"%>

<h3 style="line-height: 1.5; margin: 0 0 1em;"><a id="_1"></a>一、部署失败</h3>
<ul style="list-style-type: disc; padding-left: 40px;">
<li style="line-height: 1.5; margin: 0 0 1em;">
<p style="line-height: 1.5; margin: 0 0 1em;"><strong>日志分析：</strong></p>
<ul style="list-style-type: disc; padding-left: 40px;">
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>catalina.2024-06-16.log</strong> 和 <strong>localhost.log</strong>：重点关注异常堆栈信息（如：java.lang.OutOfMemoryError、java.lang.ClassNotFoundException等），以及启动过程中出现的错误提示。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>logging.properties</strong>：确保日志配置正确，日志级别设置合理，以便捕获足够详细的信息。</li>
</ul>
</li>
<li style="line-height: 1.5; margin: 0 0 1em;">
<p style="line-height: 1.5; margin: 0 0 1em;"><strong>常见原因：</strong></p>
<ul style="list-style-type: disc; padding-left: 40px;">
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>配置错误：</strong> 检查server.xml、context.xml等配置文件，确保端口号、路径等配置正确。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>依赖缺失：</strong> 确认所有依赖的jar包已正确部署。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>权限问题：</strong> 确保Tomcat用户对日志目录、部署目录等具有写权限。</li>
</ul>
</li>
</ul>
<h3 style="line-height: 1.5; margin: 0 0 1em;"><a id="_12"></a>二、启动失败</h3>
<ul style="list-style-type: disc; padding-left: 40px;">
<li style="line-height: 1.5; margin: 0 0 1em;">
<p style="line-height: 1.5; margin: 0 0 1em;"><strong>日志分析：</strong></p>
<ul style="list-style-type: disc; padding-left: 40px;">
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>catalina.out</strong> 和 <strong>catalina.${date}.log</strong>：重点关注启动过程中出现的错误提示，如端口冲突、内存不足、类加载失败等。</li>
</ul>
</li>
<li style="line-height: 1.5; margin: 0 0 1em;">
<p style="line-height: 1.5; margin: 0 0 1em;"><strong>常见原因：</strong></p>
<ul style="list-style-type: disc; padding-left: 40px;">
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>端口冲突：</strong> 修改server.xml中Connector的port属性。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>内存不足：</strong> 增加JVM内存参数（-Xms、-Xmx），或优化代码减少内存占用。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>JDK路径错误：</strong> 检查JAVA_HOME环境变量设置。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>权限问题：</strong> 确保MARS目录及子目录具有正确的权限。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>配置文件错误：</strong> 仔细检查配置文件(<a href="http://catalina.sh">catalina.sh</a>,server.xml)语法和配置项。</li>
</ul>
</li>
</ul>
<h3 style="line-height: 1.5; margin: 0 0 1em;"><a id="_24"></a>三、宕机异常退出</h3>
<ul style="list-style-type: disc; padding-left: 40px;">
<li style="line-height: 1.5; margin: 0 0 1em;">
<p style="line-height: 1.5; margin: 0 0 1em;"><strong>日志分析：</strong></p>
<ul style="list-style-type: disc; padding-left: 40px;">
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>catalina.out</strong>、<strong>localhost.log</strong>、<strong>catalina.log</strong>：重点关注异常堆栈信息，以及发生宕机时的系统状态。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>gc.log</strong>：分析GC日志，判断是否为内存泄漏或GC频繁导致。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>hs_err_pid.log</strong>：分析JVM错误日志，查找核心转储信息。</li>
</ul>
</li>
<li style="line-height: 1.5; margin: 0 0 1em;">
<p style="line-height: 1.5; margin: 0 0 1em;"><strong>常见原因：</strong></p>
<ul style="list-style-type: disc; padding-left: 40px;">
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>内存溢出：</strong> 增加JVM内存参数，优化代码，使用内存分析工具定位内存泄漏。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>线程死锁：</strong> 使用jstack命令生成线程转储，分析死锁情况。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>系统资源耗尽：</strong> 检查CPU、磁盘IO等系统资源使用情况。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>代码Bug：</strong> 仔细检查代码，修复导致异常的Bug。</li>
</ul>
</li>
</ul>
<h3 style="line-height: 1.5; margin: 0 0 1em;"><a id="_37"></a>四、服务不可访问</h3>
<ul style="list-style-type: disc; padding-left: 40px;">
<li style="line-height: 1.5; margin: 0 0 1em;">
<p style="line-height: 1.5; margin: 0 0 1em;"><strong>日志分析：</strong></p>
<ul style="list-style-type: disc; padding-left: 40px;">
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>catalina.out</strong>、<strong>localhost.log</strong>：检查是否有异常信息。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>jstack日志</strong>：分析线程状态，查看是否有线程阻塞或死锁。</li>
</ul>
</li>
<li style="line-height: 1.5; margin: 0 0 1em;">
<p style="line-height: 1.5; margin: 0 0 1em;"><strong>常见原因：</strong></p>
<ul style="list-style-type: disc; padding-left: 40px;">
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>连接数限制：</strong> 增加Tomcat的maxThreads参数。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>线程池耗尽：</strong> 调整线程池配置。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>网络问题：</strong> 检查网络连接是否正常。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>防火墙限制：</strong> 检查防火墙设置。</li>
</ul>
</li>
</ul>
<h3 style="line-height: 1.5; margin: 0 0 1em;"><a id="_49"></a>五、功能异常</h3>
<ul style="list-style-type: disc; padding-left: 40px;">
<li style="line-height: 1.5; margin: 0 0 1em;">
<p style="line-height: 1.5; margin: 0 0 1em;"><strong>日志分析：</strong></p>
<ul style="list-style-type: disc; padding-left: 40px;">
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>jdbc-error.log</strong>、<strong>catalina-error.log</strong>、<strong>catalina.out</strong>：检查数据库连接、SQL执行等异常。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>浏览器控制台</strong>：查看JavaScript错误、网络请求异常。</li>
</ul>
</li>
<li style="line-height: 1.5; margin: 0 0 1em;">
<p style="line-height: 1.5; margin: 0 0 1em;"><strong>常见原因：</strong></p>
<ul style="list-style-type: disc; padding-left: 40px;">
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>代码逻辑错误：</strong> 仔细检查代码逻辑。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>数据库问题：</strong> 检查数据库连接、SQL语句是否正确。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>配置错误：</strong> 检查配置文件是否正确。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>外部系统依赖问题：</strong> 检查依赖的外部系统是否正常。</li>
</ul>
</li>
</ul>
<h3 style="line-height: 1.5; margin: 0 0 1em;"><a id="_61"></a>总结</h3>
<ul style="list-style-type: disc; padding-left: 40px;">
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>系统化分析：</strong> 根据问题现象，有针对性地分析相关日志。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>工具辅助：</strong> 使用jstack、jmap等工具辅助分析。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>排除法：</strong> 通过排除法逐步缩小问题范围。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>经验积累：</strong> 总结常见问题，建立问题库。</li>
</ul>
<p style="line-height: 1.5; margin: 0 0 1em;"><strong>建议：</strong></p>
<ul style="list-style-type: disc; padding-left: 40px;">
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>加强日志管理：</strong> 配置详细的日志，便于问题排查。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>定期监控：</strong> 定期监控系统运行状态，及时发现潜在问题。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>建立问题库：</strong> 记录常见问题及解决方案，方便快速查找。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><strong>优化代码：</strong> 提高代码质量，减少故障发生。</li>
</ul>
<h2 style="line-height: 1.5; margin: 0 0 1em;"><a id="JDK_jps_jstack_jmap_77"></a>JDK工具 jps, jstack, jmap,</h2>
<ul style="list-style-type: disc; padding-left: 40px;">
<li style="line-height: 1.5; margin: 0 0 1em;"><code>jps</code>：快速查看当前系统中的Java进程。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><code>jstack</code>：分析线程问题，如死锁或挂起。脚本dump_threads.sh，工具fastthread。</li>
<li style="line-height: 1.5; margin: 0 0 1em;"><code>jmap</code>：内存分析，生成堆内存快照，检查内存使用情况。脚本dump_heap.sh，工具JProfiler。</li>
</ul>

