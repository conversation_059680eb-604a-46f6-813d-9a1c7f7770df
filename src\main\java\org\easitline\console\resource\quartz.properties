# Default Properties file for use by StdSchedulerFactory
# to create a Quartz Scheduler Instance, if a different
# properties file is not explicitly specified.
#

org.quartz.scheduler.instanceName: DefaultQuartzScheduler
org.quartz.scheduler.rmi.export: false
org.quartz.scheduler.rmi.proxy: false
org.quartz.scheduler.wrapJobExecutionInUserTransaction: false

org.quartz.threadPool.class: org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadCount: 10
org.quartz.threadPool.threadPriority: 5
org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread: true

org.quartz.jobStore.misfireThreshold: 1000

org.quartz.scheduler.jmx.export: true

#org.quartz.jobStore.class: org.quartz.simpl.RAMJobStore

# for cluster
#org.quartz.jobStore.tablePrefix = EASY_JOB_QRTZ_
#org.quartz.scheduler.instanceId: AUTO
#org.quartz.jobStore.class: org.quartz.impl.jdbcjobstore.JobStoreTX
#org.quartz.jobStore.isClustered: true
#org.quartz.jobStore.clusterCheckinInterval: 1000
