<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
	 <fieldset class="layui-elem-field layui-field-title">
			<legend>升级日志</legend>
	  </fieldset>
		<form id="easyform-log-list">
			<div style="padding:0 15px" id="appHisList">
				<table class="layui-table text-center" data-mars="app.hisList" data-container="#histData" data-template="hisListTemp">
					<thead>
						<tr>
							<th>序号</th>
							<th style="text-align:left">应用名称</th>
							<th style="text-align:left">部署时间</th>
							<th style="text-align:left">版本号</th>
							<th style="text-align:left">WAR名称</th>
							<th>WAR大小</th>
							<th>版本描述</th>
							<th style="display:none;">操作</th>
						</tr>
					</thead>
					<tbody id="histData">
					</tbody>
				</table>
				<script id="hisListTemp" type="text/x-jsrender">
					{{for list}}
							<tr>
								<td>{{:#index+1}}</td>
								<td style="text-align:left">{{:APP_NAME}}</td>
								<td style="text-align:left">{{:DEPLOY_TIME}}</td>
								<td style="text-align:left">{{:APP_VERSION}}</td>
								<td style="text-align:left">{{:WAR_NAME}}</td>
								<td>{{:WAR_SIZE}}</td>
								<td style="text-align:left" title="{{:APP_VERSION_DESC}}">
										<a href="javascript:void(0)" onclick="showDescContent($(this));">{{call:APP_VERSION_DESC fn='showDesc'}}</a>
										<div style="display:none"><textarea style="height:250px" class="layui-textarea">{{:APP_VERSION_DESC}}</textarea></div>
								</td>
								<td style="display:none;">
									<a href="javascript:switchVersion('{{:ID}}');">切换版本</a>
								</td>
							</tr>
					{{/for}}					         
				</script>
		    </div>
		</form>

<script>

	$(function(){
		$("#easyform-log-list").render();
	});
	
	function showDescContent(obj){
		layer.open({area:['500px','400px'],content:obj.next().html()});
	}
	//切换版本
	function switchVersion(id){
		layer.confirm("确定切换此版本吗？",function(confIndex){
			layer.close(confIndex);
    		ajax.remoteCall("${ctxPath}/servlet/application?action=switchVersion",{id:id},function(result) { 
    			layer.msg(result.msg,{icon: 1,time:800},function(){
					layer.closeAll();
					loadPage("application/list.jsp",{});
				});
   			});
		});
	}
	function showDesc(str){
		if(str==''){return ''};
		return cutText(str,50);
	}
	
</script>

