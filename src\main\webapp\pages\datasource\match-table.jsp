<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://www.easitline.org/easitline-taglib" prefix="EasyTag"%>
<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<title>脚本缺失匹配</title>
		 <LINK rel="Shortcut Icon" href="${ctxPath}/favicon.ico" />
		<link rel="stylesheet" href="${staticPath}/lib/layui/css/layui.css">
		<link rel="stylesheet" href="${ctxPath}/static/css/site.css">
		<script type="text/javascript" src="${staticPath}/js/jquery.min.js"></script> 
		<style type="text/css">
			 body{padding: 10px 30px;margin: 10px 30px;}
			 .layui-input-block{width: 100%;}
			 .layui-textarea{height: 130px;}
			 .layui-input-block{margin-left: 0px;margin-top: 12px;}
			 .layui-form-select{display: inline-block;}
		</style>
	</head>
<body>
	<form id="easyform" class="layui-form">
		 <input type="hidden" id="tabType"/>
		
		 <fieldset class="layui-elem-field site-demo-button" style="margin-top: 10px;">
			  <legend>SQL工具</legend>
			  <div style="padding: 15px;">
			  
			      <select class="name" style="width: 180px;" id="fileList" lay-search>
			      		<option value="">请选择</option>
			      </select>
				  <button style="margin-left: 30px;display: inline-block;" class="layui-btn" type="button" onclick="matchFn()">脚本匹配</button>
				  <button id="upload-btn" style="margin-left: 30px;display: inline-block;" class="layui-btn layui-btn-normal" type="button" onclick="importSql()">导入脚步</button>
			  </div>
		 </fieldset>
		  <table id="cust-list" lay-filter="cust-list"></table>
	</form>
	<script type="text/javascript" src="${staticPath}/js/jsrender.min.js"></script>
	<script type="text/javascript" src="${staticPath}/lib/layer/layer.js"></script>
	<script type="text/javascript" src="${staticPath}/lib/layui/layui.js"></script>
	<script type="text/javascript" src="${staticPath}/js/easitline.core-2.0.0.js"></script>
	<script type="text/javascript" src="${staticPath}/js/requreLib.js"></script>
	<script type="text/javascript">
	
    var dbType='${param.dbType}';
    var pwd=sessionStorage.getItem("c_pwd")||'';
    
    $(function(){
    	loadFile();
    });
    function loadFile(){
    	ajax.remoteCall("${ctxPath}/servlet/datasource?action=querySchemFile",{dsName:'${param.dsName}'},function(result){ 
    		if(result.data){
    			layui.use('form',function(){
    				var form=layui.form;
		    		render.renderSelect($("#fileList"),result.data);
					form.render();    				
    			});
    		}else{
    			layer.msg("未找到脚本版本文件");
    		}
	    	reqExcute();
		});
    }
    function matchFn(){
    	var checkStatus = table.checkStatus('cust-list');
		var checkList=checkStatus.data;
		var dbs=[];
		if(checkList.length!=1){
			layer.msg("一次只能选一个数据库匹配。");
			return;
		}
    	var  dbName= checkList[0].DATABASE || checkList[0].USERNAME;
    	var  path = $("#fileList").val();
    	var index= layer.msg('后台匹配处理中,耐心等候...', {icon: 16 ,shade: 0.01,time:0,area:'250px'});
    	if(path){
	    	ajax.remoteCall("${ctxPath}/servlet/datasource?action=matchTable",{dsName:'${param.dsName}',dbName:dbName,path:path},function(result){  
	    		console.log(result);
				layer.close(index);
	    		layer.open({content:"<div style='margin:15px;text-align: center;'>缺失"+result.data.tableNames.length+"张表，"+result.data.tableCols.length+"张表缺失字段<br><br><textarea class='layui-textarea' style='height:400px'>"+JSON.stringify(result.data)+"</textarea></div>",type:1,area:['500px','500px']});
	    	},{loading:false});
    	}

    }
   		  layui.use('upload', function(){
		 	  var upload = layui.upload;
			  var uploadInst = upload.render({
			    elem: '#upload-btn' //绑定元素
			    ,url: '${ctxPath}/servlet/application?action=uploadSqlFile' //上传接口
			    ,number: 1
			    ,accept: 'file'
			    ,exts:'json'
			    ,size:1024*50
			    ,field: 'appFile'//设定文件域的字段名
			    ,data: {}
			    ,done: function(res, index, upload){
				    if(res&&res.state==1){
				    	layer.msg(res.msg,{icon: 1,time:800},function(){
							layer.closeAll();
							loadFile();
						}); 
					}else{
						layer.alert(res.msg,{icon: 5});
					}
			    },before:function(){
			    	
			    }
			    ,error: function(res, index, upload){
			    	layer.alert("上传文件请求异常！",{icon: 5});
			    }
 	      });
   	  });
   		  
	function reqExcute(){
		if(pwd){
			doExcute();
		}else{
		 layer.prompt({title:"输入密码",formType:1},function(value, index, elem){
			layer.close(index);
			pwd=value;
			sessionStorage.setItem("c_pwd",pwd);
			doExcute();
		 });
		}
   }
   function doExcute(){
		var sqlStr="show databases";
		if(dbType=='MySql'){
			sqlStr = "show databases";
		}else{
			sqlStr = "SELECT * FROM DBA_USERS";
		}
		doSelect(sqlStr);
	}
	
	var table;
	function doSelect(sqlContent){
		var data={dsName:'${param.dsName}',sqlStr:sqlContent,maxRow:100,pwd:pwd};
		ajax.remoteCall("${ctxPath}/servlet/datasource?action=querySql", data,function(result){  
			if(result.state==1){
				var msg=""+result.msg+"结果行数："+result.data.row+",执行时间："+result.data.time+"";
				layer.msg(msg,{time:3000});
				if(result.data){
					var firstData=result.data.data[0];
					var cols=[];
					var i=2;
					cols[0]={type:'numbers'};
					cols[1]={type:'checkbox'};
					for(var key in firstData){
						cols[i]={'field':key,'title':key,sort:true};
						i++
					}
					 layui.use(['table'], function(){
						 table = layui.table;
						  table.render({
						      elem: '#cust-list'
						      ,cols: [cols],
						      page:true,
						      limit:30,
						      height:500,
						      cellMinWidth:100,
						      limits:[10,15,30,20,50],
						      data:result.data.data
						  });
						  
				    }); 
				}
			}else if(result.state==403){
				pwd="";
				layer.alert(result.msg);
			}else{
				var msg="<span style='color:red'>"+result.msg+"</span>";
				layer.msg(msg);
			}
		},{then:function(result){
			
		}});
	}
	 $(function(){
		 
	 });
</script>
</body>
</html>
