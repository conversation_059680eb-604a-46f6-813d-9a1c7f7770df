package org.easitline.console.dao;

import java.io.File;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.console.base.AppDaoContext;
import org.easitline.console.command.Self;
import org.easitline.console.service.ConfigCryptorService;
import org.easitline.console.utils.AesUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sun.management.OperatingSystemMXBean;

@WebObject(name="server")
public class ServerConfigDao extends AppDaoContext {
	
	
	@WebControl(name="javaTheads",type=Types.LIST)
	public JSONObject javaTheads(){
		List<JSONObject> list = new ArrayList<>();
		ThreadGroup group = Thread.currentThread().getThreadGroup();
		while (group.getParent() != null) {
		    group = group.getParent();
		}
		int slackSize = group.activeCount() * 2;
		Thread[] slackThreads = new Thread[slackSize];
		int actualSize = group.enumerate(slackThreads);
		Thread[] actualThreads = new Thread[actualSize];
		System.arraycopy(slackThreads, 0, actualThreads, 0, actualSize);
		info("Threads size is " + actualThreads.length, null);
		for (Thread thread : actualThreads) {
		    JSONObject object = new JSONObject();
		    object.put("id", thread.getId());
		    String name = thread.getName();
		    object.put("name", name);
		    if (name.startsWith("Thread-") || name.startsWith("http-")) {
		        continue;
		    }
		    StackTraceElement[] stackTrace = thread.getStackTrace();
		    if (stackTrace != null && stackTrace.length > 0) {
		        object.put("class", stackTrace[stackTrace.length - 1].getClassName());
		   } else {
		       object.put("class", "Unknown");
		   }
		   ThreadGroup cgroup = thread.getThreadGroup();
		   if(cgroup != null){
		      object.put("group", cgroup.getName());
		   }else{
		      object.put("group", "Unknown");
		   }
		   object.put("isDaemon", thread.isDaemon());
		   object.put("priority", thread.getPriority());
		   object.put("state", thread.getState().toString());
		   object.put("isAlive", thread.isAlive());
		   object.put("isInterrupted",thread.isInterrupted());
		   object.put("isBackground",thread.isDaemon()&&!thread.isInterrupted());
		   list.add(object);
		}
        return getJsonResult(list);
	}
	
	 public static List<JSONObject> collectThreadData() {
	        List<JSONObject> list = new ArrayList<>();
	        ThreadGroup group = Thread.currentThread().getThreadGroup();
	        ThreadGroup topGroup = findTopThreadGroup(group);
	        int slackSize = calculateSlackSize(topGroup);
	        Thread[] slackThreads = new Thread[slackSize];
	        int actualSize = topGroup.enumerate(slackThreads);
	        Thread[] atualThreads = new Thread[actualSize];
	        System.arraycopy(slackThreads, 0, atualThreads, 0, actualSize);
	        for (Thread thread : atualThreads) {
	            JSONObject object = new JSONObject();
	            object.put("id", thread.getId());
	            String name = thread.getName();
	            object.put("name", name);
	            if (name.startsWith("Thread-") || name.startsWith("http-")) {
	                continue;
	            }
	            StackTraceElement[] stackTrace = thread.getStackTrace();
	            if (stackTrace!= null && stackTrace.length > 0) {
	                String className = stackTrace[stackTrace.length - 1].getClassName();
	                if (!className.equals("java.lang.Thread")) {
	                    object.put("class", className);
	                } else {
	                    object.put("class", "--");
	                }
	            } else {
	                object.put("class", "--");
	            }
	            object.put("isDaemon", thread.isDaemon());
	            list.add(object);
	        }
	        return list;
	    }

	    private static ThreadGroup findTopThreadGroup(ThreadGroup group) {
	        ThreadGroup topGroup = group;
	        while (group!= null) {
	            topGroup = group;
	            group = group.getParent();
	        }
	        return topGroup;
	    }

	    private static int calculateSlackSize(ThreadGroup topGroup) {
	        return topGroup.activeCount() * 2;
	    }

	
	
	@WebControl(name="list",type= Types.LIST)
	public JSONObject list(){
		return queryForList("select * from EASI_APP_INFO", null,new MapRowMapperImpl());
	}
	
	@WebControl(name="moreList",type= Types.TEMPLATE)
	public JSONObject moreList(){
		List<JSONObject> list = null;
		try {
			list = getQuery().queryForList("select * from EASI_CONF where CAN_DELETE = 0 order by ORDER_INDEX", null,new JSONMapperImpl());
			for(JSONObject row :list){
				row.put("CONF_VALUE",ConfigCryptorService.decryptString(row.getString("CONF_VALUE"),"GV"));
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			try {
				list = getQuery().queryForList("select * from EASI_CONF where CAN_DELETE = 0", null,new JSONMapperImpl());
			} catch (SQLException ex) {
				this.error(ex.getMessage(), ex);
			}
		}
		return getJsonResult(list);
	}
	
	@WebControl(name="defaultList",type= Types.TEMPLATE)
	public JSONObject defaultList(){
		List<JSONObject> list = null;
		try {
			list = getQuery().queryForList("select * from EASI_CONF where CAN_DELETE = 2 order by ORDER_INDEX", null,new JSONMapperImpl());
			for(JSONObject row :list){
				row.put("CONF_VALUE",ConfigCryptorService.decryptString(row.getString("CONF_VALUE"),"GV"));
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			try {
				list = getQuery().queryForList("select * from EASI_CONF where CAN_DELETE = 2", null,new JSONMapperImpl());
			} catch (SQLException ex) {
				this.error(ex.getMessage(), ex);
			}
		}
		return getJsonResult(list);
	}
	
	@WebControl(name="latelyLoginLog",type= Types.LIST)
	public JSONObject latelyLoginLog(){
		EasyQuery query=this.getQuery();
		query.setMaxRow(50);
		this.setQuery(query);
		return queryForList("select * from EASI_LOGIN_LOG  order by LOGIN_TIME desc limit 50", new Object[]{},new MapRowMapperImpl());
	}
	
	@WebControl(name="operateLog",type= Types.LIST)
	public JSONObject operateLog(){
		EasyQuery query=this.getQuery();
		query.setMaxRow(100);
		this.setQuery(query);
		return queryForList("select * from EASI_OPERATE_LOG  order by OPERATE_TIME desc limit 100", new Object[]{},new MapRowMapperImpl());
	}
	
	@WebControl(name="appLog",type= Types.LIST)
	public JSONObject appLog(){
		EasyQuery query=this.getQuery();
		query.setMaxRow(150);
		this.setQuery(query);
		String appId = param.getString("appId");
	    EasySQL sql = new EasySQL("select * from EASI_APP_LOG");
	    if(StringUtils.isNotBlank(appId)) {
	    	sql.append("where 1=1");
	    }
	    sql.append(appId,"AND APP_ID = ?");
	    sql.append(param.getString("beginDate"),"AND DATE_ID >= ?");
	    sql.append(param.getString("endDate"),"AND DATE_ID <= ?");
	    sql.append("order by OPERATE_TIME desc limit 150");
    	return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="config",type=Types.RECORD)
	public JSONObject record(){
		JSONObject data = new JSONObject();
		List<Map<String, String>> list;
		try {
			list = this.getConsoleQuery().queryForList("select * from  EASI_CONF", null,new MapRowMapperImpl());
			for(Map<String, String> row :list){
				data.put(row.get("CONF_KEY"),ConfigCryptorService.decryptString(row.get("CONF_VALUE"),"GV"));
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
		JSONObject result = new JSONObject();
		result.put("data", AesUtils.getInstance().encrypt(data.toJSONString()));
		return result;
	}
	
	@WebControl(name="runtime",type=Types.LIST)
	public JSONObject runtime(){
		java.util.Properties  pro =  System.getProperties();
		java.util.Enumeration enums = pro.propertyNames();
		JSONObject result = new JSONObject();
		JSONArray list = new JSONArray();
		
		JSONObject obj = new JSONObject();
		obj.put("key", "ProcessId");
		obj.put("value", Self.getProcessId());
		list.add(obj);

		
	    MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();

	    MemoryUsage memoryUsage = memoryMXBean.getHeapMemoryUsage(); //椎内存使用情况

	    long totalMemorySize = memoryUsage.getInit(); //初始的总内存

	    long maxMemorySize = memoryUsage.getMax(); //最大可用内存

	    long usedMemorySize = memoryUsage.getUsed(); //已使用的内存

	    StringBuffer memory = new StringBuffer();
	    memory.append("TotalMemory:"+totalMemorySize/(1024*1024)+"M");
	    memory.append(",FreeMemory:"+(totalMemorySize-usedMemorySize)/(1024*1024)+"M");
	    memory.append(",MaxMemory:"+maxMemorySize/(1024*1024)+"M");
	    memory.append(",UsedMemory:"+usedMemorySize/(1024*1024)+"M");
	    
	    JSONObject obj2 = new JSONObject();
		obj2.put("key", "JvmMemory");
		obj2.put("value", memory.toString());
		list.add(obj2);
		
		OperatingSystemMXBean mem = (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
		StringBuffer osMemory = new StringBuffer();
		osMemory.append("Total RAM：" + mem.getTotalPhysicalMemorySize() / 1024 / 1024 + "MB");
		osMemory.append(",Available RAM：" + mem.getFreePhysicalMemorySize() / 1024 / 1024 + "MB");
        
        JSONObject obj3 = new JSONObject();
		obj3.put("key", "OsMemory");
		obj3.put("value", osMemory.toString());
		list.add(obj3);
		
		StringBuffer fileDisk = new StringBuffer();
		File[] disks = File.listRoots();
        for(File file : disks)
        {
        	fileDisk.append(file.getPath() + "    ");
        	fileDisk.append("空闲未使用 = " + file.getFreeSpace() / 1024 / 1024 + "M" + "    ");// 空闲空间
            fileDisk.append("已经使用 = " + file.getUsableSpace() / 1024 / 1024 + "M" + "    ");// 可用空间
            fileDisk.append("总容量 = " + file.getTotalSpace() / 1024 / 1024 + "M" + "    ");// 总空间
            fileDisk.append("<br>");
        }
        
        JSONObject obj4 = new JSONObject();
		obj4.put("key", "OsDisks");
		obj4.put("value", fileDisk.toString());
		list.add(obj4);   
	        
	        
	    
		List<String> params = ManagementFactory.getRuntimeMXBean().getInputArguments();
		int i = 1;
		for(String val:params) {
			JSONObject enu = new JSONObject();
			enu.put("key", "vm_params("+i+")");
	      	enu.put("value", val);
	      	list.add(enu);
	      	i++;
		}
		
		String key;
		while(enums.hasMoreElements()){
			JSONObject enu = new JSONObject();
	      	key = (String)enums.nextElement();
	      	
	      	enu.put("key", key);
	      	enu.put("value", System.getProperty(key,""));
	      	list.add(enu);
		}
		
		result.put("data", list);
		result.put("total", list.size());
		return result;
	}
	
}
