package org.easitline.console.vo;

/**
 * 
 * 系统调度任务(Job)
 * <AUTHOR>
 *
 * 对应表结构
 *  create table EASI_JOB_STEP
	(
	  JOB_ID         VARCHAR2(32) not null,
	  JOB_NAME       VARCHAR2(100),
	  JOB_DESC       VARCHAR2(1000),
	  APP_NAME       VARCHAR2(100),
	  CRON           VARCHAR2(20),
	  SERVER_NAME    VARCHAR2(50),
	  JOB_STATE      INTEGER,
	  SERVICE_ID     VARCHAR2(50),
	  SERVICE_NAME   VARCHAR2(100),
	  OK_COUNT       INTEGER,
	  FAIL_COUNT     INTEGER,
	  LAST_OK_TIME   VARCHAR2(32),
	  LAST_FAIL_TIME VARCHAR2(32),
	  DEPEND_JOBS    VARCHAR2(200)
	)
 */
public class AppJobModel implements java.io.Serializable {

	private static final long serialVersionUID = 7695362458284724815L;
	
	private String jobId;         //任务标识
	private String jobName;       //任务名称
	private String appName;       //应用名称，同时也用于jobGroup
	private String jobDesc;       //任务描述
	private String cron;          //任务表达式
	private String jobType;       //调度类型 1：串行  2：并行
	private String jobTypeName;   //调度类型名称
	private String serverName;    //执行MARS服务名称，为空时，缺省采用当前的mars进行调度
	private String serviceId;     //ESB服务ID
	private String serviceName;   //ESB服务名称
	private String jobState;      //任务状态代码
	private String jobStateName;  //任务状态名称
	private String okCount;       //成功执行次数
	private String failCount;     //失败次数
	private String lastOkTime;    //最后执行成功时间
	private String lastFailTime;  //最后执行失败时间
	private String dependJobs;    //依赖调度，多个依赖之间采用，分隔，调度必须是月和日的调度才生效
	private String beginDate;     //开始日期
	private String endDate;       //结束日期
	private String beginTime;     //开始时间
	private String endTime;       //结束时间
	private String nextTime;   //下次执行时间
	
	public String getJobId() {
		return jobId;
	}
	
	public void setJobId(String jobId) {
		this.jobId = jobId;
	}
	
	public String getJobName() {
		return jobName;
	}
	
	public void setJobName(String jobName) {
		this.jobName = jobName;
	}
	
	public String getCron() {
		return cron;
	}
	
	public void setCron(String cron) {
		this.cron = cron;
	}
	
	public String getServiceId() {
		return serviceId;
	}
	
	public void setServiceId(String serviceId) {
		this.serviceId = serviceId;
	}
	
	public String getServiceName() {
		return serviceName;
	}
	
	public void setServiceName(String serviceName) {
		this.serviceName = serviceName;
	}
	
	public String getJobState() {
		return jobState;
	}
	
	public void setJobState(String jobState) {
		this.jobState = jobState;
	}
	
	public String getOkCount() {
		return okCount;
	}
	
	public void setOkCount(String okCount) {
		this.okCount = okCount;
	}
	
	public String getFailCount() {
		return failCount;
	}
	
	public void setFailCount(String failCount) {
		this.failCount = failCount;
	}
	
	public String getLastOkTime() {
		return lastOkTime;
	}
	
	public void setLastOkTime(String lastOkTime) {
		this.lastOkTime = lastOkTime;
	}
	
	public String getLastFailTime() {
		return lastFailTime;
	}
	
	public String getJobTypeName() {
		return jobTypeName;
	}

	public void setJobTypeName(String jobTypeName) {
		this.jobTypeName = jobTypeName;
	}

	public void setLastFailTime(String lastFailTime) {
		this.lastFailTime = lastFailTime;
	}
	
	public String getDependJobs() {
		return dependJobs;
	}
	
	public String getServerName() {
		return serverName;
	}

	public void setServerName(String serverName) {
		this.serverName = serverName;
	}

	public void setDependJobs(String dependJobs) {
		this.dependJobs = dependJobs;
	}

	public String getJobStateName() {
		return jobStateName;
	}

	public void setJobStateName(String jobStateName) {
		this.jobStateName = jobStateName;
	}

	public String getAppName() {
		return appName;
	}

	public String getNextTime() {
		return nextTime;
	}

	public void setNextTime(String nextTime) {
		this.nextTime = nextTime;
	}

	public void setAppName(String appName) {
		this.appName = appName;
	}

	public String getJobDesc() {
		return jobDesc;
	}

	public void setJobDesc(String jobDesc) {
		this.jobDesc = jobDesc;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getJobType() {
		return jobType;
	}

	public void setJobType(String jobType) {
		this.jobType = jobType;
	}

	public String getBeginDate() {
		return beginDate;
	}

	public void setBeginDate(String beginDate) {
		this.beginDate = beginDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getBeginTime() {
		return beginTime;
	}

	public void setBeginTime(String beginTime) {
		this.beginTime = beginTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

}
