package org.easitline.console.vo;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.easitline.common.db.EasyRowMapper;


/**
--应用配置信息
create table EASI_APP_CONF (
   APP_ID               varchar(100)                   not null,
   ITEM_KEY             VARCHAR(100)                   ,
   ITEM_NAME            VARCHAR(100)                   ,
   ITEM_DESC            VARCHAR(500)                   ,
   JSON_STRING          VARCHAR(500)                   ,  --用于通过JOSN来配置选择内容，如果JSON内容存在，贼ITEM_TYPE没有用
   ITEM_VALUE           VARCHAR(500)                   ,  
   ITEM_TYPE            VARCHAR(100)                   ,  --取值：number、string、date、time、datetime
  PRIMARY KEY(APP_ID, ITEM_KEY)
)
 * <AUTHOR>
 *
 */
public class AppConfigRowMapper implements EasyRowMapper<AppConfigModel> {

	@Override
	public AppConfigModel mapRow(ResultSet rs, int rowNum) {
		// TODO Auto-generated method stub
		AppConfigModel vo = new AppConfigModel();
		try {
			vo.setAppId(rs.getString("APP_ID"));
			vo.setItemKey(rs.getString("ITEM_KEY"));
			vo.setItemName(rs.getString("ITEM_NAME"));
			vo.setItemDesc(rs.getString("ITEM_DESC"));
			vo.setItemValue(rs.getString("ITEM_VALUE"));
		} catch (SQLException ex) {
			ex.printStackTrace();
		}
		return vo;
		
	}

}
