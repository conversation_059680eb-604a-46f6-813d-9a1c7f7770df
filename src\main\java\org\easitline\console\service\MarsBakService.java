package org.easitline.console.service;

import java.io.File;
import java.io.IOException;

import org.apache.log4j.Logger;
import org.easitline.common.core.Globals;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.FileKit;
import org.apache.commons.lang3.StringUtils;
import org.easitline.console.base.Constants;

public class MarsBakService{
	 
	private static class Holder{
		private static MarsBakService service=new MarsBakService();
	}
	public static MarsBakService getService(){
		return Holder.service;
	}
	
	public Logger getLogger() {
		return LogEngine.getLogger(Constants.EASITLINE_CONSOLE, "mars-bak");
	}
	
	public void run() {
		bakWar(null);
		bakConfDb();
	}
	
	private String deployDir() {
		return System.getProperty("deployDir", Globals.WEBAPPS_DIR);
	}
	
	public void bakWar(String warName) {
		String bakPath = deployDir();
		if(StringUtils.isBlank(warName)) {
			bakConfDb();
		}
		MarsBakService.getService().bakWar(bakPath,warName);
	}
	
	public String bakConfDb() {
		File bakFile = new File(Globals.SERVER_DIR+File.separator+"db"+File.separator+"bak");
		if(!bakFile.exists()) {bakFile.mkdir();}
		
		String bakFilePath = Globals.SERVER_DIR+File.separator+"db"+File.separator+"mars.db";
		File targetFile = new File(bakFilePath);
		if(targetFile.exists()) {
			try {
				String path = bakFile.getAbsolutePath()+File.separator+"mars_"+EasyDate.getCurrentDateString("yyyyMMddHHmmss")+".db";
				FileKit.copyFile(targetFile.getAbsolutePath(), path);
				getLogger().info(path+">mars.db备份成功", null);
				return path;
			} catch (IOException e) {
				getLogger().error(e.getMessage(), e);
			}
		}
		return null;
	}
	
	
	public void bakWar(String bakPath,String bakWarName){
		File bakWarFile = new File(Globals.SERVER_DIR+File.separator+"bakwar");
		if(!bakWarFile.exists()) {bakWarFile.mkdir();}
		
		String targetDir = bakWarFile.getAbsolutePath()+File.separator+EasyDate.getCurrentDateString("yyyyMMddHHmmss");
		File targetFile = new File(targetDir);
		if(!targetFile.exists()) {targetFile.mkdir();}
		
		File file = new File(bakPath);
		File[] files = file.listFiles();
		for(File logFile:files) {
			String fileName = logFile.getName();
			if(StringUtils.isNotBlank(bakWarName)&&!fileName.equalsIgnoreCase(bakWarName)) {
				continue;
			}
			if(fileName.indexOf(".war")>-1) {
				long timestamp = logFile.lastModified();
				try {
					String targetFilePath = targetDir+File.separator+fileName;
					FileKit.copyFile(logFile.getAbsolutePath(), targetFilePath);
					
					File logBakFile = new File(targetFilePath);
					logBakFile.setLastModified(timestamp);
					
					getLogger().info(targetFilePath+">备份成功", null);
				} catch (IOException e) {
					getLogger().error(e.getMessage(), e);
				}
				
			}
			
		}
	}

}
