$(function(){
	
	fillDataOfMinutesAndHoursSelectOptions();
    fillDayWeekInMonth();
    fillInWeekDays();
    fillInMonths(); 
    createCanlendar2(); 
    initalDate();
    changeDesc("#DailyTab");
    
    $("#mgrs_ok").click(function(){		

    	var cron = generate();
    	var dateIntervalObj = getDateInterval();
    	var timeIntervalObj = getTimeInterval();
    	
    	var jobDesc = $("#summarySpan").html();;
    	
    	var activeTab = $("div.active").attr('id');
    	if("MinutesTab" != activeTab && "OneTimeTab" != activeTab) {
    		callbackCron(cron, jobDesc, dateIntervalObj, timeIntervalObj);
    	} else {
    		callbackCron(cron, jobDesc);
    	}
    	
    	$("#lookup_modal").modal("hide");
    });
    
    $(".sui-nav li a").click(function(){
    	
    	var tab = $(this).attr("href");
    	if("#MinutesTab" == tab || "#OneTimeTab" == tab) {
    		$("#keepTimeTable").hide();
    		$("#everyDayTable").hide();
    	} else {
    		$("#keepTimeTable").show();
    		$("#everyDayTable").show();
    	}
    	changeDesc(tab);
    });
    
    //createCanlendar();
    
    $(".day").click(function(){		
    	$(this).toggleClass("active");
    });
    
    $("input[name='timeTypeRadio']").click(function() {
    	changeDesc();
    });
    
    $("#oneTimeTimer").change(function(){
    	changeDesc();
    });
    
    $("#intervalInput").change(function(){
    	changeDesc();
    });
    
    $("#intervalTypeSel").change(function(){
    	changeDesc();
    });
    
    $("#beginTime").change(function(){
    	changeDesc();
    });
    
    $("#endTime").change(function(){
    	changeDesc();
    });
    
    $("input[name='endDateRadio']").click(function(){
    	changeDesc();
    });
    
    $("#endDate").change(function(){
    	changeDesc();
    });
    
    $("#WeeklyTab input:checkbox").click(function(){
    	changeDesc();
    });
    
    $("#MonthlyTab input:checkbox").click(function(){
    	changeDesc();
    });
    
    $("input[name='executeTypeRadio']").click(function(){
    	changeDesc();
    });
    
	$("#beginHour").change(function(){
    	changeDesc();
    });
	
	$("#beginMinute").change(function(){
    	changeDesc();
    });
	
	$("#minuteInput").change(function(){
    	changeDesc();
    });
	
	$("#oneTimeDate").change(function(){
		changeDesc();
	});
	
	$("#oneTimeTime").change(function(){
		changeDesc();
	});

    /* $('#DayOfMOnthInput').datepicker({
    	autoclose:false,
    	todayHighlight:false,
    	format:"dd",
    	startView:0,
    	minViewMode:0,
    	Multidate:3,
        size: "small"
    });  */
});

//关闭日期选择窗口
function closeDateSelect() {
	//$(".day").removeClass("active");
	$("#dateSelectDiv").hide();
}

//显示选择日期控件
function showDateCalendar() {
	
	var pos = $("#DayOfMOnthInput").position();
	
	var positionTop = $("#DayOfMOnthInput").position().top;   
	var positionLeft = $("#DayOfMOnthInput").position().left;  
	var height = $("#DayOfMOnthInput").height()+3;

	$("#dateSelectDiv").css({position: "absolute",'top':positionTop+height,'left':positionLeft,'z-index':2});   
	$("#dateSelectDiv").show();
}

//初始化开始时间
function initalDate() {
	
	var today = new Date();
    var toyear = today.getFullYear(); 
    var tomonth = today.getMonth()+1;
    var todate = today.getDate();
    
    if(tomonth<10) {
       tomonth = "0" + tomonth;
    }
    
    if(todate<10) {
    	todate = "0" + todate;
    }
    
    var beginDate = toyear + "-" + tomonth + "-" + todate;
    
    $("#beginDate").val(beginDate);
    $("#endDate").val(beginDate);
    
    $("#oneTimeDate").val(beginDate);
    
    function myHandler(event) {
    	//console.info(event.data.old);
    	var date = $(this).val();
    	var oldVal = event.data.old;
    	//console.info(data);
    	if(null == date || "" == date) {
    		 $(this).val(oldVal);
    	}
    }
    $("#beginDate").on("change", {old:  $("#beginDate").val()}, myHandler);
    
    $("#endDate").on("change", {old:  $("#endDate").val()}, myHandler);
    
    $("#oneTimeDate").on("change", {old:  $("#oneTimeDate").val()}, myHandler);
        
}

//获取今天的日期
function getToday() {
	
	var today = new Date();
    var toyear = today.getFullYear(); 
    var tomonth = today.getMonth()+1;
    var todate = today.getDate();
    
    if(tomonth<10) {
        tomonth = "0" + tomonth;
     }
     
     if(todate<10) {
     	todate = "0" + todate;
     }
    
    return toyear + "-" + tomonth + "-" + todate;
}

//创建日历
function createCanlendar() {
	
	var dateAry = new Array();

	for(var i=1; i<=35; i++) {
		
		if(i%7 == 1) {
			dateAry.push("<tr>");
		}

		if(i>31) {
			dateAry.push("<td class='day new' data-day=''></td>");
		} else {
			dateAry.push("<td class='day' data-day='" ,i , "'>" , i , "</td>");
		}
		
		if(i%7 == 0) {
			dateAry.push("</tr>");
		}
	}
	
	$("#dateTableBody").html(dateAry.join(""));
}

//获取选中的日期
function getSelectedDate() {
	var selectedDayAry = new Array();
	$(".day").each(function(index, item){
		if($(this).hasClass("active")) {
			var day = $(this).attr("data-day");
			selectedDayAry.push(day);
		}
	});
	$("#DayOfMOnthInput").val(selectedDayAry.join(","));
	$("#dateSelectDiv").hide();
}

//填充月份
function fillInMonths() {
    var days = [
        { text: "一月", val: "1" },
        { text: "二月", val: "2" },
        { text: "三月", val: "3" },
        { text: "四月", val: "4" },
        { text: "五月", val: "5" },
        { text: "六月", val: "6" },
        { text: "七月", val: "7" },
        { text: "八月", val: "8" },
        { text: "九月", val: "9" },
        { text: "十月", val: "10" },
        { text: "十一月", val: "11" },
        { text: "十二月", val: "12" }
    ];
    $(".months").each(function () {
        fillOptions(this, days);
    });
};

//填充星期几
function fillInWeekDays() {
    var days = [
        { text: "星期一", val: "MON" },
        { text: "星期二", val: "TUE" },
        { text: "星期三", val: "WED" },
        { text: "星期四", val: "THU" },
        { text: "星期五", val: "FRI" },
        { text: "星期六", val: "SAT" },
        { text: "星期日", val: "SUN" }
    ];
    $(".week-days").each(function () {
        fillOptions(this, days);
    });

};

//填充第几周
function fillDayWeekInMonth() {
    var days = [
        { text: "第一周", val: "1" },
        { text: "第二周", val: "2" },
        { text: "第三周", val: "3" },
        { text: "第四周", val: "4" }
    ];
    $(".day-order-in-month").each(function () {
        fillOptions(this, days);
    });
};

//填充选项
function fillOptions(elements, options) {
    for (var i = 0; i < options.length; i++)
        $(elements).append("<option value='" + options[i].val + "'>" + options[i].text + "</option>");
};

//填充小时和分钟
function fillDataOfMinutesAndHoursSelectOptions() {
    for (var i = 0; i < 60; i++) {
        if (i < 24) {
            $(".hours").each(function () { $(this).append(timeSelectOption(i)); });
        }
        $(".minutes").each(function () { $(this).append(timeSelectOption(i)); });
    }
};

function displayTimeUnit(unit) {
    /* if (unit.toString().length == 1)
        return "0" + unit; */
    return unit;
};

function timeSelectOption(i) {
    return "<option id='" + i + "'>" + displayTimeUnit(i) + "</option>";
};

//处理定时Tab
function doWithMinuteTab() {
	
	 var result = "";
	 var intervalMinute = $("#minutesInput").val();   //分钟
     var beginHour = $("#beginHour").val();   //开始小时数
     var beginMinute = $("#beginMinute").val();   //开始分钟数
     var radioVal = $("input[name='executeTypeRadio']:checked").val();
     
     if(null == intervalMinute || intervalMinute.length == 0) {
    	 alert("间隔分钟数不能为空");
    	 return result;
     }
     
     var cronMinute = "";
     //debugger;
     if(radioVal == 'time') {
    	 cronMinute = beginMinute + "/" + intervalMinute;
     } else {
    	 cronMinute = "0/" + intervalMinute;
    	 beginHour = "*";
     }

     var cronStr = new Array();
     cronStr.push("0", " ", cronMinute, " ", beginHour, " * * ?");
     
     result = cronStr.join("");
     return  result;
}

//处理每天Tab
function doWithDailyTab() {
	
	var result = "";
	result = doWithCommonPart("*", "*", "?");
	return result;
}

//处理每周的Tab
function doWithWeeklyTab() {
	
	  var result = "";
	  var selectedDays = "";
      $("#WeeklyTab input:checkbox:checked").each(function () { selectedDays += $(this).val() + ","; });
      if (selectedDays.length > 0)
          selectedDays = selectedDays.substr(0, selectedDays.length - 1);
      
      var dayOfMonth = "?";
      if(null == selectedDays || selectedDays.length == 0) {
    	  selectedDays = "?";
    	  dayOfMonth = "*";
      }
      
      result = doWithCommonPart(dayOfMonth, "*", selectedDays);
      
      return result;
}

//处理每月的Tab
function doWithMonthlyTab() {
	
	var result = "";
	var selectedDays = "";
    $("#MonthlyTab input:checkbox:checked").each(function () { selectedDays += $(this).val() + ","; });
    if (selectedDays.length > 0)
        selectedDays = selectedDays.substr(0, selectedDays.length - 1);
    
    if(null == selectedDays || selectedDays.length == 0) {
  	  selectedDays = "*";
    }
    
    result = doWithCommonPart("*", selectedDays, "?");
    
    return result;
}

//处理公共部分
function doWithCommonPart(dayOfMonth, month, dayOfWeek) {
	
	var result = "";
	var timeType = $("input[name='timeTypeRadio']:checked").val();
	var retObj = null;
	var cronAry = new Array();
	
	if("oneTime" == timeType) {  //执行一次
		retObj = getOneTimeExpression();   //获取执行时间的小时和分钟
		cronAry.push("0 ", retObj.minute, " ", retObj.hour, " ", dayOfMonth, " ", month, " ", dayOfWeek);
	} else if(timeType == "moreTime") {   //执行多次
		retObj = getMoreTimesExpression();
	
		var intervalType = retObj.intervalType;
		
		if("hour" == intervalType) {
			cronAry.push("0 ", retObj.beginTimeMinute, " ",  retObj.beginTimeHour, "-", retObj.endTimeHour, "/",retObj.interval, " ", dayOfMonth, " ", month, " ", dayOfWeek);
		} else if("minute" == intervalType) {
			cronAry.push("0 ", retObj.beginTimeMinute, "/", retObj.interval, " ", retObj.beginTimeHour, "-", retObj.endTimeHour, " ", dayOfMonth, " ", month, " ", dayOfWeek);
		}
	}
	
	//获取当前日期
	/* var today = new Date();
    var toyear = today.getFullYear(); 
    var tomonth = today.getMonth()+1;
    var todate = today.getDate();  */
	
	result = cronAry.join("");
	
	return result;
}

//处理执行一次
function doWithOneTimeTab() {
	
	var result = "";
	var oneTimeDate = $("#oneTimeDate").val();
	var oneTimeTime = $("#oneTimeTime").val();
	
	var oneTimeDateAry = oneTimeDate.split("-");
    var oneTimeTimeAry = oneTimeTime.split(":");
	
	var cronAry = new Array();
	
	//cronAry.push("0 ", oneTimeTimeAry[1], " ",  oneTimeTimeAry[0], " ", oneTimeDateAry[2], " ", oneTimeDateAry[1], " ? ", oneTimeDateAry[0]);
	cronAry.push("0 ", parseInt(oneTimeTimeAry[1]), " ",  parseInt(oneTimeTimeAry[0]), " ", parseInt(oneTimeDateAry[2]), " ", parseInt(oneTimeDateAry[1]), " ?");
	
	result = cronAry.join("");
	
	return result;
}

//获取执行一次的表达式
function getOneTimeExpression() {
	
	var oneTimeTimerVal = $("#oneTimeTimer").val();
	var oneTimeAry = oneTimeTimerVal.split(":");
	var hour = oneTimeAry[0];
	var minute = oneTimeAry[1];

	var obj = new Object();
	obj.hour = parseInt(hour);
	obj.minute = parseInt(minute);
	
	return obj;
}

//获取执行多次的表达式
function getMoreTimesExpression () {
	
	var intervalVal = $("#intervalInput").val();
	var intervalType = $("#intervalTypeSel :checked").val();
	var beginTime = $("#beginTime").val();
	var endTime = $("#endTime").val();
	
	var beginTimeAry = beginTime.split(":");
	var endTimeAry = endTime.split(":"); 

	var obj = new Object();
	obj.interval = intervalVal;
	obj.intervalType = intervalType;
	obj.beginTimeHour = parseInt(beginTimeAry[0]);
	obj.beginTimeMinute = parseInt(beginTimeAry[1]);
	obj.endTimeHour = parseInt(endTimeAry[0]);
	obj.endTimeMinute = parseInt(endTimeAry[1]);
	
	return obj;
}

//获取开始日期和结束日期
function getDateInterval() {
	
	var beginDate = $("#beginDate").val();
	var endDate = $("#endDate").val();
	var endDateChoose = $("input[name='endDateRadio']:checked").val();
	var obj = new Object();
	obj.beginDate = beginDate;
	
	if(endDateChoose == "hasEndDate") {
		obj.endDate = endDate;
	} 
	return obj;
}

//获取开始时间和结束时间
function getTimeInterval() {
	var timeType = $("input[name='timeTypeRadio']:checked").val();
	var obj = new Object();
	if("moreTime" == timeType) {
		var beginTime = $("#beginTime").val();
		var endTime = $("#endTime").val();
		
		obj.beginTime = beginTime;
		obj.endTime = endTime;
		
		return obj;
	}
}

//验证开始时间和结束时间
function checkBeginTimeAndEndTime() {
	
	var moreTimesBeginHour = Number($("#dailyBeginHour").val());
	var moreTimesEndHour = Number($("#dailyEndHour").val());
	
	if(moreTimesBeginHour>moreTimesEndHour) {
		alert("开始时间必须大于结束时间");
		return;
	}
	
}

//生成cron表达式
function generate() {

    var activeTab = $("div.active").attr('id');
    var results = "";
    switch (activeTab) {
        case "MinutesTab":
        	 results = doWithMinuteTab();
        	 break;
        case "DailyTab":
        	results = doWithDailyTab();
            break;
        case "WeeklyTab":
            results = doWithWeeklyTab();
            break;
        case "MonthlyTab":
        	results = doWithMonthlyTab();
            break;
        case "OneTimeTab":
        	results = doWithOneTimeTab();
            break;
    }
    return results;
}

//隐藏公共Div
function hideCommonDiv() {
   $("#commonDiv").hide();
}

//显示公共Div
function showCommonDiv() {
   $("#commonDiv").show();
}

//生成月份
function createCanlendar2() {
	
   
   var dateOfMoneyHtml = new Array(); 
   
   //dateOfMoneyHtml.push("<div class='sui-row'>");
   
   for(var i=1; i<=31; i++) {
	   
	   var date = i;
	   if(i<10) {
		   date = "0"+date;
	   }
	   dateOfMoneyHtml.push("<input type='checkbox' value='", i, "'>", date,"&nbsp;&nbsp;&nbsp;&nbsp;");
	   
	   if(i%10==0) {
		   dateOfMoneyHtml.push("<br/>");
	   }
   }
   $("#calendarDiv").html(dateOfMoneyHtml.join(""));
}

//显示调度任务的描述
function changeDesc(tab) {
	
	var activeTab = tab;
	
	if(null == activeTab) {
		activeTab = $("div.active").attr('id');
	} else {
		activeTab = activeTab.substring(1,activeTab.length); 
	}

	var evreyStr = "";
	var summaryStrAry = new Array();
	
	if("MinutesTab" == activeTab) {
		createMinuteDesc();
		return;
	} else if("DailyTab" == activeTab) {
		evreyStr = "每天";
	} else if("WeeklyTab" ==  activeTab) {
		evreyStr = "每周";
	} else if("MonthlyTab" == activeTab) {
		evreyStr = "每月";
	} else if("OneTimeTab" == activeTab) {
		createOneTimeDesc();
		return;
	} 
	

	if(!checkTime()) {
		$("#endTime").val("23:59");
	}
	
	if(!checkDate()) {
		var today = getToday();
		$("#endDate").val(today);
	} 

	var beginTime = $("#beginTime").val();
	var endTime = $("#endTime").val();
	var interval = $("#intervalInput").val();
	var intervalType = $("#intervalTypeSel :checked").val();    //频率：一次还是多次
	var beginDate = $("#beginDate").val();
	var endDate = $("#endDate").val();
	var endDateStatus = $("input[name='endDateRadio']:checked").val();   //是否需要结束日期
	var timeType = $("input[name='timeTypeRadio']:checked").val();  //每天频率
	var oneTimeStr = $("#oneTimeTimer").val();
	
	if("hour" ==  intervalType) {
		intervalType = "小时";
	} else {
		intervalType = "分钟";
	}
	
	//计算每天频率
	var everyDateTimesStr = "";
	if("oneTime" == timeType) {
		everyDateTimesStr = "在" + oneTimeStr + ":00执行一次。";
	} else {
		everyDateTimesStr = "在" + beginTime +  ":00" + "和" + endTime + ":59" + "之间，每" +  interval + intervalType + "执行。";
	}
	
	var endDateStr = "";
	if("hasEndDate" == endDateStatus) {
		endDateStr = "，" + endDate + "结束。";
	} else {
		endDateStr = "。";
	}
	
	//显示执行周和月
	var selectedDays = "";
	if("WeeklyTab" ==  activeTab) {
	     $("#WeeklyTab input:checkbox:checked").each(function () { selectedDays += $(this).attr("txt") + ","; });
	     if (selectedDays.length > 0) {
	    	 selectedDays = selectedDays.substr(0, selectedDays.length - 1);
	     }   
	} else if("MonthlyTab" == activeTab) {
	    $("#MonthlyTab input:checkbox:checked").each(function () { selectedDays += $(this).val() + ","; });
	    if (selectedDays.length > 0) {
	    	 selectedDays = selectedDays.substr(0, selectedDays.length - 1);
	    	 selectedDays += "号";
	    }
	} 
	summaryStrAry.push(evreyStr, selectedDays, everyDateTimesStr , "将从", beginDate, "开始使用计划", endDateStr);
	
	$("#summarySpan").html(summaryStrAry.join(""));
}

//验证开始时间是否小于结束时间
function checkTime() {
	
	var isOk=true;
	var beginTime = $("#beginTime").val();
	var endTime = $("#endTime").val();
	
	if(null == beginTime || null == endTime)return isOk;
	
	var beginTimeAry = beginTime.split(":");
	var endTimeAry = endTime.split(":");
	var beginHour = parseInt(beginTimeAry[0]);
	var beginMinute = parseInt(beginTimeAry[1]);
	var endHour = parseInt(endTimeAry[0]);
	var endMinute = parseInt(endTimeAry[1]);
	
	if(endHour<beginHour) {
		isOk = false;
	} else if(endHour==beginHour){
		if(endMinute<beginMinute) {
			isOk = false;
		}
	}
	
	return isOk;
} 

//生成定时描述
function createMinuteDesc() {
	
	var executeType = $("input[name='executeTypeRadio']:checked").val();
	var beginHour = $("#beginHour").val();
	var beginMinute = $("#beginMinute").val();
	var interval = $("#minutesInput").val();
	
	var descStrAry = new Array();
	
	var beginTimeStr = "";
	if("now" == executeType) {
		beginTimeStr = "在下一分钟";
	} else {
		beginTimeStr = beginMinute + ":" + beginMinute;
	}
	
	descStrAry.push("将", beginTimeStr, "开始每隔", interval, "分钟执行一次");
	
	$("#summarySpan").html(descStrAry.join(""));
}

//生成执行一次的描述
function createOneTimeDesc() {
	
	var oneTimeDate = $("#oneTimeDate").val();
	var oneTimeTime = $("#oneTimeTime").val();
	
	var oneTimeStr = "将在" + oneTimeDate + " " + oneTimeTime + "执行一次后结束。";
	
	$("#summarySpan").html(oneTimeStr);
}

//验证开始日期是否小于结束日期
function checkDate() {
	//debugger;
	var beginDate = $("#beginDate").val();
	var endDate = $("#endDate").val();
	var isOk = true;
	
	if(null == beginDate || null == endDate)return isOk;
	
	var endDateStatus = $("input[name='endDateRadio']:checked").val();
	var beginDateAry = beginDate.split("-");
	var endDateAry = endDate.split("-");
	var beginYear = parseInt(beginDateAry[0]);
	var beginMonth = parseInt(beginDateAry[1]);
	var beginDay = parseInt(beginDateAry[2]);
	var endYear = parseInt(endDateAry[0]);
	var endMonth = parseInt(endDateAry[1]);
	var endDay = parseInt(endDateAry[2]);
	
	if("hasEndDate" == endDateStatus) {
		
		if(endYear<beginYear) {
			isOk = false;
		} else if(endYear==beginYear) {
			if(endMonth<beginMonth) {
				isOk = false;
			} else if(endMonth==beginMonth){
				if(endDay<beginDay) {
					isOk = false;
				}
			}
		}
	}
	return isOk;
} 