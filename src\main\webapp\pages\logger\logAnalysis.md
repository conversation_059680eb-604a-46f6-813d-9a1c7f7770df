
### 一、部署失败

* **日志分析：**
    * **catalina.2024-06-16.log** 和 **localhost.log**：重点关注异常堆栈信息（如：java.lang.OutOfMemoryError、java.lang.ClassNotFoundException等），以及启动过程中出现的错误提示。
    * **logging.properties**：确保日志配置正确，日志级别设置合理，以便捕获足够详细的信息。

* **常见原因：**
    * **配置错误：** 检查server.xml、context.xml等配置文件，确保端口号、路径等配置正确。
    * **依赖缺失：** 确认所有依赖的jar包已正确部署。
    * **权限问题：** 确保Tomcat用户对日志目录、部署目录等具有写权限。

### 二、启动失败

* **日志分析：**
    * **catalina.out** 和 **catalina.${date}.log**：重点关注启动过程中出现的错误提示，如端口冲突、内存不足、类加载失败等。

* **常见原因：**
    * **端口冲突：** 修改server.xml中Connector的port属性。
    * **内存不足：** 增加JVM内存参数（-Xms、-Xmx），或优化代码减少内存占用。
    * **JDK路径错误：** 检查JAVA_HOME环境变量设置。
    * **权限问题：** 确保MARS目录及子目录具有正确的权限。
    * **配置文件错误：** 仔细检查配置文件(catalina.sh,server.xml)语法和配置项。

### 三、宕机异常退出

* **日志分析：**
    * **catalina.out**、**localhost.log**、**catalina.log**：重点关注异常堆栈信息，以及发生宕机时的系统状态。
    * **gc.log**：分析GC日志，判断是否为内存泄漏或GC频繁导致。
    * **hs_err_pid.log**：分析JVM错误日志，查找核心转储信息。

* **常见原因：**
    * **内存溢出：** 增加JVM内存参数，优化代码，使用内存分析工具定位内存泄漏。
    * **线程死锁：** 使用jstack命令生成线程转储，分析死锁情况。
    * **系统资源耗尽：** 检查CPU、磁盘IO等系统资源使用情况。
    * **代码Bug：** 仔细检查代码，修复导致异常的Bug。

### 四、服务不可访问

* **日志分析：**
    * **catalina.out**、**localhost.log**：检查是否有异常信息。
    * **jstack日志**：分析线程状态，查看是否有线程阻塞或死锁。

* **常见原因：**
    * **连接数限制：** 增加Tomcat的maxThreads参数。
    * **线程池耗尽：** 调整线程池配置，检查程序(慢SQL或异常程序)。
    * **网络问题：** 检查网络连接是否正常。
    * **防火墙限制：** 检查防火墙设置。

### 五、功能异常

* **日志分析：**
    * **jdbc-error.log**、**catalina-error.log**、**catalina.out**：检查数据库连接、SQL执行等异常。
    * **浏览器控制台**：查看JavaScript错误、网络请求异常。

* **常见原因：**
    * **代码逻辑错误：** 仔细检查代码逻辑。
    * **数据库问题：** 检查数据库连接、SQL语句是否正确。
    * **配置错误：** 检查配置文件是否正确。
    * **外部系统依赖问题：** 检查依赖的外部系统是否正常。

### 总结

* **系统化分析：** 根据问题现象，有针对性地分析相关日志。
* **工具辅助：** 使用jstack、jmap等工具辅助分析。
* **排除法：** 通过排除法逐步缩小问题范围。
* **经验积累：** 总结常见问题，建立问题库。

**建议：**

* **加强日志管理：** 配置详细的日志，便于问题排查。
* **定期监控：** 定期监控系统运行状态，及时发现潜在问题。
* **建立问题库：** 记录常见问题及解决方案，方便快速查找。
* **优化代码：** 提高代码质量，减少故障发生。



## JDK工具 jps, jstack, jmap,
- `jps`：快速查看当前系统中的Java进程。
- `jstack`：分析线程问题，如死锁或挂起。脚本dump_threads.sh，工具fastthread。
- `jmap`：内存分析，生成堆内存快照，检查内存使用情况。脚本dump_heap.sh，工具JProfiler。


