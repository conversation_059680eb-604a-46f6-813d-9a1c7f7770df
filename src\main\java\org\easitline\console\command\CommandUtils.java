package org.easitline.console.command;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

import org.easitline.common.core.log.LogEngine;

public class CommandUtils {
	
	private static String logName = "console-cmd";
	
	public static String runCommands(String cmd){
		LogEngine.getLogger(logName).warn("excute >> "+cmd);
		String[] commands = {"/bin/sh","-c",cmd};
		return runCommands(commands);
	}
	
	public static String getTomcatServerList() {
		//echo $PPID
		return runCommands("ps -ef|grep java | grep catalina");
	}

	public static String runCommands(String[] commands){
		Runtime rt = Runtime.getRuntime();
		StringBuffer str  = new StringBuffer("");
		StringBuffer errStr  = new StringBuffer("");
		try {
			Process proc = rt.exec(commands);
			BufferedReader stdInput = new BufferedReader(new InputStreamReader(proc.getInputStream()));
			BufferedReader stdError = new BufferedReader(new InputStreamReader(proc.getErrorStream()));
			
//			ReadThread.execute("stdout", proc.getInputStream(), true);
//			ReadThread.execute("errout", proc.getErrorStream(), true);
	        
			try {
				proc.waitFor();
			} catch (InterruptedException e) {
				LogEngine.getLogger(logName).error(null,e);
			}
			
			String line = null;
			int i = 0;
			while ((line = stdInput.readLine()) != null) {
				LogEngine.getLogger(logName).warn(line);
				str.append(line).append("\n");
				i++;
				if(i>10000) {
					break;
				}
			}
			int j = 0;
			while ((line = stdError.readLine()) != null) {
				LogEngine.getLogger(logName).warn(line);
				errStr.append(line).append("\n");
				if(j>10000) {
					break;
				}
			}
			
			if(StringUtil.notBlank(errStr.toString())) {
				return str.toString() + errStr.toString();
			}
		} catch (IOException e) {
			LogEngine.getLogger(logName).error(e);
			return e.getMessage();
		}
		return str.toString();
	}
}
		
