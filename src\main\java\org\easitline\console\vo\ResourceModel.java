package org.easitline.console.vo;

import org.apache.commons.lang3.StringUtils;

/**
 * 
 * <AUTHOR>
 *
 *         create table EASI_RES ( RES_ID varchar(30) not null comment
 *         '资源ID，全局唯一，由应用本身定义', RES_NAME varchar(50) not null comment '资源名称',
 *         P_RES_ID varchar(20) not null comment '父资源ID', RES_URL varchar(200)
 *         comment 'URL访问地址', RES_TYPE int default 1 comment '资源类型，1：应用
 *         ，2：菜单，3：功能，9: 其它', RES_TARGET varchar(20) comment '打开方式，_blank
 *         在新的窗口打开，否则在portal中打开', RES_STATE int default 0 comment '状态，0:正常，缺省
 *         ，1：暂停', IDX_ORDER int comment '排序', APP_ID varchar(30), primary key
 *         (RES_ID) )
 *
 *
 */
public class ResourceModel {
	
	private String resId;
	private String resName;
	private String presId;
	private String resUrl;
	private String resType;
	private String resUpdate;
	private String resRemark;
	private String resState;
	private String idxOrder;
	private String appId;
	private String portal;
	private String resIcon;
	private String entId;
	private String authFlag;
	private String resCode;
	
	public String getPortal() {
		if(StringUtils.isBlank(portal)) return "2000";
		return portal;
	}

	public void setPortal(String portal) {
		this.portal = portal;
	}

	public String getResId() {
		return resId;
	}

	public void setResId(String resId) {
		this.resId = resId;
	}

	public String getResName() {
		return resName;
	}

	public void setResName(String resName) {
		this.resName = resName;
	}

	public String getPresId() {
		return presId;
	}

	public void setPresId(String presId) {
		this.presId = presId;
	}

	public String getResUrl() {
		if(this.resUrl == null) return "";
		return resUrl;
	}

	public void setResUrl(String resUrl) {
		this.resUrl = resUrl;
	}

	public String getResType() {
		if(StringUtils.isBlank(resType)) return "2";
		return resType;
	}

	public void setResType(String resType) {
		this.resType = resType;
	}

	public String getResState() {
		return resState;
	}

	public void setResState(String resState) {
		this.resState = resState;
	}

	public String getIdxOrder() {
		if(idxOrder == null) return "1";
		return idxOrder;
	}

	public void setIdxOrder(String idxOrder) {
		this.idxOrder = idxOrder;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getResIcon() {
		return resIcon;
	}

	public void setResIcon(String resIcon) {
		this.resIcon = resIcon;
	}
	
	public String getEntId() {
		return entId;
	}

	public void setEntId(String entId) {
		this.entId = entId;
	}

	public String getResUpdate() {
		if(StringUtils.isBlank(resUpdate)) return "0";
		return resUpdate;
	}

	public void setResUpdate(String resUpdate) {
		this.resUpdate = resUpdate;
	}

	public String getAuthFlag() {
		return authFlag;
	}

	public void setAuthFlag(String authFlag) {
		this.authFlag = authFlag;
	}

	public String getResRemark() {
		return resRemark;
	}

	public void setResRemark(String resRemark) {
		this.resRemark = resRemark;
	}

	public String getResCode() {
		return resCode;
	}

	public void setResCode(String resCode) {
		this.resCode = resCode;
	}

	

}
