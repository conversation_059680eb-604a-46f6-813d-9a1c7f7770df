<%@page pageEncoding="UTF-8"%>
<div>
	<blockquote class="layui-elem-quote">
		请填写调度任务信息，选择调用的ESB服务，填写调度计划，填写调度任务名称 ，属于应用字段只用于标识跟哪个应用关联，没有别的功能作用
	</blockquote>

	<fieldset class="layui-elem-field layui-field-title">
		<legend>定时任务</legend>
	</fieldset>

	<form class="layui-form" id="easyform" action="" autocomplete="off" data-mars="job.record" data-mars-prefix="job.">
		<input type="hidden" name="jobIdInput" value="${param.jobId}" />
		<div id="formItem">
		</div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">任务名称</label>
	    <div class="layui-input-block">
	      <input type="text" name="job.jobName" lay-verify="required" class="layui-input">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">调用服务</label>
	    <div class="layui-input-block">
	      <select name="job.serviceId" class="layui-select" lay-verify="required" data-mars="job.serviceDict">
			<option value="">请选择</option>
	      </select>
		    <div class="layui-form-mid layui-word-aux">服务ID以“CRON-”开头的服务才可以创建定时任务</div>
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">调度计划</label>
	    <div class="layui-input-block">
	      <textarea name="job.jobDesc" id="descInput" class="layui-textarea" readonly="readonly" lay-verify="required"  onclick="inputCron();" placeholder="配置调度计划"></textarea>
			<input type="text" name="job.cron" id="cronInput" style="margin-top: 10px;" class="layui-input input-xlarge" readonly="readonly" >
			<input type="hidden" name="job.beginDate" id="beginDateInput" class="input-xlarge">
			<input type="hidden" name="job.endDate" id="endDateInput" class="input-xlarge">
			<input type="hidden" name="job.beginTime" id="beginTimeInput" class="input-xlarge">
			<input type="hidden" name="job.endTime" id="endTimeInput" class="input-xlarge">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">属于应用</label>
	    <div class="layui-input-block">
	      <select name="job.appName" lay-verify="required" class="layui-select" data-mars="job.appDict">
				<option value="平台">平台</option>
	      </select>
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <div class="layui-input-block">
	      <button type="button" id="submitbut" lay-submit lay-filter="submitbut" class="layui-btn layui-btn-small layui-btn-normal" style="padding:0 20px">保存</button>
	      <button type="button" onclick="back()" class="layui-btn layui-btn-small layui-btn-primary" style="padding:0 20px">返回</button>
	    </div>
	  </div>
	</form>
</div>

 
<script>
layui.use('form', function(){
	  var form = layui.form;
	  //监听提交
	  form.on('submit(submitbut)', function(data){
		  save(data.field);
	  });
	  $(function () {
		  $("#easyform").render({success:function(result){
			  form.render('select'); //刷新select选择框渲染
		  }});
	  });         
});

	  function save(data) {
		ajax.remoteCall("${ctxPath}/servlet/job?action=save",
				data,
				function(result) { 	
					if(result.state==1){
						layer.msg(result.msg,{icon:1,time:800},function(){
							layer.closeAll();
							loadPage("job/job-list.jsp",{});
						});
					}else{
						layer.alert(result.msg);
					}
				}
		);
	}
	
	//返回按钮
	function back() {
		loadPage("job/job-list.jsp",{});
	}
	
	//填写cron表达式
	var cronTab = false;
	function inputCron() {
		if(cronTab){
			return;
		}
		cronTab = true;
		popup.layerShow({type:1,title:'调度计划',offset:'auto',area:['865px','600px'],btn: ['确定', '关闭']
		        ,yes: function(index){
		          	layer.closeAll();
		          	cronTab = false;
		          	
		        },btn2: function(){
		          	layer.closeAll();
		        	cronTab = false;
		        }
	        }
			,"${ctxPath}/pages/job/task-edit.jsp"
			,{}
		);
		cronTab = false;
	}
	
	//回填cron
	function callbackCron(cron, jobDesc, dataIntervalObj, timeIntervalObj) {
		
		$("#cronInput").val(cron);
		$("#descInput").val(jobDesc);
		
		if(null != dataIntervalObj && null != timeIntervalObj) {
			$("#beginDateInput").val(dataIntervalObj.beginDate);
			$("#endDateInput").val(dataIntervalObj.endDate);
			$("#beginTimeInput").val(timeIntervalObj.beginTime);
			$("#endTimeInput").val(timeIntervalObj.endTime);
		}
	}
</script>
