package org.easitline.console.command;
public class OS {
    /**
     * 
     */
    public static final String NAME = System.getProperty("os.name");

    /**
     * 
     */
    public static final boolean LINUX = (NAME.indexOf("Linux") > -1);

    /**
     * 
     */
    public static final boolean WINDOWS = (NAME.indexOf("Windows") > -1);

    /**
     * @param cmd
     * @return String
     */
    public static String getCMD(String cmd) {
        if(WINDOWS) {
            return "\"" + cmd + "\"";
        }
        return cmd;
    }
}

