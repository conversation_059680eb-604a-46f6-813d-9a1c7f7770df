package org.easitline.console.dao;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceRegistor;
import org.easitline.console.base.AppDaoContext;
import org.easitline.console.base.Constants;
import org.easitline.console.job.JobLogManager;
import org.easitline.console.job.JobManager;
import org.easitline.console.vo.AppJobModel;
import org.easitline.console.vo.AppJobRowMapper;
import org.easitline.console.vo.JobLogModel;
import org.quartz.CronTrigger;
import org.quartz.Job;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.SimpleTrigger;
import org.quartz.Trigger;
import org.quartz.TriggerKey;
import org.quartz.impl.StdSchedulerFactory;
import org.quartz.impl.matchers.GroupMatcher;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

@WebObject(name="job")
public class JobDao extends AppDaoContext {
	@WebControl(name="list",type= Types.LIST)
	public JSONObject list(){
		String sql = "select t1.* from EASI_JOB_STEP t1 ";
		List<AppJobModel> list = null;
		Object[] params = new Object[]{};
		/*String nodeName = ServerContext.getNodeName();
		if(null != nodeName && !"".equals(nodeName)) {
			sql += " where t1.server_name=?";
			params = new Object[]{nodeName};
		}*/
		try {
			list = this.getSysDefaultQuery().queryForList(sql, params,new AppJobRowMapper());
		} catch (SQLException e) {
			e.printStackTrace();
		}
		if(list == null){
			list = new ArrayList<AppJobModel>();
		}
		JSONObject res = new JSONObject();
		res.put("data", list);
		res.put("total", list.size());
		return res;
	}
	
	@WebControl(name="quartzJobList",type= Types.LIST)
	public JSONObject quartzJobList(){
		JSONArray array = new JSONArray();
		 try {
			StdSchedulerFactory schedulerFactory = new StdSchedulerFactory();
            Scheduler scheduler = schedulerFactory.getScheduler();
            
            for (String groupName : scheduler.getJobGroupNames()) {
                for (JobKey jobKey : scheduler.getJobKeys(GroupMatcher.jobGroupEquals(groupName))) {
                	JobDetail jobDetail = scheduler.getJobDetail(jobKey);
                    List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);
                    for (Trigger trigger : triggers) {
                        Trigger.TriggerState triggerState = scheduler.getTriggerState(trigger.getKey());
                        TriggerKey triggerKey = trigger.getKey();
                        JSONObject row = new JSONObject();
                        row.put("TriggerGroup",triggerKey.getGroup());
                        row.put("TriggerName",triggerKey.getName());
                        row.put("State",triggerState);
                        row.put("Description",trigger.getDescription());
                        row.put("NextFireTime",trigger.getNextFireTime());
                        
                        // 获取任务的名称和组
                        String jobName = jobDetail.getKey().getName();
                        String jobGroup = jobDetail.getKey().getGroup();
                        // 获取任务的类信息
                        Class<? extends Job> jobClass = jobDetail.getJobClass();
                        row.put("JobName",jobName);
                        row.put("JobGroup",jobGroup);
                        row.put("JobClass",jobClass.getName());
                        
                        if (trigger instanceof CronTrigger) {
                            CronTrigger cronTrigger = (CronTrigger) trigger;
                            String cronExpression = cronTrigger.getCronExpression();
                            row.put("cronExpression",cronExpression);
                        } else if (trigger instanceof SimpleTrigger) {
                            SimpleTrigger simpleTrigger = (SimpleTrigger) trigger;
                            int repeatCount = simpleTrigger.getRepeatCount();
                            int repeatInterval = (int) simpleTrigger.getRepeatInterval();
                            row.put("cronExpression","重复次数：" + repeatCount + ", 重复间隔：" + repeatInterval);
                        }
                        array.add(row);
                    }
                }
            }
        } catch (SchedulerException e) {
           this.error(e.getMessage(), e);
        }
		return getJsonResult(array);
	}
	
	
	@WebControl(name="jobLogList",type= Types.LIST)
	public JSONObject jobLogList(){
		JobLogManager jobLogManager = new JobLogManager();
		String jobId = param.getString("jobId");
		List<JobLogModel> jobLogList = jobLogManager.getJobLogListByJobId(jobId);
		if(jobLogList == null){
			jobLogList = new ArrayList<JobLogModel>();
		}
		JSONObject res = new JSONObject();
		res.put("data", jobLogList);
		res.put("total", jobLogList.size());
		return res;
	}
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		JobManager jobManager = new JobManager();
		String jobId = param.getString("jobIdInput");
		AppJobModel jobObj = null;
		try {
			jobObj = jobManager.getJobById(jobId);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		if(jobObj ==null ){
			jobObj = new AppJobModel();
		}
		JSONObject result = new JSONObject();
		result.put("data", jobObj);
		return result;
	}
	
	@WebControl(name="serviceDict",type=Types.DICT)
	public JSONObject serviceDict(){
		List<ServiceResource> serviceList = ServiceRegistor.getRegistor().listAllServices();
		Map<String, String> dicts = new LinkedHashMap<>();
		for(ServiceResource service : serviceList) {
			String serviceId = service.serviceId;
			if(null != serviceId && serviceId.toUpperCase().startsWith(Constants.CRON_SERVICE_PREFIX)) {
				dicts.put(service.serviceId, service.serviceName);
			}
		}
		JSONObject result = new JSONObject();
		result.put("total", Integer.valueOf(dicts.size()));
		result.put("data", dicts);
		return result;
	}
	
	@WebControl(name="appDict",type=Types.DICT)
	public JSONObject appDict(){
		return getDictByQuery("select APP_NAME as APP_ID,APP_NAME from EASI_APP_INFO", new Object[]{});
	}
}
