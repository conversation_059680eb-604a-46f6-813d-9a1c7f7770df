package org.easitline.console.log;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileFilter;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import org.easitline.common.core.Globals;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.console.base.Constants;
import org.easitline.console.listener.GlobalContextListener;

/**
 * Mars统一日志服务，提供把模块的日志放到solor中。
 * <AUTHOR>
 *
 */
public class LogBackupServer implements Runnable{
	
	
	@Override
	public void run() {
		
		try {
			Thread.sleep(20*1000);
		} catch (Exception ex) {
			LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(ex.getMessage(),ex);
		}
		
		try {
			Thread bakThread = new Thread(new BackFileThread());
			bakThread.start();
		} catch (Exception e) {
			LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(e.getMessage(),e);
		}
		
		while(GlobalContextListener.runState){
			if(!this.isRun()){
				try {
					Thread.sleep(10*1000);
				} catch (InterruptedException e) {
					LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(e.getMessage(),e);
					e.printStackTrace();
				}
			}
			try {
				if(this.checkBakLogState()) 	this.checkNewCatalinaLog();
			} catch (Exception ex) {
				LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(ex,ex);
			}
			
			try {
				clearHistoryLog();
			} catch (Exception ex) {
				LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(ex.getMessage(),ex);
			}
			
			try {
				Thread.sleep(10*1000);
			} catch (InterruptedException e) {
				LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(e.getMessage(),e);
			}
			
		}
	}
	
	public void  clearHistoryLog(){
		int logSaveDay = Integer.valueOf(ServerContext.getProperties("logSaveDay", "3"));
		if(logSaveDay <= 0) return ;
		
		EasyCalendar cal = EasyCalendar.newInstance();
		cal.add(EasyCalendar.DAY, logSaveDay*-1);
		String  dateId = cal.getDateInt()+"";
		
		String bakPath = ServerContext.getProperties("bakToDir", Globals.SERVER_DIR+File.separator+"logbak");
		File   bakDir = new File(bakPath);
		if(!bakDir.isDirectory()) return ;
		
		File[] dirs = bakDir.listFiles(new FileFilter() {
			@Override
			public boolean accept(File pathname) {
				if(!pathname.isDirectory()) return false;
				String dirname = pathname.getName();
				//如果目录日期，比 清理的日期小，则执行清理操作。
				if(dirname.compareTo(dateId)<0){
					return true;
				}
				return false;
			}
		});
		
		for(File dir : dirs){
			clearLogDir(dir);
		}
	}
	
	public void clearLogDir(File dir){
		String pathname = dir.getName();
		//防止删除目录
		if(!pathname.startsWith("20")) return ;
		File[] logfiles = dir.listFiles();
		for(File file:logfiles){
			String filename = file.getName();
			if(filename.startsWith("catalina")){
				file.delete();
			}
		}
		dir.delete();
	}
	
	public boolean isRun(){
		String bakLogFlag = ServerContext.getProperties("bakLogFlag", "0");
    	return "1".equals(bakLogFlag);
	}
	
	public String getCurrentTime(){
		try {
			Thread.sleep(2*1000);
		} catch (Exception ex) {
			LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(ex.getMessage(),ex);
		}
		String datetime = EasyCalendar.newInstance().getDateTime("-");
		datetime =  datetime.replaceAll("-", "");
		datetime =  datetime.replaceAll(" ", "");
		datetime =  datetime.replaceAll(":", "");
		return datetime;
	}
	
	/**
	 * 检查新的Catalina日志，并执行改名
	 */
	public void checkNewCatalinaLog(){
		String sourceLog = Globals.BASE_DIR+File.separator+"logs";
		File file = new File(sourceLog);
		File[] files = file.listFiles(new FileFilter() {
			@Override
			public boolean accept(File pathname) {
				if(!pathname.isFile()) return false;
				return pathname.getName().startsWith("catalina.out.");
			}
		});
		for(File _file : files){
			String newFileName = _file.getPath().split("catalina.out")[0]+"catalina.out_"+getCurrentTime();
			File newFile = new File(newFileName);
			LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info("[LogBackupServer]重命名日志文件 -> 源文件:"+_file.getPath()+",目标文件:"+newFile.getPath());
			_file.renameTo(newFile);
		}
	}
	
	
	/**
	 * 检查日志备份执行状态，如果备份日志超过20个日志文件没有做迁移，不做重命名处理。
	 * @return
	 */
	public boolean checkBakLogState(){
		String sourceLog = Globals.BASE_DIR+File.separator+"logs";
		File file = new File(sourceLog);
		if(!file.isFile()) return false;
		File[] files = file.listFiles(new FileFilter() {
			@Override
			public boolean accept(File pathname) {
				if(!pathname.isFile()) return false;
				return pathname.getName().startsWith("catalina.out_");
			}
		});
		if(files == null) return false;
		if(files.length>=20) {
			LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info("[LogBackupServer]日志重命名暂停执行，原因： 超过20个catalina.out_日志文件没有被迁移。");
			return false;
		}
		return true;
	}
	
	/**
	 * 执行日志备份
	 */
	public void backupCatalinaLog(){
		String sourceLog = Globals.BASE_DIR+File.separator+"logs";
		File file = new File(sourceLog);
		if(!file.isFile()) return;
		File[] files = file.listFiles(new FileFilter() {
			@Override
			public boolean accept(File pathname) {
				if(!pathname.isFile()) return false;
				return pathname.getName().startsWith("catalina.out_");
			}
		});
		if(files == null) return;
		for(File _file : files){
			zipCatalinaLog(_file);
		}
	}
	
	/**
	 * 把日志文件压缩成zip文件，并存储到目标的目录。
	 * @param srcFile
	 */
	public void zipCatalinaLog(File srcFile){
		String bakPath = ServerContext.getProperties("bakToDir", Globals.SERVER_DIR+File.separator+"logbak");
		if(!bakPath.endsWith(File.separator)) bakPath = bakPath + File.separator;
		bakPath =  bakPath + EasyCalendar.newInstance().getDateInt()+File.separator;
		File bakDir = new File(bakPath);
		if(!bakDir.isDirectory()) bakDir.mkdirs();
		String backFilename = bakPath + srcFile.getName()+".zip";
		File targetFile = new File(backFilename);
		try {
			long timer = System.currentTimeMillis();
			this.zip(srcFile, targetFile);
			timer = System.currentTimeMillis() - timer;
			srcFile.delete();
			LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info("[LogBackupServer]日志备份耗时："+timer+"ms,删除原日志文件 -> "+srcFile.getPath());
		} catch (Exception ex) {
			LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info("[LogBackupServer] 备份日志文件失败，原因："+ ex.getMessage(),ex);
		}
	}
	
	private void zip(File srcFile, File targetFile) throws Exception {
		LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info("[LogBackupServer]备份日志文件 -> 源文件:"+srcFile.getPath()+",目标文件:"+targetFile.getPath());
		FileInputStream fis = null;
		BufferedInputStream bis = null;
		FileOutputStream fos = null;
		BufferedOutputStream bos = null;
		ZipOutputStream zos = null;// 压缩包
		try {
			fis = new FileInputStream(srcFile);
			bis = new BufferedInputStream(fis);
			fos = new FileOutputStream(targetFile);
			bos = new BufferedOutputStream(fos);
			zos = new ZipOutputStream(bos);// 压缩包
			ZipEntry ze = new ZipEntry(srcFile.getName());// 这是压缩包名里的文件名
			zos.putNextEntry(ze);// 写入新的 ZIP 文件条目并将流定位到条目数据的开始处
			byte[] buf = new byte[1024];
			int len;
			while ((len = bis.read(buf)) != -1) {
				zos.write(buf, 0, len);
				zos.flush();
			}
		} catch (Exception ex) {
			LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info("[LogBackupServer] 备份日志文件失败，原因："+ ex.getMessage(),ex);
		} finally {
			try {  if(zos!=null) zos.close(); } catch (Exception e) { }
			try {  if(bos!=null) bos.close(); } catch (Exception e) { }
			try {  if(fos!=null) fis.close(); } catch (Exception e) { }
			try {  if(bis!=null) fis.close(); } catch (Exception e) { }
			try {  if(fis!=null) fis.close(); } catch (Exception e) { }
		}
	}
	
	
	private class BackFileThread implements Runnable{

		@Override
		public void run() {
			while(GlobalContextListener.runState){
				
			   if(!isRun()){
					try {
						Thread.sleep(10*1000);
					} catch (InterruptedException e) {
						LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(e.getMessage(),e);
					}
				}
				
				try {
					backupCatalinaLog();
				} catch (Exception ex) {
					LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(ex.getMessage(),ex);
				}finally{
					try {
						Thread.sleep(10*1000);
					} catch (Exception ex2) {
						LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(ex2.getMessage(),ex2);
					}
				}
				
			}
		}
		
		
	}
	
	public static void main(String[] args) {
		String str1 = "20200101";
		String str2 = "20200102";
		int result = str1.compareTo(str2);
		System.out.println(result);
	}


}
