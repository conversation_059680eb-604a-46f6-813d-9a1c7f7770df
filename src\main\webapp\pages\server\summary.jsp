<%@ page language="java" contentType="text/html; charset=UTF-8"   pageEncoding="UTF-8"%>
              <div id="html"></div>
             <script id="list-template" type="text/x-jsrender">
            		   <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
 									 <legend>SERVER</legend>
						</fieldset>
						<div class="layui-form">
						  <table  class="layui-table" lay-even="" lay-skin="row">
						     <colgroup>
						      <col width="250">
						      <col>
						    </colgroup>
						    <tbody>
						      <tr>
						        <td>版本号</td>
						        <td>{{:data.version}}</td>
						      </tr>
						      <tr>
						        <td>发布日期</td>
						        <td>{{:data.versionDate}}</td>
						      </tr>
						      <tr>
						        <td>启动时间</td>
						        <td>{{:data.bootTime}}</td>
						      </tr>
						      <tr>
						        <td>已运行时间</td>
						        <td>{{:data.runtime}}</td>
						      </tr>
						      <tr>
						        <td>进程PID</td>
						        <td>{{:data.pid}} <a style="display:none" href="javascript:stopServer()">停止进程<a> <a style="display:none" href="javascript:restartServer()">重启服务</a></td>
						      </tr>
						      <tr>
						        <td>JAVA版本</td>
						        <td>{{:data.javaVendor}} {{:data.javaVmVersion}}</td>
						      </tr>
						      <tr>
						        <td>JAVA路径</td>
						        <td>{{:data.javaHome}}</td>
						      </tr>
						      <tr>
						        <td>用户目录</td>
						        <td>{{:data.userDir}}</td>
						      </tr>
						      <tr>
						        <td>用户名</td>
						        <td>{{:data.userName}}</td>
						      </tr>
						      <tr>
						        <td>深度加密</td>
						        <td>
						            {{:data.cryptorFlag}}
						            {{if data.cryptorFlag=='启用'}}
						                {{:data.cryptorState}}
						            {{/if}}
						        </td>
						      </tr>
						      <tr>
						        <td>操作系统</td>
						        <td>{{:data.osInfo}}</td>
						      </tr>
						      <tr>
						        <td>IP地址</td>
						        <td>{{:data.address}}</td>
						      </tr>
						      <tr>
						        <td>总内存/已使用/剩余</td>
						        <td>{{:data.memFree}}</td>
						      </tr>
						      <tr>
						        <td>应用服务器</td>
						        <td>{{:data.serverInfo}}</td>
						      </tr>
						      <tr>
						        <td>应用目录</td>
						        <td>{{:data.catalinaBase}}</td>
						      </tr>
						      <tr>
						        <td>机器唯一标识码</td>
						        <td>{{:data.machineCode}}</td>
						      </tr>
						    </tbody>
						  </table>
						</div>

            		   <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
 									 <legend>JVM</legend>
						</fieldset>
						<div class="layui-form">
						  <table  class="layui-table" lay-even="" lay-skin="row">
								<colgroup>
				      		<col width="250">
				     			<col>
				    		</colgroup>
						    <tbody>
						      <tr>
						        <td>JVM版本</td>
						        <td>{{:data.javaVm}}</td>
						      </tr>
						      <tr>
						        <td>分配JVM内存</td>
						        <td>{{:data.maxMemory}}</td>
						      </tr>
						      <tr>
						        <td>JVM可用内存</td>
						        <td>{{:data.freeMemory}}</td>
						      </tr>
						      <tr>
						        <td>JVM内存使用率</td>
						        <td>{{:data.memoryUsedPercent}}</td>
						      </tr>
						      <tr>
						        <td>创建线程数</td>
						        <td>{{:data.totalThread}}</td>
						      </tr>
						      <tr>
						        <td>JVM老年代内存</td>
						        <td>{{:data.memoryPoolInfo}} <a style="display:none;" onclick="memoryGc(this);" href="javascript:;">强制回收</a></td>
						      </tr>
						    </tbody>
						  </table>
						</div>

            		   <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
 							 <legend>磁盘空间</legend>
						</fieldset>
						<div class="layui-form">
						 <table  class="layui-table" lay-even="" lay-skin="row">
						 <thead>
						    <tr>
						      <th>设备名称</th>
						      <th>总用量</th>
						      <th>已使用用量</th>
						      <th>有效用量</th>
						    </tr> 
						  </thead>
						    <tbody>
 							{{for data.fileSystemInfo}}
						      <tr>
						        <td>{{:devName}}</td>
						        <td>{{:total}}</td>
						        <td>{{:used}}</td>
						        <td>{{:avail}}</td>
						      </tr>
							{{/for}}
						    </tbody>
						  </table>
						</div>
            		   <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
 							 <legend>CPU使用情况</legend>
						</fieldset>
						<div class="layui-form">
						 <table  class="layui-table" lay-even="" lay-skin="row">
						 <thead>
						    <tr>
						      <th>CPU信息</th>
						      <th>CPU用户使用率</th>
						      <th>CPU系统使用率</th>
						      <th>CPU当前错误率</th>
						      <th>CPU当前空闲率</th>
						      <th>CPU总的使用率</th>
						    </tr> 
						  </thead>
						    <tbody>
 							{{for data.cpuInfo}}
						      <tr>
						        <td>{{:model}}</td>
						        <td>{{:user}}</td>
						        <td>{{:sys}}</td>
						        <td>{{:nice}}</td>
						        <td>{{:idle}}</td>
						        <td>{{:combined}}</td>
						      </tr>
							{{/for}}
						    </tbody>
						  </table>
						</div>
	             </script>
	       
<script type="text/javascript">

	function loadSummaryInfo() {
			var url = "${ctxPath}/servlet/serverInfo?action=serverInfo"; 
			$.ajax({
				url : url,
				type : 'post',
				cache:false,
				dataType : 'json',
				contentType : "application/x-www-form-urlencoded; charset=UTF-8",
				data : {},
				success : function(result) {
					var template = $.templates("#list-template");
		       	    var htmlOutput = template.render(result);
		       	    $("#html").html(htmlOutput);
				},error : function(jqXHR, textStatus, errorThrown) {
					console.error("HTTP Status "  + jqXHR.status + " - " +textStatus+ " - "+url+"");
					var template = $.templates("#list-template");
		       	    var htmlOutput = template.render({data:{}});
		       	    $("#html").html(htmlOutput);
				}
			});
	}
	function updateVersion() {
			var url = "${ctxPath}/servlet/serverInfo?query=getVersion"; 
			$.ajax({
				url : url,
				type : 'post',
				cache:false,
				dataType : 'json',
				contentType : "application/x-www-form-urlencoded; charset=UTF-8",
				data : {},
				success : function(result) {
					layer.msg(result.msg);
				},error : function(jqXHR, textStatus, errorThrown) {
					console.error("HTTP Status "  + jqXHR.status + " - " +textStatus+ " - "+url+"");
				}
			});
	}
	$(function(){
		loadSummaryInfo();
		//updateVersion();
	});
	
	function memoryGc(){
		layer.confirm("确认执行吗，请勿业务高峰期执行，执行中系统可能会卡顿1-2秒",{icon: 3},function(){
			ajax.remoteCall("${ctxPath}/servlet/serverInfo?action=gc",{},function(result){  
				if(result.state==1){
					layer.msg(result.msg,{icon:7,offset:'20px'},function(){
						location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		});
	}
	
	function restartServer(){
		ajax.remoteCall("${ctxPath}/servlet/serverInfo?action=restartServer",{},function(result){  
			if(result.state==1){
				layer.alert("正在执行,若几分钟内服务没成功重启或没响应请手动重启服务。",{icon: 1,time:10000},function(){
					layer.closeAll();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
	
	function stopServer(){
		ajax.remoteCall("${ctxPath}/servlet/serverInfo?action=stopServer",{},function(result){  
			if(result.state==1){
				layer.alert("正在执行,若几分钟内服务没停止,请手动停止服务。",{icon: 1,time:1000},function(){
					layer.closeAll();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}

</script>
