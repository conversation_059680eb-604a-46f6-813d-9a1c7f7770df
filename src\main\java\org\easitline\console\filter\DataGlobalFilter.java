package org.easitline.console.filter;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.easitline.common.core.log.LogEngine;
import org.easitline.common.utils.kit.WebKit;

@WebFilter("/*")
public class DataGlobalFilter implements Filter {

	private static List<String> sensitiveKeywords = Arrays.asList(
		"webapps", "web-inf", "meta-inf", "/..", "..%2f", "%2e%2e", "%252e%252e","easyserver"
	);

	
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        String keywordsParam = filterConfig.getInitParameter("sensitiveKeywords");
        if (keywordsParam != null && !keywordsParam.trim().isEmpty()) {
            String[] arr = keywordsParam.split(",");
            sensitiveKeywords = new java.util.ArrayList<>();
            for (String s : arr) {
                if (!s.trim().isEmpty()) {
                    sensitiveKeywords.add(s.trim());
                }
            }
        }
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain)
            throws IOException, ServletException {

        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        String fullURL = request.getRequestURL().toString();
        String queryString = request.getQueryString();
        if (queryString != null) {
            fullURL += "?" + queryString;
        }

        String lowerUrl = fullURL.toLowerCase();
        for (String keyword : sensitiveKeywords) {
            if (lowerUrl.contains(keyword.toLowerCase())) {
                this.sendMsg(WebKit.getIP(request),lowerUrl,lowerUrl+"包含["+keyword+"]关键字攻击,已被拦截");
                response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                response.setContentType("text/plain;charset=UTF-8");
                response.getWriter().write("Access Denied: Your request contains illegal content.");
                response.getWriter().flush();
                return;
            }
        }

        // 新增POST参数检测
        if ("POST".equalsIgnoreCase(request.getMethod())) {
            String contentType = request.getContentType();
            if (contentType != null && contentType.contains("form-data")) {
                boolean hasSensitive = false;
                try {
                    for (javax.servlet.http.Part part : request.getParts()) {
                        String filename = part.getSubmittedFileName();                        
                        if (filename != null) {
                            String hitKeyword = getSensitiveKeyword(filename);
                            if (hitKeyword != null) {
                                LogEngine.getLogger("yc-attack").error(String.format("[拦截] IP=[%s] URL=[%s] 文件名=[%s] 命中敏感词=[%s]，原因=文件上传名包含敏感关键字，已被拦截。", WebKit.getIP(request), fullURL, filename, hitKeyword));
                                hasSensitive = true;
                                break;
                            }
                        }
                    }
                } catch (Exception e) {
                    // 解析异常，默认放行，但可记录异常日志
                    LogEngine.getLogger("yc-attack").warn(String.format("[警告] IP=[%s] URL=[%s] form-data解析异常：%s", WebKit.getIP(request), fullURL, e.getMessage()));
                }
                if (hasSensitive) {
                    this.sendMsg(WebKit.getIP(request), fullURL, "form-data文件名包含敏感关键字,已被拦截");
                    response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                    response.setContentType("text/plain;charset=UTF-8");
                    response.getWriter().write("Access Denied: Your request contains illegal content.");
                    response.getWriter().flush();
                    return;
                }
                chain.doFilter(request, servletResponse);
                return;
            }
            chain.doFilter(request, servletResponse);
            return;
        }
        chain.doFilter(servletRequest, servletResponse);
    }


    @Override
    public void destroy() {
        // 销毁方法，如有需要可添加清理逻辑
    }
    
    private void sendMsg(String remoteAddr,String requestUri,String requestParam) {
    	String eventDesc = "截获疑似攻击，攻击者IP地址:" + remoteAddr + ",访问URL:" + requestUri;
    	LogEngine.getLogger("yc-attack").error(eventDesc);
    }

    // 优化敏感词检测，返回命中的敏感词
    private String getSensitiveKeyword(String text) {
        if (text == null) return null;
        String lower = text.toLowerCase();
        for (String keyword : sensitiveKeywords) {
            if (lower.contains(keyword.toLowerCase())) {
                return keyword;
            }
        }
        return null;
    }
}
