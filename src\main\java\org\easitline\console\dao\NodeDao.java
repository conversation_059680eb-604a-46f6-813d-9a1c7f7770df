package org.easitline.console.dao;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServerContext;
import org.easitline.console.base.AppDaoContext;

import com.alibaba.fastjson.JSONObject;

@WebObject(name = "NodeDao")
public class NodeDao extends AppDaoContext {
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		String nodeId = param.getString("nodeId");
		JSONObject object = queryForRecord("select * from easi_node where node_id = ?", nodeId);
		object.put("securityKey",ServerContext.getProperties("SECURITY_KEY",""));
		return object;
	}
	
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		JSONObject object = queryForList("select * from easi_node",new Object[]{});
		object.put("securityKey",ServerContext.getProperties("SECURITY_KEY",""));
		return object;
	}
	
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		return getDictByQuery("select NODE_ID,NODE_NAME from easi_node");
	}

}
