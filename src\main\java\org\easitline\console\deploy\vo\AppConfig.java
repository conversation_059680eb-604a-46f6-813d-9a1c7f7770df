package org.easitline.console.deploy.vo;

/**
 * 应用配置对象
 * <AUTHOR>
 * @date   2016-09-20
 */
public class AppConfig {

	private String appId;     //应用标识
	private String version;   //应用版本
	private String itemId;    //配置标识
	private String itemName;  //配置名称
	private String itemKey;   //配置键
	private String pItemKey;   //父级ItemKey
	private String itemJson;  // 配置JSON
	private String itemValue; //配置值
	private String itemDesc;  //配置描述
	private String itemType;  //配置项类型
	private String orderIndex;  //排序值
	
	public String getAppId() {
		return appId;
	}
	public void setAppId(String appId) {
		this.appId = appId;
	}
	public String getItemName() {
		return itemName;
	}
	public void setItemName(String itemName) {
		this.itemName = itemName;
	}
	public String getItemValue() {
		return itemValue;
	}
	public void setItemValue(String itemValue) {
		this.itemValue = itemValue;
	}
	public String getItemDesc() {
		return itemDesc;
	}
	public void setItemDesc(String itemDesc) {
		this.itemDesc = itemDesc;
	}
	public String getItemType() {
		return itemType;
	}
	public void setItemType(String itemType) {
		this.itemType = itemType;
	}
	public String getItemKey() {
		return itemKey;
	}
	public void setItemKey(String itemKey) {
		this.itemKey = itemKey;
	}
	public String getVersion() {
		return version;
	}
	public void setVersion(String version) {
		this.version = version;
	}
	public String getItemId() {
		return itemId;
	}
	public void setItemId(String itemId) {
		this.itemId = itemId;
	}
	public String getItemJson() {
		return itemJson==null?"":itemJson;
	}
	public void setItemJson(String itemJson) {
		this.itemJson = itemJson;
	}
	public String getOrderIndex() {
		return orderIndex;
	}
	public void setOrderIndex(String orderIndex) {
		this.orderIndex = orderIndex;
	}
	public String getpItemKey() {
		return pItemKey;
	}
	public void setpItemKey(String pItemKey) {
		this.pItemKey = pItemKey;
	}
	
	
}
