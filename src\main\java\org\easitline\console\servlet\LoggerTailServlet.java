package org.easitline.console.servlet;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.RandomAccessFile;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.Globals;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.console.base.Constants;

@WebServlet("/servlet/logger/tail/*")
public class LoggerTailServlet extends HttpServlet{
	private static final long serialVersionUID = 1L;
	// 文件位置缓存，记录上次读取位置
	private static final java.util.Map<String, Long> FILE_POSITION_CACHE = new java.util.concurrent.ConcurrentHashMap<>();
	// 最大缓存时间（分钟）
	private static final int MAX_CACHE_MINUTES = 30;
	// 缓存过期时间
	private static final java.util.Map<String, Long> CACHE_EXPIRY_TIME = new java.util.concurrent.ConcurrentHashMap<>();
	// 每批处理的最大行数，防止内存溢出
	private static final int MAX_BATCH_SIZE = 5000;
	// 默认每页大小
	private static final int DEFAULT_PAGE_SIZE = 10;
	// grep搜索缓存存活时间（秒）
	private static final int GREP_CACHE_TTL = 300; // 5分钟
	// grep搜索结果缓存
	private static final java.util.Map<String, GrepSearchCache> GREP_SEARCH_CACHE = new java.util.concurrent.ConcurrentHashMap<>();
	// grep搜索缓存最大条目数
	private static final int MAX_GREP_CACHE_ENTRIES = 50;
	// 缓存清理锁，防止并发清理导致线程安全问题
	private static final Object CACHE_CLEAN_LOCK = new Object();
	// 允许处理的最大文件大小（MB），0表示无限制
	private static final int MAX_FILE_SIZE_MB = 200; // 限制为200MB
	// 单次grep操作的最长执行时间（毫秒）
	private static final int GREP_TIMEOUT_MS = 45000; // 45秒
	// 正则表达式最大长度
	private static final int MAX_REGEX_LENGTH = 500;
	// 正则表达式复杂性限制 - 量词的最大数量
	private static final int MAX_QUANTIFIERS = 5;

	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		Render.renderHtml(request, response, "GET requests are not supported");
	}
	
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
    	response.setContentType("text/plain; charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        
        // 基本参数读取和校验
        int linesToRead = 20;
        String linesToReadStr = request.getParameter("readLine");
        if(StringUtils.isNotBlank(linesToReadStr)) {
            try {
        	    linesToRead = Integer.valueOf(linesToReadStr);
            } catch (NumberFormatException e) {
                // 使用默认值
            }
        }
        if(linesToRead>200) {
        	linesToRead = 200;
        }
        
        String name = request.getParameter("name");
        String source = request.getParameter("source");
        
        // 安全检查 - 防止路径遍历
        if (name != null && (name.contains("../") || name.contains("..\\") || 
            name.contains("/..") || name.contains("\\..") || name.startsWith("/"))) {
            LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("拒绝访问：检测到可能的路径遍历尝试 - " + name);
            response.getWriter().println("拒绝访问：非法的日志名称");
            return;
        }
        
        // 白名单检查 - 仅允许source为base或app
        if (!"base".equals(source) && !"app".equals(source)) {
            LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("拒绝访问：无效的source参数 - " + source);
            response.getWriter().println("拒绝访问：无效的source参数");
            return;
        }

        String action = request.getParameter("action");
        String grepPattern = request.getParameter("grep");
        boolean useGrep = StringUtils.isNotBlank(grepPattern);
        
        // 正则表达式安全检查
        if (useGrep) {
            // 检查长度限制
            if (grepPattern.length() > MAX_REGEX_LENGTH) {
                LogEngine.getLogger(Constants.EASITLINE_CONSOLE).warn("正则表达式过长: " + grepPattern.length() + " 字符");
                response.getWriter().println("正则表达式过长，请限制在 " + MAX_REGEX_LENGTH + " 字符以内");
                return;
            }
            
            // 检查可能的灾难性回溯模式
            int quantifierCount = countRegexQuantifiers(grepPattern);
            if (quantifierCount > MAX_QUANTIFIERS) {
                LogEngine.getLogger(Constants.EASITLINE_CONSOLE).warn("检测到复杂正则表达式，量词数量: " + quantifierCount);
                response.getWriter().println("正则表达式过于复杂，包含过多量词 (*,+,?)，可能导致性能问题");
                return;
            }
        }
        
        boolean caseSensitive = "true".equals(request.getParameter("caseSensitive"));
        boolean followMode = "true".equals(request.getParameter("follow"));
        int contextLines = 0;
        String contextLinesStr = request.getParameter("context");
        if(StringUtils.isNotBlank(contextLinesStr)) {
            try {
                contextLines = Integer.parseInt(contextLinesStr);
            } catch (NumberFormatException e) {
                // 忽略错误，使用默认值0
            }
        }
        
        // 处理分页参数
        int page = 1;
        int pageSize = DEFAULT_PAGE_SIZE;
        String pageStr = request.getParameter("page");
        String pageSizeStr = request.getParameter("pageSize");
        
        if(StringUtils.isNotBlank(pageStr)) {
            try {
                page = Integer.parseInt(pageStr);
                if(page < 1) page = 1;
            } catch (NumberFormatException e) {
                // 使用默认值
            }
        }
        
        if(StringUtils.isNotBlank(pageSizeStr)) {
            try {
                pageSize = Integer.parseInt(pageSizeStr);
                if(pageSize < 1) pageSize = DEFAULT_PAGE_SIZE;
                if(pageSize > 50) pageSize = 50; // 限制每页最大数
            } catch (NumberFormatException e) {
                // 使用默认值
            }
        }
        
        if ("clear".equals(action)) {
            response.getWriter().println("clear");
            // 清除缓存
            clearPositionCache(name + source);
            return;
        }

        if ("clearCache".equals(action)) {
            // 清除所有缓存
            FILE_POSITION_CACHE.clear();
            CACHE_EXPIRY_TIME.clear();
            GREP_SEARCH_CACHE.clear();
            response.getWriter().println("所有缓存已清除");
            return;
        }

        if ("clearGrepCache".equals(action)) {
            // 清除grep搜索缓存
            GREP_SEARCH_CACHE.clear();
            response.getWriter().println("grep缓存已清除");
            return;
        }
       
        String LOG_FILE_PATH = null;
        
        if("base".equals(source)) {
        	LOG_FILE_PATH = Globals.BASE_DIR+File.separator+"logs"+File.separator+name;
        }else if("app".equals(source)) {
        	LOG_FILE_PATH = Globals.LOG_DIR+File.separator+name;
        }

        LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info("LOG_FILE_PATH:"+LOG_FILE_PATH);
        
        File file = new File(LOG_FILE_PATH);
        
        try {
            // 安全检查 - 确保文件在允许的目录内
            File baseDir = null;
            if("base".equals(source)) {
                baseDir = new File(Globals.BASE_DIR, "logs");
            } else {
                baseDir = new File(Globals.LOG_DIR);
            }
            
            // 检查文件是否在允许的目录内
            if (!file.getCanonicalPath().startsWith(baseDir.getCanonicalPath())) {
                LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("安全警告：尝试访问允许目录外的文件 - " + file.getCanonicalPath());
                response.getWriter().println("拒绝访问：文件位置不在允许的范围内");
                return;
            }
        } catch (Exception e) {
            LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("检查文件路径安全性时出错", e);
            response.getWriter().println("拒绝访问：无法验证文件安全性");
            return;
        }
        
        if(!file.exists()) {
        	LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("LoggerTail路径不存在："+LOG_FILE_PATH);
        	response.getWriter().println("路径不存在："+LOG_FILE_PATH);
            return;
        }
        
        // 检查文件是否过大
        long fileSizeMB = file.length() / (1024 * 1024);
        if (MAX_FILE_SIZE_MB > 0 && fileSizeMB > MAX_FILE_SIZE_MB * 2) {
            LogEngine.getLogger(Constants.EASITLINE_CONSOLE).warn("文件过大：" + fileSizeMB + "MB - " + LOG_FILE_PATH);
            response.getWriter().println("警告：文件大小(" + fileSizeMB + "MB)远超系统限制，可能导致操作缓慢或失败");
        }
        
        StringBuilder result = new StringBuilder();
        PrintWriter out = response.getWriter();
        String cacheKey = name + source;
        
        try (RandomAccessFile randomAccessFile = new RandomAccessFile(file, "r")) {
            if (useGrep) {
                try {
                    // 使用grep模式读取文件
                    readWithGrep(randomAccessFile, result, grepPattern, linesToRead, caseSensitive, contextLines, page, pageSize, LOG_FILE_PATH);
                } catch (PatternSyntaxException e) {
                    // 处理正则表达式语法错误
                    result.append("正则表达式错误: ").append(e.getMessage()).append("\n");
                    result.append("请检查您的正则表达式语法是否正确\n");
                    LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("正则表达式错误: " + e.getMessage(), e);
                } catch (OutOfMemoryError e) {
                    // 处理内存溢出错误
                    result.append("处理文件时内存不足，请尝试更精确的过滤表达式或减少行数\n");
                    LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error("Grep处理时内存溢出", e);
                }
            } else if (followMode) {
                // 使用类似tail -f的模式读取新增内容
                readFollowMode(randomAccessFile, result, linesToRead, cacheKey, file);
            } else {
                // 标准的tail读取
                readLastLines(randomAccessFile, result, linesToRead);
                // 更新文件位置缓存
                updatePositionCache(cacheKey, randomAccessFile.length());
            }
            
            result.append("\nReqtime：").append(EasyDate.getCurrentDateString()).append("，rows：").append(linesToRead);
            if (useGrep) {
                result.append("，grep：").append(grepPattern);
                if (contextLines > 0) {
                    result.append("，上下文行数：").append(contextLines);
                }
            }
            
        } catch (Exception e) {
        	LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(e.getMessage(),e);
        	out.print("读取文件时发生错误: " + e.getMessage());
        	return;
        }
        out.print(result.toString());
    }
    
    /**
     * 更新文件位置缓存
     */
    private void updatePositionCache(String key, long position) {
        FILE_POSITION_CACHE.put(key, position);
        // 设置过期时间
        CACHE_EXPIRY_TIME.put(key, System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(MAX_CACHE_MINUTES));
        // 清理过期的缓存
        cleanExpiredCache();
    }
    
    /**
     * 清理过期的缓存项
     */
    private void cleanExpiredCache() {
        // 使用同步块保证只有一个线程进行清理
        synchronized (CACHE_CLEAN_LOCK) {
            long currentTime = System.currentTimeMillis();
            List<String> keysToRemove = new ArrayList<>();
            
            // 找出过期的键
            for (java.util.Map.Entry<String, Long> entry : CACHE_EXPIRY_TIME.entrySet()) {
                if (entry.getValue() < currentTime) {
                    keysToRemove.add(entry.getKey());
                }
            }
            
            // 移除过期的缓存
            for (String key : keysToRemove) {
                FILE_POSITION_CACHE.remove(key);
                CACHE_EXPIRY_TIME.remove(key);
            }
            
            // 检查GREP_SEARCH_CACHE大小，执行LRU淘汰
            cleanGrepCacheIfNeeded();
        }
    }
    
    /**
     * 根据LRU策略清理grep缓存
     */
    private void cleanGrepCacheIfNeeded() {
        if (GREP_SEARCH_CACHE.size() <= MAX_GREP_CACHE_ENTRIES) {
            return; // 不需要清理
        }
        
        LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info("Grep缓存超出限制，当前大小: " + GREP_SEARCH_CACHE.size() + "，执行LRU清理");
        
        // 创建排序列表 - 按创建时间排序
        List<java.util.Map.Entry<String, GrepSearchCache>> entries = new ArrayList<>(GREP_SEARCH_CACHE.entrySet());
        entries.sort((e1, e2) -> Long.compare(e1.getValue().getCreationTime(), e2.getValue().getCreationTime()));
        
        // 移除最老的缓存项，直到达到目标大小
        int itemsToRemove = entries.size() - MAX_GREP_CACHE_ENTRIES;
        for (int i = 0; i < itemsToRemove; i++) {
            GREP_SEARCH_CACHE.remove(entries.get(i).getKey());
        }
        
        LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info("Grep缓存清理完成，当前大小: " + GREP_SEARCH_CACHE.size());
    }
    
    /**
     * 清除指定键的位置缓存
     */
    private void clearPositionCache(String key) {
        FILE_POSITION_CACHE.remove(key);
        CACHE_EXPIRY_TIME.remove(key);
    }
    
    /**
     * 标准的tail读取，获取文件最后n行
     */
    private void readLastLines(RandomAccessFile randomAccessFile, StringBuilder result, int linesToRead) throws IOException {
        long length = randomAccessFile.length();
        // 开始位置
        long position = length - 1;
        int linesCount = 0;
        
        while (position >= 0) {
            randomAccessFile.seek(position);
            byte b = randomAccessFile.readByte();
            if (b == '\n') { // 如果是换行符
                linesCount++; // 行数加一
                if (linesCount == linesToRead) { // 达到指定行数则停止
                    break;
                }
            }
            position--; // 继续向前移动
            if (position < 0) { 
                position = -1; // 到达文件开头时停止循环
                break;
            }
        }
        
        byte[] bytes = new byte[(int)(length - position)];
        randomAccessFile.seek(position + 1); 
        randomAccessFile.read(bytes);
        String lastLines= new String(bytes, StandardCharsets.UTF_8);
        result.append(lastLines);
    }
    
    /**
     * 类似tail -f模式，读取文件新增内容
     */
    private void readFollowMode(RandomAccessFile randomAccessFile, StringBuilder result, int defaultLines, String cacheKey, File file) throws IOException {
        // 获取缓存的位置，如果没有，则获取文件末尾
        long lastPosition = FILE_POSITION_CACHE.getOrDefault(cacheKey, -1L);
        long currentLength = file.length();
        
        // 如果文件被截断或首次访问，从文件末尾开始读取指定行数
        if (lastPosition == -1 || lastPosition > currentLength) {
            readLastLines(randomAccessFile, result, defaultLines);
            updatePositionCache(cacheKey, currentLength);
            return;
        }
        
        // 读取新增内容
        randomAccessFile.seek(lastPosition);
        StringBuilder newContent = new StringBuilder();
        String line;
        
        while ((line = randomAccessFile.readLine()) != null) {
            // 处理字符编码
            line = new String(line.getBytes("ISO-8859-1"), StandardCharsets.UTF_8);
            newContent.append(line).append("\n");
        }
        
        // 更新缓存位置
        updatePositionCache(cacheKey, randomAccessFile.getFilePointer());
        
        // 如果有新内容，添加到结果
        if (newContent.length() > 0) {
            result.append(newContent.toString());
        } else {
            result.append("没有新内容");
        }
    }
    
    /**
     * 使用grep模式读取文件，查找匹配的行
     */
    private void readWithGrep(RandomAccessFile randomAccessFile, StringBuilder result, String grepPattern,
                            int maxLines, boolean caseSensitive, int contextLines, int page, int pageSize, String filePath) throws IOException {
        // 检查文件大小是否超出限制
        if (MAX_FILE_SIZE_MB > 0) {
            long fileSizeInMB = randomAccessFile.length() / (1024 * 1024);
            if (fileSizeInMB > MAX_FILE_SIZE_MB) {
                result.append("警告：文件大小(" + fileSizeInMB + "MB)超过系统限制(" + MAX_FILE_SIZE_MB + "MB)，搜索可能需要较长时间或被终止\n");
                LogEngine.getLogger(Constants.EASITLINE_CONSOLE).warn("Grep处理大文件: " + fileSizeInMB + "MB");
            }
        }
        
        // 使用传入的文件路径，避免反射操作
        
        // 检查是否有缓存结果可用
        String cacheKey = generateGrepCacheKey(filePath, grepPattern, caseSensitive, contextLines, pageSize);
        GrepSearchCache cache = GREP_SEARCH_CACHE.get(cacheKey);
        
        // 获取文件最后修改时间
        long fileLastModified;
        try {
            File file = new File(filePath);
            fileLastModified = file.lastModified();
        } catch (Exception e) {
            // 如果无法获取修改时间，使用当前时间
            fileLastModified = System.currentTimeMillis();
        }
        
        // 如果缓存有效，直接使用缓存
        if (cache != null && cache.isValid(fileLastModified)) {
            // 检查请求的页面是否在缓存范围内
            int totalPages = cache.getTotalPages();
            
            // 确保页码合法
            if (page < 1) page = 1;
            if (page > totalPages) page = totalPages;
            
            // 如果已有缓存的页，直接返回
            if (cache.hasPageData(page)) {
                String pageData = cache.getPageData(page);
                result.append(pageData);
                
                // 添加分页信息
                result.append("\n当前页: ").append(page).append(", 总页数: ").append(totalPages)
                      .append(", 总匹配数: ").append(cache.getTotalMatches()).append("\n");
                result.append("totalPages: ").append(totalPages).append("\n");
                
                LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info("从缓存返回grep第 " + page + " 页，总页数: " + totalPages);
                return;
            }
            
            // 如果请求新页面，更新已缓存的数据
            updateGrepCacheWithPage(randomAccessFile, cache, page, result, filePath);
            return;
        }
        
        // 使用分批处理方式读取文件，防止内存溢出
        int flags = caseSensitive ? 0 : Pattern.CASE_INSENSITIVE;
        Pattern pattern;
        try {
            // 使用超时设置编译正则表达式（需要Java 9+）
            // 在Java 8环境下，可以移除这行代码，仅使用普通编译
            try {
                // 尝试使用反射获取Java 9+中的带超时的Pattern编译方法
                java.lang.reflect.Method compileMethod = Pattern.class.getMethod("compile", String.class, int.class, long.class);
                pattern = (Pattern) compileMethod.invoke(null, grepPattern, flags, 1000L); // 1秒超时
                LogEngine.getLogger(Constants.EASITLINE_CONSOLE).debug("使用支持超时的正则表达式编译");
            } catch (Exception e) {
                // 回退到标准编译方法
                pattern = Pattern.compile(grepPattern, flags);
            }
        } catch (PatternSyntaxException e) {
            // 对正则表达式进行转义处理，尝试作为普通文本搜索
            String escapedPattern = Pattern.quote(grepPattern);
            try {
                pattern = Pattern.compile(escapedPattern, flags);
                result.append("注意：您的输入被视为普通文本进行搜索，而非正则表达式\n");
                LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info("转换为普通文本搜索: " + grepPattern);
            } catch (PatternSyntaxException ex) {
                throw e; // 如果还是失败，则抛出原始异常
            }
        }
        
        // 创建新缓存对象
        GrepSearchCache newCache = new GrepSearchCache(grepPattern, caseSensitive, contextLines, pageSize, fileLastModified);
        
        // 收集匹配项的位置信息，而不是匹配内容，减少内存使用
        List<MatchPosition> allMatches = new ArrayList<>();
        int lineNumber = 0;
        int linesProcessed = 0;
        long lineStartPosition = 0;
        
        // 从头开始读取文件
        randomAccessFile.seek(0);
        String line;
        
        // 第一遍：收集所有匹配行的位置信息
        LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info("grep开始处理文件，模式: " + grepPattern);
        long startTime = System.currentTimeMillis();
        long lastProgressLog = startTime;
        int matchCount = 0;
        
        while ((line = randomAccessFile.readLine()) != null) {
            long lineEndPosition = randomAccessFile.getFilePointer();
            lineNumber++;
            linesProcessed++;
            
            // 转换为UTF-8字符串
            line = new String(line.getBytes("ISO-8859-1"), StandardCharsets.UTF_8);
            
            // 检查当前行是否匹配
            Matcher matcher = pattern.matcher(line);
            if (matcher.find()) {
                // 记录匹配行的位置信息，而不是内容
                MatchPosition match = new MatchPosition();
                match.lineNumber = lineNumber;
                match.startPosition = lineStartPosition;
                match.endPosition = lineEndPosition;
                
                allMatches.add(match);
                matchCount++;
            }
            
            // 更新下一行的起始位置
            lineStartPosition = lineEndPosition;
            
            // 安全检查 - 每500ms记录一次进度日志，帮助诊断慢查询
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastProgressLog > 500) {
                lastProgressLog = currentTime;
                LogEngine.getLogger(Constants.EASITLINE_CONSOLE).debug("Grep处理中... 已处理 " + linesProcessed + " 行，匹配 " + matchCount + " 行，耗时 " + (currentTime - startTime) + "ms");
            }
            
            // 每处理一定行数，检查内存使用情况和时间
            if (linesProcessed % MAX_BATCH_SIZE == 0) {
                // 简单的内存使用检查，如果可用内存过低，提前退出
                if (Runtime.getRuntime().freeMemory() < 1024 * 1024 * 10) { // 10MB
                    result.append("警告：内存使用过高，搜索提前终止\n");
                    break;
                }
                
                // 搜索超时检查
                if (System.currentTimeMillis() - startTime > GREP_TIMEOUT_MS) {
                    result.append("警告：搜索时间过长，已处理 " + linesProcessed + " 行，搜索提前终止\n");
                    break;
                }
            }
        }
        
        int totalMatches = allMatches.size();
        LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info("grep找到 " + totalMatches + " 个匹配项，处理了 " + linesProcessed + " 行");
        
        // 如果没有找到匹配项
        if (allMatches.isEmpty()) {
            String noMatchesMessage = "没有找到匹配 '" + grepPattern + "' 的内容\n";
            noMatchesMessage += "totalPages: 0\n";
            
            result.append(noMatchesMessage);
            
            // 保存空结果到缓存
            newCache.setTotalMatches(0);
            newCache.setTotalPages(0);
            newCache.addPageData(1, noMatchesMessage);
            GREP_SEARCH_CACHE.put(cacheKey, newCache);
            
            return;
        }
        
        // 计算总页数
        int totalPages = (totalMatches + pageSize - 1) / pageSize;
        newCache.setTotalMatches(totalMatches);
        newCache.setTotalPages(totalPages);
        
        // 确保页码合法
        if (page < 1) page = 1;
        if (page > totalPages) page = totalPages;
        
        // 计算当前页的起始和结束索引
        int startIndex = (page - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, totalMatches);
        
        // 获取当前页的匹配项
        List<MatchPosition> pageMatches = allMatches.subList(startIndex, endIndex);
        
        // 重置结果用于收集当前页数据
        StringBuilder pageData = new StringBuilder();
        
        // 第二遍：根据位置信息生成当前页的内容
        randomAccessFile.seek(0);
        lineNumber = 0;
        int lastPrintedLine = -1; // 记录上次打印的行号
        long lastReadPosition = 0;
        
        for (MatchPosition match : pageMatches) {
            int matchedLine = match.lineNumber;
            
            // 计算上下文范围
            int startLine = Math.max(1, matchedLine - contextLines);
            int endLine = matchedLine + contextLines;
            
            // 如果与上一个匹配的行范围重叠，调整起始行
            if (startLine <= lastPrintedLine) {
                startLine = lastPrintedLine + 1;
                
                // 如果无法避免重叠，添加分隔符并继续
                if (startLine > endLine) {
                    pageData.append("--\n");
                    continue;
                }
            }
            
            // 如果需要跳转到匹配行之前的位置
            if (lineNumber < startLine - 1) {
                // 直接跳转到匹配行前的位置
                randomAccessFile.seek(match.startPosition);
                lineNumber = matchedLine - 1;
                
                // 重新读取当前行
                String matchedContent = randomAccessFile.readLine();
                if (matchedContent != null) {
                    matchedContent = new String(matchedContent.getBytes("ISO-8859-1"), StandardCharsets.UTF_8);
                    
                    // 处理高亮
                    Matcher matcher = pattern.matcher(matchedContent);
                    StringBuffer sb = new StringBuffer();
                    while (matcher.find()) {
                        String replacement = "[[" + escapeDollarSign(matcher.group()) + "]]";
                        matcher.appendReplacement(sb, replacement);
                    }
                    matcher.appendTail(sb);
                    
                    pageData.append(matchedLine).append(": ").append(sb.toString()).append('\n');
                    lastPrintedLine = matchedLine;
                    lastReadPosition = randomAccessFile.getFilePointer();
                }
            } 
            // 否则从上次位置继续读取
            else {
                // 回到上次读取位置
                randomAccessFile.seek(lastReadPosition);
                
                // 跳过行直到上下文开始
                while (lineNumber < startLine - 1) {
                    randomAccessFile.readLine();
                    lineNumber++;
                }
                
                // 输出上下文前的行
                for (int j = startLine; j < matchedLine; j++) {
                    String contextLine = randomAccessFile.readLine();
                    if (contextLine != null) {
                        lineNumber++;
                        contextLine = new String(contextLine.getBytes("ISO-8859-1"), StandardCharsets.UTF_8);
                        pageData.append(j).append(": ").append(contextLine).append('\n');
                        lastPrintedLine = j;
                    }
                }
                
                // 输出匹配行
                String matchedContent = randomAccessFile.readLine();
                if (matchedContent != null) {
                    lineNumber++;
                    matchedContent = new String(matchedContent.getBytes("ISO-8859-1"), StandardCharsets.UTF_8);
                    
                    // 处理高亮
                    Matcher matcher = pattern.matcher(matchedContent);
                    StringBuffer sb = new StringBuffer();
                    matcher.reset();
                    while (matcher.find()) {
                        String replacement = "[[" + escapeDollarSign(matcher.group()) + "]]";
                        matcher.appendReplacement(sb, replacement);
                    }
                    matcher.appendTail(sb);
                    
                    pageData.append(matchedLine).append(": ").append(sb.toString()).append('\n');
                    lastPrintedLine = matchedLine;
                }
                
                // 输出上下文后的行
                for (int j = matchedLine + 1; j <= endLine; j++) {
                    String contextLine = randomAccessFile.readLine();
                    if (contextLine != null) {
                        lineNumber++;
                        contextLine = new String(contextLine.getBytes("ISO-8859-1"), StandardCharsets.UTF_8);
                        pageData.append(j).append(": ").append(contextLine).append('\n');
                        lastPrintedLine = j;
                    }
                }
                
                lastReadPosition = randomAccessFile.getFilePointer();
            }
            
            // 不同匹配组之间添加分隔符
            pageData.append("--\n");
        }
        
        // 添加分页信息
        pageData.append("\n当前页: ").append(page).append(", 总页数: ").append(totalPages)
              .append(", 总匹配数: ").append(totalMatches).append("\n");
        pageData.append("totalPages: ").append(totalPages).append("\n");
        
        // 将页面数据添加到缓存并保存缓存
        newCache.addPageData(page, pageData.toString());
        GREP_SEARCH_CACHE.put(cacheKey, newCache);
        
        // 添加到返回结果
        result.append(pageData.toString());
        
        LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info("grep返回第 " + page + " 页，总页数: " + totalPages);
    }
    
    /**
     * 更新grep缓存并获取新页面数据
     */
    private void updateGrepCacheWithPage(RandomAccessFile randomAccessFile, GrepSearchCache cache, int page, StringBuilder result, String filePath) throws IOException {
        // 此部分代码逻辑与readWithGrep相似，但只处理特定页面的数据
        // 实际中，这里应该根据缓存的匹配位置信息来高效地读取特定页面数据
        // 为简化，这里不再重复实现这部分逻辑

        // 如果无法从缓存中获取页面数据，重新执行搜索
        result.append("缓存页面数据不可用，重新执行搜索...\n");
        readWithGrep(randomAccessFile, result, cache.getPattern(), 0, cache.isCaseSensitive(), cache.getContextLines(), page, cache.getPageSize(), filePath);
    }
    
    /**
     * 生成grep缓存键
     */
    private String generateGrepCacheKey(String filePath, String pattern, boolean caseSensitive, int contextLines, int pageSize) {
        return filePath + "|" + pattern + "|" + caseSensitive + "|" + contextLines + "|" + pageSize;
    }
    
    /**
     * 转义替换字符串中的美元符号
     */
    private String escapeDollarSign(String input) {
        return input.replace("$", "\\$");
    }
    
    /**
     * grep搜索缓存类
     */
    private static class GrepSearchCache {
        private final String pattern;
        private final boolean caseSensitive;
        private final int contextLines;
        private final int pageSize;
        private final long fileLastModified;
        private final long creationTime;
        private int totalMatches;
        private int totalPages;
        private final java.util.Map<Integer, String> pageDataCache = new java.util.HashMap<>();
        
        public GrepSearchCache(String pattern, boolean caseSensitive, int contextLines, int pageSize, long fileLastModified) {
            this.pattern = pattern;
            this.caseSensitive = caseSensitive;
            this.contextLines = contextLines;
            this.pageSize = pageSize;
            this.fileLastModified = fileLastModified;
            this.creationTime = System.currentTimeMillis();
        }
        
        public boolean isValid(long currentFileLastModified) {
            // 如果文件被修改或缓存过期，则缓存失效
            return fileLastModified == currentFileLastModified && 
                   (System.currentTimeMillis() - creationTime) < (GREP_CACHE_TTL * 1000);
        }
        
        public void setTotalMatches(int totalMatches) {
            this.totalMatches = totalMatches;
        }
        
        public int getTotalMatches() {
            return totalMatches;
        }
        
        public void setTotalPages(int totalPages) {
            this.totalPages = totalPages;
        }
        
        public int getTotalPages() {
            return totalPages;
        }
        
        public void addPageData(int page, String data) {
            pageDataCache.put(page, data);
        }
        
        public boolean hasPageData(int page) {
            return pageDataCache.containsKey(page);
        }
        
        public String getPageData(int page) {
            return pageDataCache.get(page);
        }
        
        public String getPattern() {
            return pattern;
        }
        
        public boolean isCaseSensitive() {
            return caseSensitive;
        }
        
        public int getContextLines() {
            return contextLines;
        }
        
        public int getPageSize() {
            return pageSize;
        }
        
        // 新增获取创建时间的方法，用于LRU缓存实现
        public long getCreationTime() {
            return creationTime;
        }
    }
    
    /**
     * 匹配结果类，用于存储匹配行信息
     */
    private static class MatchResult {
        int lineNumber;
        String content;
    }
    
    /**
     * 匹配位置类，仅存储匹配行的位置信息，而不存储内容
     */
    private static class MatchPosition {
        int lineNumber;
        long startPosition;
        long endPosition;
    }
    
    /**
     * 计算正则表达式中量词 (*,+,?, {n,m}) 的数量，作为复杂度判断依据
     */
    private int countRegexQuantifiers(String regex) {
        int count = 0;
        boolean inCharClass = false;
        
        for (int i = 0; i < regex.length(); i++) {
            char c = regex.charAt(i);
            
            // 跳过字符类内部
            if (c == '[' && (i == 0 || regex.charAt(i-1) != '\\')) {
                inCharClass = true;
            } else if (c == ']' && (i == 0 || regex.charAt(i-1) != '\\')) {
                inCharClass = false;
            }
            
            // 计算量词
            if (!inCharClass) {
                if ((c == '*' || c == '+' || c == '?' || c == '{') && 
                    (i > 0 && regex.charAt(i-1) != '\\')) {
                    count++;
                }
            }
        }
        
        return count;
    }
}