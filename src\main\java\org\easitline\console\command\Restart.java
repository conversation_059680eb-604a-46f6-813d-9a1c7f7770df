package org.easitline.console.command;

import java.io.File;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Map;

import org.easitline.common.core.Globals;
import org.easitline.common.core.log.LogEngine;
import org.easitline.console.base.Constants;

public class Restart {
    private static FileLogger LOG;

    public static boolean runShell(String appId,String work,String cmd) {
    	String processId = Self.getProcessId();
    	return  execute(cmd,processId,work,appId);
    }
    
    public static boolean restartTomcat() {
    	String work=Globals.BASE_DIR;
    	String cmd = getRestartCommand(work);
    	String processId = Self.getProcessId();
    	return  execute(cmd,processId,work,Constants.EASITLINE_CONSOLE);
    }
    
    
    /**
     * @param processId
     * @param work
     * @return boolean
     */
    public static boolean execute(String cmd, String processId, String work,String appId) {
      
        String javaHome = getJavaHome();
        String banner = getBanner();

        log("{}", banner);
        info("appId: {}", appId);
        info("processId: {}", processId);
        info("daemon: {}", Thread.currentThread().isDaemon());
        info("work: {}", work);
        info("cmd: {}", cmd);
        info("processId: {}, work: {}", processId, work);
        info("JAVA_HOME: {}", javaHome);
        info("execute: {} {} {}", cmd, processId,work);

        if(cmd == null) {
            info("restart failed, cmd is null.");
            return false;
        }

       // Self.kill(processId,90000L, true);

        try {
            ProcessBuilder processBuilder = new ProcessBuilder(OS.getCMD(cmd), processId,work,appId);
            setJavaHome(processBuilder, javaHome);
            processBuilder.directory(new File(work));// 指定检查脚本存放目录，绝对路径
            processBuilder.redirectErrorStream(true);//合并标准错误和标准输出
            Process process = processBuilder.start();

            ReadThread.execute("stdout", process.getInputStream(), true);
            ReadThread.execute("errout", process.getErrorStream(), true);
            
            int exitCode = process.waitFor();
            info("exitCode: {}", exitCode);
            info("wait start tomcat");
            info("processId：{}",Self.getProcessId());
            Thread.sleep(2000);
            process.destroy();
            return true;
        }
        catch(Exception e) {
            info(e.getMessage(), e);
        }
        return false;
    }

    /**
     * @param work
     * @return String
     */
    private static String getRestartCommand(String work) {
        File file = null;
        if(OS.WINDOWS) {
            file = new File(Globals.CONF_DIR + "/restart.bat");
        }
        else {
            file = new File(Globals.CONF_DIR + "/restart.sh");
        }
        if(!file.exists()) {
        	file = new File(Globals.BASE_DIR + "/run.sh");
        }
        if(!file.exists()) {
        	file = new File(Globals.BASE_DIR + "/restart.sh");
        }
        if(file.exists() && file.isFile()) {
            return file.getAbsolutePath();
        }else{
        	return null;
        }
    }
  
    /**
     * @param format
     * @param args
     */
    private static void log(String format, Object ... args) {
        FileLogger logger = getLogger();
        String message = logger.format(format, args);
        logger.write(message);
    }

    /**
     * @param format
     * @param args
     */
    private static void info(String format, Object ... args) {
        FileLogger logger = getLogger();
        String message = logger.format(format, args);
        logger.info(message);
        LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info(message);
    }

    /**
     * @param message
     * @param throwable
     * @param args
     */
    private static void info(String message, Throwable throwable) {
        String stackTrace = getStackTrace(throwable);
        FileLogger logger = getLogger();
        logger.info(message);
        logger.write(stackTrace);

        LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info(message,throwable);
    }

    /**
     * @return String
     */
    public static String getBanner() {
        StringBuilder banner = new StringBuilder();
        banner.append("================================================\r\n");
        banner.append("Restart Server\r\n");
        banner.append("================================================\r\n");
        return banner.toString();
    }

    /**
     * @return FileLogger
     */
    public static FileLogger getLogger() {
        if(LOG == null) {
            boolean append = true;
            long $100M = 100L * 1024L * 1024L;
            File logFile = new File(Globals.LOG_DIR+File.separator+"mars-restart.log");
            try {
                if(!logFile.exists()) {
                    logFile.createNewFile();
                    logFile.setReadable(true, true);
                    logFile.setWritable(true, true);
                    logFile.setExecutable(true, true);
                }
                append = (logFile.length() < $100M);
            }
            catch(Exception e) {
                e.printStackTrace();
            }
            LOG = new FileLogger(logFile, "utf-8", append);
        }
        return LOG;
    }

    /**
     * @return String
     */
    public static String getJavaHome() {
        File file = new File(System.getProperty("java.home"));

        if(file.getName().equalsIgnoreCase("jre")) {
            File home = file.getParentFile();
            File work = new File(home, "bin");

            if(work.exists() && work.isDirectory()) {
                return home.getAbsolutePath();
            }
        }
        return file.getAbsolutePath();
    }

    /**
     * @param processBuilder
     * @param javaHome
     */
    protected static void setJavaHome(ProcessBuilder processBuilder, String javaHome) {
        try {
            /**
             * windows server
             * 在某些情况下使用java进程重启tomcat的时候, JAVA_HOME环境变量不会被传递到新的进程
             * 这种情况将导致tomcat无法启动, setclasspath.bat脚本报错
             * 此处使用当前的JAVA_HOME传递到新的进程
             */
            Map<String, String> environment = processBuilder.environment();

            if(environment.get("JAVA_HOME") == null) {
                info("set JAVA_HOME = " + javaHome);
                environment.put("JAVA_HOME", javaHome);
            }
        }
        catch(Exception e) {
            info("set environment failed: " + javaHome);
        }
    }

    /**
     * @param throwable
     * @return String
     */
    private static String getStackTrace(Throwable throwable) {
        StringWriter stringWriter = new StringWriter();
        PrintWriter out = new PrintWriter(stringWriter);

        throwable.printStackTrace(out);
        out.flush();
        return stringWriter.toString();
    }

    /**
     * @param map
     */
    protected static void print(Map<String, String> map) {
        if(map == null || map.isEmpty()) {
            info("environment: null");
            return;
        }

        for(Map.Entry<String, String> entry : map.entrySet()) {
            info("env." + entry.getKey() + " = " + entry.getValue());
        }
    }

    /**
     * release
     */
    protected static void release() {
        if(LOG != null) {
            LOG.close();
        }
    }
}
