<%@page pageEncoding="UTF-8"%>
<div>
	<blockquote class="layui-elem-quote">
		数据源管理用于配置Mars平台数据库连接池，这里新增的数据源为系统数据源，系统数据源和应用数据源的对应关系在应用配置中执行指定。
	</blockquote>

	<fieldset class="layui-elem-field layui-field-title">
		<legend>编辑数据源</legend>
	</fieldset>
	<form class="layui-form" method="post" id="easyform"  autocomplete="off" data-mars="datasource.record" data-mars-prefix="ds.">
		<input type="hidden" name="pkId" value="${param.pkId}"/>
		<input type="hidden" name="dName" value="${param.dName}"/>
		  <div class="layui-form-item">
		    <label class="layui-form-label">配置方式</label>
		    <div class="layui-input-block">
		      <select name="ds.CONF_TYPE" lay-verify="required" class="layui-select" lay-filter="selectConfType" style="width: 100%;display: none;">
		        <option value="simple">简单模式</option>
		        <option value="advanced">高级模式</option>
		      </select>
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">数据源名称</label>
		    <div class="layui-input-block">
		      <input type="text" lay-verify="required" id="dsName" name="ds.SYS_DS_NAME" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">数据库类型</label>
		    <div class="layui-input-block">
		      <input name="ds.DB_TYPE_NAME" id="dbTypeName" type="hidden">
		      <select name="ds.DB_TYPE" lay-verify="required" class="layui-select" lay-filter="selectDbType" style="width: 100%;display: none;">
		        <option value="MySql">MySql</option>
		        <option value="MySql8">MySql8.X</option>
		        <option value="Oracle">Oracle</option>
		        <option value="PostgreSql">PostgreSql</option>
		        <option value="dameng">达梦</option>
		        <option value="OpenGauss">OpenGauss</option>
		        <option value="GoldenDB">GoldenDB</option>
		        <option value="KingbaseES">人大金仓</option>
		        <option value="Db2">Db2</option>
		       <!--  <option value="Sybase">Sybase</option> -->
		       <!-- <option value="PostgreSql">海量数据库</option>
		        <option value="PostgreSql">中国移动</option>
		        <option value="PostgreSql">磐维数据库</option>
		        <option value="PostgreSql">GaussDB</option>-->
		        <option value="clickhouse">clickhouse</option>
		        <option value="oscar">神通数据库</option>
		        <option value="SqlServer">SqlServer</option>
		        <option value="other">其它</option>
		      </select>
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">库主从标识</label>
		    <div class="layui-input-block">
			      <input type="radio" name="ds.DB_MASTER" value="true" title="主库" checked="checked">
			      <input type="radio" name="ds.DB_MASTER" value="false" title="从库">
		    </div>
		  </div>
		  <div class="layui-form-item simple">
		    <label class="layui-form-label">数据库IP</label>
		    <div class="layui-input-block">
		      <input type="text" lay-verify="required" name="ds.IP_ADDR" class="layui-input">
		      <span style="position: absolute;top: 7px;right: -25px;cursor: pointer;" onclick="layer.msg('oracle rac方式ip填写服务名')"><i class="layui-icon layui-icon-tips"></i></span>
		    </div>
		  </div>
		  <div class="layui-form-item simple">
		    <label class="layui-form-label">数据库端口</label>
		    <div class="layui-input-block">
		      <input type="number" lay-verify="required" name="ds.IP_PORT" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">最小连接数</label>
		    <div class="layui-input-block">
		      <input type="text" lay-verify="required" name="ds.MIN_CONN" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">最大连接数</label>
		    <div class="layui-input-block">
		      <input type="text" lay-verify="required" name="ds.MAX_CONN" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item simple">
		    <label class="layui-form-label">数据库名</label>
		    <div class="layui-input-block">
		      <input type="text" lay-verify="required" name="ds.DB_NAME" class="layui-input">
		    </div>
		  </div>
		    <div class="layui-form-item">
		    <label class="layui-form-label">数据库用户名</label>
		    <div class="layui-input-block">
		      <input type="text" lay-verify="required" name="ds.USERNAME" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">密码</label>
		    <div class="layui-input-block">
		      <input type="password" lay-verify="required" name="PASSWORD" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item advanced">
		    <label class="layui-form-label">JDBC连接字符串</label>
		    <div class="layui-input-block">
		      <input type="text" name="ds.JDBC_URL" placeholder="jdbc:....,如不为空ip端口等都无效,都是以此配置为准" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item advanced">
		    <label class="layui-form-label">JDBC驱动类</label>
		    <div class="layui-input-block">
		      <input type="text" name="ds.DRIVER_NAME" placeholder="example:com.mysql.cj.jdbc.Driver" class="layui-input">
		    </div>
		  </div>
		   <div class="layui-form-item simple-other">
		    <label class="layui-form-label">连接扩展参数</label>
		    <div class="layui-input-block">
		      <input type="text" placeholder="默认不需填写;格式:characterEncoding=UTF-8&useSSL=false"  onchange="validateJdbcUrlParams(this)" name="ds.URL_PROPERTIES" class="layui-input">
		      <span style="position: absolute;top: 7px;right: -25px;cursor: pointer;" onclick="layer.alert('characterEncoding=UTF-8&serverTimezone=Hongkong&useSSL=false&allowPublicKeyRetrieval=true',{title:'参考配置'})"><i class="layui-icon layui-icon-tips"></i></span>
		    </div>
		  </div>
		   <div class="layui-form-item">
		    <label class="layui-form-label">连接池参数</label>
		    <div class="layui-input-block">
		      	 <input type="text" readonly="readonly" id="druidConf" placeholder="默认无需修改，如需修改点击右侧配置" name="ds.DS_PROPERTIES" class="layui-input">
		     	 <a href="javascript:druidConf(this);" style="position: absolute;top: 7px;right: -35px;">配置</a>
		    </div>
		  </div> 
		 <%--  <div class="layui-form-item" style="display: none;">
		    <label class="layui-form-label">SQL执行语句</label>
		    <div class="layui-input-block">
		      <textarea type="text"  name="sqlContent" class="layui-textarea"></textarea>
		    </div>
		  </div>  --%>
		  <div class="layui-form-item">
		    <div class="layui-input-block">
		      <button type="button" lay-submit lay-filter="submitBtn" class="layui-btn layui-btn-sm">保存</button>
		      <button type="button" lay-submit lay-filter="testbut" id="testbut" class="layui-btn layui-btn-sm layui-btn-normal">测试连接</button>
		      <button type="button" id="delbut" class="layui-btn layui-btn-sm layui-btn-warm" onclick="deleteDs()">删除数据源</button>
		      <button type="button" id="initbut" class="layui-btn layui-btn-sm layui-btn-warm" onclick="initData()">初始化数据</button>
		      
		      <button type="button" id="tableMatch" class="layui-btn layui-btn-sm layui-btn-normal ml-15 layui-hide" onclick="tableMatchFn()">表结构检测</button>
		     
		    </div>
		  </div>
	</form>
</div>

<style type="text/css">
    #confEasyform .layui-input-block{width:300px;}
	.advanced{display: none;}
	input:-webkit-autofill {  
    -webkit-box-shadow: 0 0 0px 1000px white inset !important;  
} 
</style>
<script>
   	
	
    var pkId = "${param.pkId}";
    var recordObj={};
    var isClickTest=false;
    $(function(){
    	initDbField();
    	$("#initbut,#tableMatch").hide();
    	$("#delbut").hide();
    	if(pkId!=""&&pkId!="undefined"){
	    	$("#easyform").render({success:function(result){
	    		if(result&&result["datasource.record"]&&result["datasource.record"].data){
	    			recordObj=result["datasource.record"].data;
	    			$("#dsName").attr("disabled","disabled");
	    			$("#delbut,#tableMatch").show();
	    			if(result["datasource.record"].data.SYS_DS_NAME =="default-ds"){
	    				$("#delbut").hide();
	    				$("#initbut").show();
	    			}
	    			if(recordObj){
	    				 changeConfType(recordObj.CONF_TYPE);
	    			}
	    			
				   	layui.use('form', function(){
				         var layuiform = layui.form;
				         layuiform.render();
						 layuiform.on('submit(submitBtn)', function(data){
							 if(isClickTest){
								 saveConnection(data.field);
							 }else{
								 layer.msg("请先点击测试确保参数无误。",{time:1000},function(){
									 $("#testbut").click();
								 });
							 }
						 });
						 layuiform.on('submit(testbut)', function(data){
							 isClickTest=true;
							 testConnection(data.field);
						 });
						
						 layuiform.on('select(selectConfType)', function(data){
							 changeConfType(data.value);
						});
						 
						 layuiform.on('select(selectDbType)', function(data){
							 var dbTypeName = data.elem[data.elem.selectedIndex].innerText;
							 $('#dbTypeName').val(dbTypeName);
							 if(data.value=='dameng'){
								$('#druidConf').val('{"filters":"stat"}');
							 }else{
								$('#druidConf').val('');
							 }
						});
				     });
	    		}
	    	}});
    	}else{
	    	//开启表单渲染
		   	layui.use('form', function(){
		         var layuiform = layui.form;
		         layuiform.render();
		         
		       	//监听提交
				 layuiform.on('submit(submitBtn)', function(data){
					 saveConnection(data.field);
				 });
				//监听提交
				 layuiform.on('submit(testbut)', function(data){
					 testConnection(data.field);
				 });
				
				 layuiform.on('select(selectConfType)', function(data){
					 changeConfType(data.value);
				});
				 
				layuiform.on('select(selectDbType)', function(data){
					 var dbTypeName = data.elem[data.elem.selectedIndex].innerText;
					 $('#dbTypeName').val(dbTypeName);
					 if(data.value=='dameng'){
						$('#druidConf').val('{"filters":"stat"}');
					 }else{
						$('#druidConf').val('');
					 }
				});
				 
		     });
    	}
	});
    
    function changeConfType(value){
    	if(value=='advanced'){
			 $('.simple,.simple-other').hide();
			 $('.advanced').show();
			 $('.advanced').find('input').attr('lay-verify','required');
			 $('.simple').find('input').removeAttr('lay-verify');
		 }else{
			 $('.advanced').hide();
			 $('.simple,.simple-other').show();
			 $('.simple').find('input').attr('lay-verify','required');
			 $('.advanced').find('input').removeAttr('lay-verify');
		 }
    }
	
    function tableMatchFn(){
		var dsName=recordObj['SYS_DS_NAME'];
		var dbType=recordObj['DB_TYPE'];
		window.open("${ctxPath}/pages/datasource/match-table.jsp?dsName="+dsName+"&dbType="+dbType);
	}
    
	//测试连接
	function testConnection(data){
		var _data = JSON.stringify(data);
		var dataStr = aesEncrypt(_data);
		ajax.remoteCall("${ctxPath}/servlet/datasource?action=test",{encryptStr:dataStr},function(result){  
			if(result.state==1){
				layer.msg(result.msg,{icon: 1,time:1000},function(){
					layer.closeAll();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
	
	//保存
	function saveConnection(data) {
		var _data = JSON.stringify(data);
		var dataStr = aesEncrypt(_data);
		ajax.remoteCall("${ctxPath}/servlet/datasource?action=save",{encryptStr:dataStr},function(result) { 	
			if(result.state==1){
				layer.msg(result.msg,{icon: 1,time:800},function(){
					layer.closeAll();
					loadPage("datasource/list.jsp",{});
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
	
	//删除
	function deleteDs() {
		var data = form.getJSONObject("#easyform");
		var msgTxt = "是否删除数据源 ："+data.pkId+"？";
		layer.confirm(msgTxt,{btn: [ '删除', '取消'],icon: 3, title:'警告',offset:'40%'}, 
			//创建人工任务
			function(index){
				layer.close(index);
				ajax.remoteCall("${ctxPath}/servlet/datasource?action=delete",data,function(result) { 	
					if(result.state==1){
						layer.msg(result.msg,{icon: 1,time:800},function(){
							layer.closeAll();
							loadPage("datasource/list.jsp",{});
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}, 
			function(index){
				layer.close(index);
			}
		);
	}
	
	//初始化数据
	function initData() {
		var data = form.getJSONObject("#easyform");
		ajax.remoteCall("${ctxPath}/servlet/datasource?action=init",data,function(result) { 	
			if(result.state==1){
				layer.msg(result.msg,{icon: 1,time:800},function(){
					layer.closeAll();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
	
	function initDbField() {
		ajax.remoteCall("${ctxPath}/servlet/datasource?action=updateDbField",{},function(result) { 	
			
		});
	}
	
	function druidConf(el){
		$.get('${ctxPath}/pages/datasource/druid-conf.jsp',{}, function(str){
		  layer.open({title:'连接池配置',offset:'30px',area:['580px','500px'],type: 1,content: str,btn:['保存'],yes:function(index){
			  var postdata = form.getJSONObject("#confEasyform");
			  for(var key in postdata){
				  if(postdata[key]==''){
					  delete postdata[key];
				  }
			  }
			  var str  = JSON.stringify(postdata);
			  $('#druidConf').val(str);
			  layer.close(index);
		  },success:function(){
				var jsonStr =  $('#druidConf').val();
				if(jsonStr){
					var json = eval('(' + jsonStr + ')');
				  	fillRecord(json,'',',','#confEasyform');
				}
		  }});
		});
	}
	
	function validateJdbcUrlParams(input) {
		  var raw = input.value.trim();
		  if (!raw) return;

		  var pairs = raw.split('&');
		  var isValid = true;
		  var errorMsg = "";

		  for (var i = 0; i < pairs.length; i++) {
		    var part = pairs[i];

		    if (part.indexOf('=') === -1) {
		      isValid = false;
		      errorMsg = "缺少 '=' 符号（问题片段: " + part + "）";
		      break;
		    }

		    var kv = part.split('=');
		    var key = kv[0];
		    var value = kv[1];

		    if (!key || !value) {
		      isValid = false;
		      errorMsg = "key 或 value 为空（问题片段: " + part + "）";
		      break;
		    }

		    if (/\s/.test(key) || /\s/.test(value)) {
		      isValid = false;
		      errorMsg = "key/value 中不能包含空格（问题片段: " + part + "）";
		      break;
		    }
		  }

		  if (!isValid) {
		    layer.msg(errorMsg, { icon: 2 });
		    input.focus();
		  }
	}
	
</script>