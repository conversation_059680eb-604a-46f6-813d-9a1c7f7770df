package org.easitline.console.service;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import org.easitline.common.core.Globals;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.FileKit;
import org.apache.commons.lang3.StringUtils;
import org.easitline.console.command.FileLogger;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

public class LogBakService implements Job{

	 private static FileLogger LOG;
	 
	private static class Holder{
		private static LogBakService service=new LogBakService();
	}
	public static LogBakService getService(){
		return Holder.service;
	}

	@Override
	public void execute(JobExecutionContext arg0) throws JobExecutionException {
		this.run();
	}
	
	public void run() {
		Calendar calendar=Calendar.getInstance();
		int month = calendar.get(Calendar.MONTH)+1;
		int week = calendar.get(Calendar.DAY_OF_WEEK);//周日 1，周一 2 周六是7
		int day = calendar.get(Calendar.DAY_OF_MONTH);
		int hour = calendar.get(Calendar.HOUR_OF_DAY);
		int minute = calendar.get(Calendar.MINUTE);
		
		this.logRename();
		
		if(minute==0) {
			this.bakCatalina();
			this.autoBakAppLogs();
		}
		//每天凌晨删除之前的日志
		if(minute==0&&hour==1) {
			delLog();
		}
	}
	
	public void delLog() {
		//删除指定天数前备份文件
		Calendar calendar = Calendar.getInstance();
		int logSaveDay = Integer.valueOf(ServerContext.getProperties("logSaveDay", "1"));
		calendar.add(Calendar.DATE, logSaveDay*-1);
		SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
		String beforeDate = format.format(calendar.getTime());
		
		String bakPath = ServerContext.getProperties("bakToDir", Globals.SERVER_DIR+File.separator+"logbak");
		File file = new File(bakPath);
		File[] list = file.listFiles();
		
		for(File fs:list) {
			fs.isDirectory();
			String name = fs.getName();
			if(name.compareTo(beforeDate)<0) {
				fs.deleteOnExit();
			}
		}
	}
	
	public void bakCatalina() {
		String sourceLog = Globals.BASE_DIR+File.separator+"logs";
		String tomcatLogBakPrefix = ServerContext.getProperties("tomcatLogBakPrefix", "catalina.out.");
		LogBakService.getService().run(sourceLog,tomcatLogBakPrefix);
	}
	
	public void bakAppLogs(String ... args) {
		String sourceLog = Globals.LOG_DIR;
		LogBakService.getService().run(sourceLog,args);
	}
	
	public void bakAppLogs() {
		LogBakService.getService().bakAppLogs("easitline","mars");
	}
	
	private void autoBakAppLogs() {
		String appLogBakPrefix = ServerContext.getProperties("appLogBakPrefix","");
		if(StringUtils.isNotBlank(appLogBakPrefix)) {
			LogBakService.getService().bakAppLogs(appLogBakPrefix.split(","));
		}
	}
	
	
	public void logRename() {
		
	}
	
	/**
	 * 备份日志文件
	 * @param sourceLog 指定目录
	 * @param args 备份规则，某文件开头
	 */
	public void run(String sourceLog,String ... args){
		String bakPath = ServerContext.getProperties("bakToDir", Globals.SERVER_DIR+File.separator+"logbak");
		
		File file = new File(sourceLog);
		File[] files = file.listFiles();
		for(File logFile:files) {
			String fileName = logFile.getName();
			long beginTs = System.currentTimeMillis();
			boolean flag = false;
			for(String val:args) {
				if(fileName.toLowerCase().startsWith(val.toLowerCase())) {
					flag = true;
					break;
				}
			}
			if(flag) {
				long timestamp = logFile.lastModified();
				Date fileDate=new Date(Long.valueOf(timestamp));
				String lastDate = EasyDate.dateToString(fileDate, "yyyyMMddHHmmss");
				long nowTs = System.currentTimeMillis();
				
//				if(!lastDate.startsWith(String.valueOf(dateId))) {
//					continue;
//				}
				//备份指定时间间距的日志文件  一旦文件很久没更新就立即备份这个文件，如果10分钟内已经没更新，那上次备份，再备份就重复了
				if((nowTs-timestamp)>(1000*60*60*24)) {
					continue;
				}
				
				try {
					//新的备份日志文件名称
					String newName =  FileKit.getQianzui(fileName)+"-"+lastDate+FileKit.getHouzui(fileName);
					
					//复制日志到备份目录
					String logFileName = bakPath+File.separator+newName;
					FileKit.copyFile(logFile.getAbsolutePath(), logFileName);
					
					File logBakFile =new File(logFileName);
					logBakFile.setLastModified(timestamp);
					
					//把备份的文件zip打包
					zip(bakPath,logFileName);
					
					//删除备份的文件
					 new File(logFileName).delete();
					
					long endTs = System.currentTimeMillis();
					
					info("{}-共耗时[{}]毫秒",logFile.getAbsolutePath(),endTs-beginTs);
				} catch (IOException e) {
					info(e.getMessage());
				}
			}
			
		}
	}
	
	
	private static void zip(String basePath,String path) throws IOException {
		   File f = new File(path);
	       FileInputStream fis = new FileInputStream(f);
	       BufferedInputStream bis = new BufferedInputStream(fis);
	       byte[] buf = new byte[1024];
	       int len;
	       FileOutputStream fos = new FileOutputStream(basePath+File.separator+f.getName()+".zip");
	       BufferedOutputStream bos = new BufferedOutputStream(fos);
	       ZipOutputStream zos = new ZipOutputStream(bos);//压缩包
	       ZipEntry ze = new ZipEntry(f.getName());//这是压缩包名里的文件名
	       zos.putNextEntry(ze);//写入新的 ZIP 文件条目并将流定位到条目数据的开始处

	       while((len=bis.read(buf))!=-1)
	       {
	          zos.write(buf,0,len);
	          zos.flush();
	       }
	       bis.close();
	       zos.close(); 
	      
	}
	
	private static void info(String format, Object ... args) {
        FileLogger logger = getLogger();
        String message = logger.format(format, args);
        logger.info(message);
    }
	
	 public static FileLogger getLogger() {
	        if(LOG == null) {
	            boolean append = true;
	            long $100M = 100L * 1024L * 1024L;
	            File logFile = new File(Globals.BASE_DIR+File.separator+"logs"+File.separator+"mars-logbak.log");
//	            File logFile = new File("D:\\develop\\mars-server-node\\logs\\mars-logbak.log");
	            try {
	                if(!logFile.exists()) {
	                    logFile.createNewFile();
	                    logFile.setReadable(true, true);
	                    logFile.setWritable(true, true);
	                    logFile.setExecutable(true, true);
	                }
	                append = (logFile.length() < $100M);
	            }
	            catch(Exception e) {
	                e.printStackTrace();
	            }
	            LOG = new FileLogger(logFile, "utf-8", append);
	        }
	        return LOG;
	 }

}
