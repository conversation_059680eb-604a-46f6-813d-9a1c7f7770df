package org.easitline.console.utils;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

public class RequestUtil {

    // 定义HTML实体编码的映射表
    private static final Map<Character, String> charToEntityMap = new HashMap<>();
    
    static {
        charToEntityMap.put('<', "&lt;");
        charToEntityMap.put('>', "&gt;");
        charToEntityMap.put('&', "&amp;");
        charToEntityMap.put('"', "&quot;");
        charToEntityMap.put('\'', "&#39;");
    }

    /**
     * 过滤输入字符串，将特殊字符替换为对应的HTML实体编码
     * @param input 需要过滤的字符串
     * @return 过滤后的字符串
     */
    public static String filter(String input) {
        if (input == null) {
            return null;
        }
        
        StringBuilder filtered = new StringBuilder(input.length());
        
        for (char c : input.toCharArray()) {
            String replacement = charToEntityMap.get(c);
            if (replacement != null) {
                filtered.append(replacement);
            } else {
                filtered.append(c);
            }
        }
        
        return filtered.toString();
    }
    

    /**
     * URL编码方法，使用UTF-8编码，并将'/'视为安全字符
     * @param input 需要编码的字符串
     * @return 编码后的字符串
     */
    public static String encode(String input) {
        if (input == null) {
            return null;
        }
        
        try {
            // 首先使用URLEncoder进行编码
            String encoded = URLEncoder.encode(input, "UTF-8");

            // 替换将'/'编码的部分
            encoded = encoded.replace("%2F", "/");

            return encoded;
        } catch (UnsupportedEncodingException e) {
            // 通常不会发生，因为UTF-8是标准编码
            throw new RuntimeException("UTF-8 encoding is not supported", e);
        }
    }
}
