<%@ page language="java" contentType="text/html; charset=UTF-8"    pageEncoding="UTF-8"%>
<!DOCTYPE HTML>
<style>
<!--
.sui-table.table-no-background td{background:none
!important;}
-->
</style>
<div class="modal-header">
	<button class="sui-close" aria-hidden="true" type="button"
		data-dismiss="modal">×</button>
	<h4 class="modal-title" id="mgrs_label">填写执行时间</h4>
</div>
<div class="modal-body">
     <ul class="sui-nav nav-tabs">
	     <li><a href="#MinutesTab" data-toggle="tab">定时</a></li>
<!-- 	     <li><a href="#HourlyTab" data-toggle="tab">小时</a></li> -->
	     <li class="active"><a href="#DailyTab" data-toggle="tab">每天</a></li>
	     <li><a href="#WeeklyTab" data-toggle="tab">每周</a></li>	
	     <li><a href="#MonthlyTab" data-toggle="tab">每月</a></li>
	     <li><a href="#OneTimeTab" data-toggle="tab">执行一次</a></li>
<!-- 	     <li><a href="#YearlyTab" data-toggle="tab">年</a></li>	 -->
	 </ul>
	 <div class="tab-content">
	     <div id="MinutesTab" class="tab-pane">
	        <div class="well well-small">
	            &nbsp;每&nbsp;<input type="text" id="minutesInput"  class="input-small"  style="width: 40px" value="1" />&nbsp;分钟执行一次
	        </div>
	        <div class="well well-small">
	            <div style="margin-bottom:2px">
	                <input type="radio" id="executeTypeRadio1" name="executeTypeRadio" value="now"  checked />立即执行
	            </div><br/>
	           <input type="radio" id="executeTypeRadio2" name="executeTypeRadio" value="time" />延迟执行时间：<select id="beginHour" class="hours" style="width: 60px"></select>时<select id="beginMinute" class="minutes" style="width: 60px"></select>分
	        </div>
	     </div>
	     <div id="DailyTab" class="tab-pane active" >
	     </div>
	     <div id="WeeklyTab" class="tab-pane">
	         <div class="well well-small">
	             <div class="sui-row-fluid">
	                 <div class="span7">
	                      <input type="checkbox" value="SUN" txt="周日">&nbsp;周日&nbsp;&nbsp;
	                      <input type="checkbox" value="MON" txt="周一">&nbsp;周一&nbsp;&nbsp;
	                      <input type="checkbox" value="TUE" txt="周二">&nbsp;周二&nbsp;&nbsp;
		                  <input type="checkbox" value="WED" txt="周三">&nbsp;周三&nbsp;&nbsp;
		                  <input type="checkbox" value="THU" txt="周四">&nbsp;周四&nbsp;&nbsp;
		                  <input type="checkbox" value="FRI" txt="周五">&nbsp;周五&nbsp;&nbsp;
		                  <input type="checkbox" value="SAT" txt="周六">&nbsp;周六&nbsp;&nbsp;
	                 </div>
	             </div>
	           </div>
	     </div>
	     <div id="MonthlyTab" class="tab-pane">
	        <div class="well well-small">
	            <div class="sui-row">
				    <div class="span5" id="calendarDiv">
				    </div>
			     </div>
	         </div>
	     </div>
	     <div id="OneTimeTab" class="tab-pane">
	        <div class="well well-small">
	            <div class="span2">
	                <span>日期:</span>
			        <input type="text" data-toggle="datepicker" value="" id="oneTimeDate">
	            </div>
	            <div class="span3">
	                <span>时间:</span>
			        <input type="text" data-toggle="timepicker" style="width:50px" value="23:59" id="oneTimeTime">
	            </div>
	        </div>
	     </div>
	     <div id="commonDiv">
	          <table class="sui-table table-nobordered table-no-background" style="border: 1px solid #ddd;" id="everyDayTable">
				  <thead>
				    <tr>
				      <th colspan="2" style="background-color:#f4f4f4"> 
				        <label class="checkbox pull-left">
				                               每天频率
				        </label>
				      </th>
				    </tr>
				  </thead>
				  <tbody>
				    <tr>
				       <td colspan="2">
				          <label class="radio-pretty inline">
						    <input type="radio" checked="checked" name="timeTypeRadio" value="oneTime"><span>执行一次,时间为</span>
						  </label>
						  <input type="text" data-toggle="timepicker" style="width:50px" value="00:00" id="oneTimeTimer">
				       </td>
				    </tr>
				    <tr>
				       <td style="width: 250px">
				          <label class="radio-pretty inline checked">
						    <input type="radio" checked="checked" name="timeTypeRadio" value="moreTime"><span>执行间隔：</span>
						  </label>
						  <input type="text" id="intervalInput"  class="input-small"  style="width: 40px" value="1" />
	                      <select id="intervalTypeSel"  >
	                         <option value="hour">小时</option>
			                 <option value="minute">分钟</option>
			              </select>
				       </td>
				       <td align="left">
				                              开始时间：<input type="text" data-toggle="timepicker" style="width:50px" value="00:00" id="beginTime">
				       </td>
				    </tr>
				     <tr>
				        <td style="width: 300px"></td>
				        <td align="left">
				                              结束时间：<input type="text" data-toggle="timepicker" style="width:50px" value="23:59" id="endTime">
				        </td>
				    </tr>
				  </tbody>
				</table>
				<table class="sui-table table-nobordered table-no-background" style="border: 1px solid #ddd;" id="keepTimeTable">
				  <thead>
				    <tr>
				      <th colspan="2" style="background-color:#f4f4f4"> 
				        <label class="checkbox pull-left">
				                               持续时间
				        </label>
				      </th>
				    </tr>
				  </thead>
				  <tbody>
				    <tr>
				       <td style="width: 300px">
				          <span>开始时间:</span>
						  <input id="beginDate" type="text" value=""  data-toggle="datepicker" readonly="readonly" format="yyyy-mm-dd" todayHighlight="false">
				       </td>
				       <td>
				          <label data-toggle="radio" class="radio-pretty inline">
						    <input type="radio" name="endDateRadio" value="hasEndDate" ><span>结束日期:</span>
						  </label>
						  <input id="endDate" type="text" data-toggle="datepicker" value=""  readonly="readonly" format="yyyy-mm-dd" todayHighlight="false">
				       </td>
				    </tr>
				    <tr>
				        <td style="width: 300px">
				        </td>
				        <td>
				           <label data-toggle="radio" class="radio-pretty inline checked">
						    <input type="radio" name="endDateRadio" value="noEndDate"><span>无结束日期</span>
						  </label>
				        </td>
				    </tr>
				  </tbody>
				</table>
				<table class="sui-table table-nobordered table-no-background" style="border: 1px solid #ddd;">
				  <thead>
				    <tr>
				      <th colspan="2" style="background-color: #f4f4f4"> 
				        <label class="checkbox pull-left">
				                               摘要
				        </label>
				      </th>
				    </tr>
				  </thead>
				  <tbody>
				    <tr>
				       <td>
				           <span id="summarySpan"></span>
				       </td>
				       <td>
				       </td>
				    </tr>
				  </tbody>
				</table>
	     </div>
	  </div>
</div>
<div class="modal-footer">					
	<button class="sui-btn btn-primary btn-large" type="button" id="mgrs_ok">确定</button>
	<button class="sui-btn btn-default btn-large" type="button" data-dismiss="modal">关闭</button>
</div>
<style type="text/css">
<!--
.well {
    min-height: 20px;
    padding: 19px;
    margin-bottom: 20px;
    background-color: #f5f5f5;
    border: 1px solid #e3e3e3;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
}

@media (min-width: 768px)
.col-sm-6 {
    width: 50%;
    float: left;
}
-->
</style>
<script type="text/javascript" src="${ctxPath}/js/input-cron.js"></script> 