<%@page pageEncoding="UTF-8"%>
<div>
	<fieldset class="layui-elem-field layui-field-title">
		<legend>编辑节点</legend>
	</fieldset>
	<form class="layui-form" method="post" id="easyform"  autocomplete="off" data-mars="NodeDao.record" data-mars-prefix="node.">
		<input type="hidden" name="nodeId" value="${param.nodeId}"/>
		<input type="hidden" name="node.NODE_ID" value="${param.nodeId}"/>
		  <div class="layui-form-item">
		    <label class="layui-form-label">服务名称</label>
		    <div class="layui-input-block">
		      <input type="text" lay-verify="required" name="node.NODE_NAME" class="layui-input">
		    </div>
		  </div>
		    <div class="layui-form-item">
		    <label class="layui-form-label">服务访问地址</label>
		    <div class="layui-input-block">
		      <input type="text" lay-verify="required" placeholder="http(s)://ip:port" onchange="setInputVal(this.value);" id="nodeUrl" name="node.NODE_URL" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">服务IP</label>
		    <div class="layui-input-block">
		      <input type="text" lay-verify="required" name="node.NODE_IP" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">服务端口</label>
		    <div class="layui-input-block">
		      <input type="number" lay-verify="required" name="node.NODE_PORT" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">备注</label>
		    <div class="layui-input-block">
		      <textarea name="node.REMARK" class="layui-textarea"></textarea>
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <div class="layui-input-block">
		      <button type="button" lay-submit lay-filter="submitBtn" class="layui-btn layui-btn-sm">保存</button>
		      <button type="button" onclick="deleteData()" style="display: none;" class="layui-btn layui-btn-sm layui-btn-warm delBtn">删除</button>
		    </div>
		  </div>
	</form>
</div>

<script>
   	
	
    var pkId = "${param.nodeId}";
    var recordObj={};
    $(function(){
    	if(g_securityKey==''){
    		layer.msg('请设置安全KEY');
    	}
    	if(pkId!=""&&pkId!="undefined"){
    		$(".delBtn").show();
	    	$("#easyform").render({success:function(result){
	    		if(result&&result["NodeDao.record"]&&result["NodeDao.record"].data){
	    			recordObj=result["NodeDao.record"].data;
				   	layui.use('form', function(){
				         var layuiform = layui.form;
						 layuiform.on('submit(submitBtn)', function(data){
							 saveData(data.field);
						 });
				     });
	    		}
	    	}});
    	}else{
		   	layui.use('form', function(){
		         var layuiform = layui.form;
				 layuiform.on('submit(submitBtn)', function(data){
					 saveData(data.field);
				 });
		     });
    	}
	});
    
	function setInputVal(val){
		
	}
	
	//保存
	function saveData(data) {
		 var url = $("#nodeUrl").val();
		  $.ajax({
			  url: url+"${ctxPath}/node/server?action=authTest&key="+g_securityKey,
			  data: {},
			  dataType: 'jsonp',
              jsonp: "callbackFunc",
              jsonpCallback: "jsonpCallback",
              contentType: "application/x-www-form-urlencoded; charset=UTF-8",
			  timeout:2000,
			  success: function(result){
				  if(result.state==1){
					  doSubmit();
				  }else{
					  layer.alert(result.msg);
				  }
			  },
			  error:function(e){
				  console.error(e);
				  alert(url+'网络不通');
			  },
			  before:function(){
				  
			  }
	     });
		 
		function jsonpCallback(result){
			console.log(data);
			if(result.state==1){
				  doSubmit();
			  }else{
				  layer.alert(result.msg);
			  }
		}
		  
		function doSubmit(){
			var op = pkId==''?'add':'update';
			ajax.remoteCall("${ctxPath}/servlet/node?action="+op,data,function(result) { 	
				if(result.state==1){
					layer.msg(result.msg,{icon: 1,time:800},function(){
						layer.closeAll();
						loadPage("node/node-config.jsp",{});
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
	}
	
	//删除
	function deleteData() {
		var data = form.getJSONObject("#easyform");
		var msgTxt = "是否删除数据";
		layer.confirm(msgTxt,{btn: [ '删除', '取消'],icon: 3, title:'警告',offset:'40%'}, 
			function(index){
				layer.close(index);
				ajax.remoteCall("${ctxPath}/servlet/node?action=del",{nodeId:data.nodeId},function(result) { 	
					if(result.state==1){
						layer.msg(result.msg,{icon: 1,time:800},function(){
							layer.closeAll();
							loadPage("node/node-config.jsp",{});
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}, 
			function(index){
				layer.close(index);
			}
		);
	}
	
</script>