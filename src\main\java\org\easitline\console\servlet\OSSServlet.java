package org.easitline.console.servlet;
import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.console.base.ConsoleBaseServlet;
import org.easitline.console.service.ConfigCryptorService;
import org.easitline.console.utils.AesUtils;

import com.alibaba.fastjson.JSONObject;

@WebServlet(urlPatterns="/servlet/oss")
public class OSSServlet extends ConsoleBaseServlet {
	private static final long serialVersionUID = 5436461740150076727L;

	public EasyResult actionForGetConf(){
		try {
			JSONObject object = new JSONObject();
			String names = getJsonPara("names");
			String[] array = names.split(",");
			for(String name:array) {
				String val = getConsoleQuery().queryForString("select CONF_VALUE from EASI_CONF where CONF_KEY = ?", name);
				object.put(name, ConfigCryptorService.decryptString(val, "GV"));
			}
			return EasyResult.ok(AesUtils.getInstance().encrypt(object.toJSONString()));
		} catch (SQLException e) {
			return EasyResult.fail();
		}
	}
	
	public EasyResult actionForSaveConf(){
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		JSONObject params = getJSONObject();
		String data = params.getString("encryptStr");
		data = AesUtils.getInstance().decrypt(data);
		JSONObject jsonObject = JsonKit.getJSONObject(data,null);
		try {
			EasyRecord record=new EasyRecord("EASI_CONF","CONF_KEY");
			
			String names = jsonObject.getString("names");
			String[] array = names.split(",");
			for(String name:array) {
				record.set("CAN_DELETE", 3);
				record.set("CONF_KEY", name);
				record.set("CONF_VALUE",jsonObject.getString(name));
				if(getConsoleQuery().queryForExist("select count(1) from EASI_CONF where CONF_KEY = ?",name)){
					getConsoleQuery().update(record);
				}else{
					getConsoleQuery().save(record);
				}
			}
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		this.addOperateLog("修改OSS配置",data);
		ServerContext.reload();
		return EasyResult.ok();
	}
	
	
	
	@Override
	protected String getResId() {
		return null;
	}

}
