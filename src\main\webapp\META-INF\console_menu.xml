<?xml version="1.0" encoding="UTF-8"?>
<resources>
   <resource id="server_conf" name="服务配置" roleId="1,2" state="0">  
   		<resource id="datasource" name="数据源管理" url="datasource/list.jsp" roleId="1,2" state="0"/>
		<resource id="application" name="应用管理" url="application/list.jsp" roleId="1,2" state="0"/>
   		<resource id="app-config" name="应用配置" url="application/config-mgr.jsp" roleId="1,2" state="0"/>
   </resource>
   
    <resource id="system_conf" name="系统配置" roleId="1,2" state="0">  
   		<resource id="config" name="平台参数配置" url="server/server-config.jsp" roleId="1,2" state="0"/>
		<resource id="config1" name="日志参数配置" url="server/log-conf.jsp" roleId="1,2" state="0"/>
		<resource id="config2" name="登录安全配置" url="server/security-conf.jsp" roleId="1,2" state="0"/>
		<resource id="config3" name="缓存服务配置" url="server/cache-adv-conf.jsp" roleId="1,2" state="0"/>
		<resource id="config4" name="MQ服务配置" url="server/mq-info.jsp" roleId="1,2" state="0"/>
		<resource id="config7" name="OSS存储配置" url="oss/oss-config.jsp" roleId="1,2" state="0"/>
		<resource id="config8" name="Elasticsearch配置" url="server/es-conf.jsp" roleId="1,2" state="0"/>
		<resource id="config6" name="定时任务配置" url="job/job-list.jsp" roleId="1,2" state="0"/>
		<resource id="config5" name="服务节点配置" url="node/node-config.jsp" roleId="1,2" state="0"/>
		<resource id="config9" name="Console账户管理" url="user/user-list.jsp" roleId="1" state="0"/>
		<resource id="service_command" name="服务命令" url="server/server-mgr.jsp" roleId="4" state="0"/>
   </resource>
   
   <resource id="platform_log" name="日志管理" roleId="1,2,9" state="0">  
		<resource id="log4" name="应用日志" url="logger/logger-list.jsp" roleId="1,2,9" state="0"/>
		<resource id="log5" name="系统日志" url="logger/tomcat-logger.jsp" roleId="1,2,9" state="0"/>
   		<resource id="log1" name="操作日志" url="logger/operate-log-list.jsp" roleId="1,2,9" state="0"/>
		<resource id="log2" name="登录日志" url="logger/login-log-list.jsp" roleId="1,2,9" state="0"/>
		<resource id="log3" name="升级日志" url="logger/upgrade-log-list.jsp" roleId="1,2,9" state="0"/>
		<resource id="log6" name="在线日志" url="/easitline-finder/sso.jsp?_blank" roleId="1,2,9" state="9"/>
		<resource id="log7" name="分析日志" url="/logger/logAnalysis.jsp" roleId="1,2,9" state="0"/>
   </resource>

    <resource id="sys_monitor" name="系统监控" roleId="1,2,9" state="0">
		<resource id="monitor1" name="Mars应用监控" url="/easitline-console/servlet/monitor/server?action=page" roleId="1" state="0"/>
		<resource id="monitor2" name="数据源监控" url="/easitline-console/servlet/monitor/druid/index.html?_blank" roleId="1,2" state="0"/>
		<resource id="monitor3" name="JVM环境变量信息" url="server/runtimeInfo.jsp" roleId="1,2,9" state="0"/>
		<resource id="monitor4" name="线程监控" url="/easitline-console/pages/server/java-threads.jsp" roleId="1,2,9" state="0"/>
		<resource id="monitor5" name="SOA服务监控" url="soa/service-list.jsp" roleId="1,2,9" state="0"/>
		<resource id="monitor6" name="定时任务监控" url="/easitline-console/pages/job/joblog-list.jsp" roleId="1,2,9" state="0"/>
   </resource>
   
</resources> 