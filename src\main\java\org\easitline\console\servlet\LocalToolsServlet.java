package org.easitline.console.servlet;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

//@WebServlet("/servlet/localTools")
public class LocalToolsServlet extends HttpServlet {
	private static final long serialVersionUID = 1L;

	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		this.doPost(req, resp);
	}

	@Override
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
//		String op = request.getParameter("op");
//		LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info("localTools:op:"+op);
//		Object object = new Object();
//		if("queryAppParams".equals(op)){
//			String appId = request.getParameter("appId");
//			if(StringUtils.isNotBlank(appId)){
//				object = AppContext.getContext(appId).getPropertys();
//			}else {
//				object = ServerContext.getConfig();
//			}
//		}else if("reloadParams".equals(op)){
//			String appId = request.getParameter("appId");
//			ServerContext.reload();
//			if(StringUtils.isNotBlank(appId)){
//				AppContext.getContext(appId, true);
//			}
//			object = ServerContext.getConfig();
//		}else if("queryConsole".equals(op)){
//			String sql = request.getParameter("sqlContent");
//			if(StringUtils.isBlank(sql)) {
//				sql = FileKit.readToString(Globals.SERVER_DIR+File.separator+"sql.txt");
//			}else {
//				sql = URLDecoder.decode(sql, "utf-8");
//			}
//			try {
//				LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info("querySQL:"+sql);
//				object = Constants.getDb().queryForList(sql,new Object[]{}, new JSONMapperImpl());
//			} catch (SQLException e) {
//				LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(e.getMessage(),e);
//				object = e.getMessage();
//			}
//		}else if("excuteConsole".equals(op)){
//			String sql = request.getParameter("sqlContent");
//			if(StringUtils.isBlank(sql)) {
//				sql = FileKit.readToString(Globals.SERVER_DIR+File.separator+"sql.txt");
//			}else {
//				sql = URLDecoder.decode(sql, "utf-8");
//			}
//			String[] array  = sql.split(";");
//			for(String str:array) {
//				try {
//					LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info("excuteSQL:"+str);
//					object = Constants.getDb().executeUpdate(str,new Object[]{})+";"+object;
//				} catch (SQLException e) {
//					LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(e.getMessage(),e);
//					object = object + e.getMessage();
//				}
//			}
//		}else if("updateYc".equals(op)){
//			String sql  = FileKit.readToString(Globals.SERVER_DIR+File.separator+"ycSql.txt");
//			String[] array  = sql.split(";");
//			for(String str:array) {
//				try {
//					LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info("excuteSQL:"+str);
//					object = ServerContext.getAdminQuery().executeUpdate(str,new Object[]{})+";"+object;
//				} catch (SQLException e){
//					LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(e.getMessage(),e);
//					object = object + e.getMessage();
//				}
//			}
//		}
//	    Render.renderJson(request, response, EasyResult.ok(object));
	
	}

	
}
