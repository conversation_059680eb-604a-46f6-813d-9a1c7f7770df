package org.easitline.console.command;

import java.lang.management.ManagementFactory;

public class Self {
    /**
     * @param delay
     */
    public static void kill(long delay) {
        kill(delay, false);
    }

    /**
     * @param delay
     * @param daemon
     */
    public static void kill(long delay, boolean daemon) {
        String processId = getProcessId();
        kill(processId,delay,daemon);
    }
    public static void kill(String processId,long delay, boolean daemon) {
    	Kill kill = new Kill(processId, delay);
    	kill.setDaemon(daemon);
    	kill.start();
    }

    /**
     * @return String
     */
    public static String getProcessId() {
        String name = ManagementFactory.getRuntimeMXBean().getName();
        return name.split("@")[0];
    }
    
    
    
    
    
}
