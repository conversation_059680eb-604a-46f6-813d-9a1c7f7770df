a{color: #1fa1df;}
.layui-body #layui-main {
	margin: 15px;
}

.layui-elem-field.layui-field-title {
	margin-top: 20px
}

.layui-elem-field legend{
	font-size:18px
}

.layui-table tr td,
.layui-table tr th {
	color: #666;
	font-size: 14px;
}
.layui-table td, .layui-table th{
	padding: 9px 6px;
}
.layui-table tr td a {
	color: #28a3ef
}

.layui-form-label {
	text-align: right;
	color:#333;
	width: 150px;
}

.layui-input-block {
	margin-left: 200px;
	width:500px
}

.layui-input, .layui-select{
	height:34px;
	line-height:34px
}
   
table.text-center tr td,
table.text-center tr th {
	text-align: center;
}

.text-c {
	text-align: center!important;
}

.text-l {
	text-align: left!important;
}

.text-r {
	text-align: right!important;
}

.td-textcut{
    min-width: 100px;
    max-width: 160px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.layer-pannel{border:1px solid #e6e6e6;}
.layer-pannel+.layer-pannel{margin-top:10px}
.layer-pannel .layer-pannel-header{background-color: #f4f4f4;height:38px;line-height:38px;padding-left:10px}
.layer-pannel .layer-pannel-content{border-top:1px solid #e6e6e6;padding:10px}
.layer-pannel-header h4{font-weight: 600;font-size:13px}
.layer-pannel-content .summary-text{color:#333}

.layui-layout-admin .layui-header{
	background-image: linear-gradient(to right, #3ab5b0 0%, #3d99be 31%, #56317a 100%);
}

.layui-nav{
	background-image: linear-gradient(to right, #434343 0%, black 100%);
}


