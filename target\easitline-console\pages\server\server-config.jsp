<%@page pageEncoding="UTF-8"%>
<style>
	 .layui-form-item .layui-form-label{width: 180px;}
	 .layui-form-item .layui-input-inline{width:500px}
	 #configList .layui-form-item .layui-input-inline,#addConfigDiv{margin-top: 8px;}
	 #addConfigDiv .layui-input-inline{margin-top: 8px;}
	 #configList .layui-form-label input{width: 80%;float: right;}
	 #configList .input,.del{border: none;}
	 #configList .del{display: none;}
	 #configList .title{color: #333;text-align: right;}
	 
</style>
<div>
	<blockquote class="layui-elem-quote">
		<span onclick="getGlobalConfig()">通过这里进行平台的运行时的相关配置，包括：日志的输出级别、登录验证码的校验等,修改缓存类型和地址等中间件地址需重启。</span>
		<a style="display: none;" href="javascript:;" onclick="exportConf();">导出配置</a>
	</blockquote>

	<fieldset class="layui-elem-field layui-field-title">
		<legend>平台参数配置</legend>
	</fieldset>
	
	<form class="layui-form" id="easyform" data-mars="server.config" data-mars-prefix="con.">
	  <div class="layui-form-item">
	    <label class="layui-form-label">业务系统名称</label>
	    <div class="layui-input-inline">
	      <input type="text" name="con.G_SYS_NAME" class="layui-input" value="">
	    </div>
	    <div class="layui-form-mid layui-word-aux">业务系统名称，用于在portal所在浏览器中显示的系统名称，如：XX系统</div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">Mars节点名称</label>
	    <div class="layui-input-inline">
	      <input type="text" name="con.G_NODE_NAME" class="layui-input" value="">
	    </div>
	    <div class="layui-form-mid layui-word-aux">描述当前Mars的节点名称，通常用于负载均衡的情景下，区分不同的mars</div>
	  </div>
	   <div class="layui-form-item">
	    <label class="layui-form-label">环境类型</label>
	    <div class="layui-input-inline">
	      <select name="con.PROFILE_ACTIVE" class="layui-select">
	        <option value="">请选择</option>
	        <option value="dev">开发环境</option>
	        <option value="test">测试环境</option>
	        <option value="pre">预发环境</option>
	        <option value="prod">生产环境</option>
	      </select>
	    </div>
	    <div class="layui-form-mid layui-word-aux">识别环境类型以便其他操作！</div>
	  </div>
	    <div class="layui-form-item">
	    <label class="layui-form-label">Console安全KEY</label>
	    <div class="layui-input-inline">
	        <input type="text" name="con.SECURITY_KEY" placeholder="可自定义数字字母组合,不要输入符号"  class="layui-input" value="">
	    </div>
	     <div class="layui-form-mid layui-word-aux">
	     	 <a href="javascript:genSecurityKey();">生成</a>
	     	 <small style="color: red;">请牢记保存key,定期修改KEY</small>
	     </div>
	  </div>
      <div class="layui-form-item">
	    <label class="layui-form-label">Console启用安全入口</label>
	    <div class="layui-input-inline">
	      <input type="checkbox" name="con.SECURITY_ENTER" value="1" lay-skin="primary">
	    </div>
	      <small style="color: red;">启用后登录必须加上key才可以访问， ${ctxPath}/login?key=安全KEY</small>
      </div>
	    <div class="layui-form-item">
	    <label class="layui-form-label">Console访问白名单</label>
	    <div class="layui-input-inline">
	        <input type="text" name="con.CONSOLE_ALLOC_ACCESS_IP" placeholder="客户端ip地址,多个逗号隔开" class="layui-input" value="">
	    </div>
	     <div class="layui-form-mid layui-word-aux">
	     	 <small style="color: red;">限制哪台电脑ip访问console</small>
	     </div>
	  </div>
	    <div class="layui-form-item">
	    <label class="layui-form-label">Console服务端白名单</label>
	    <div class="layui-input-inline">
	        <input type="text" name="con.ALLOC_ACCESS_SERVER_ADDR" placeholder="填写Mars的Ip,172.16,192.168, 多个逗号隔开" class="layui-input" value="">
	    </div>
	     <div class="layui-form-mid layui-word-aux">
	     	 <small style="color: red;">限制只能通过配置过的Mars地址才可以访问console</small>
	     </div>
	  </div>
      <div class="layui-form-item">
	    <label class="layui-form-label">Console包部署路径</label>
	    <div class="layui-input-inline">
	      <input type="text" name="con.CONSOLE_DEPLOY_DIR" placeholder="若路径非/开头表示是当前容器根目录" class="layui-input" value="">
	    </div>
	    <small style="color: red;">如业务模块和console包分开部署，请填写此目录</small>
      </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">缺省数据源</label>
	    <div class="layui-input-inline">
	      <select name="con.G_SYS_DS" class="layui-select" data-mars="datasource.dsDict">
	        <option value="">请选择</option>
	      </select>
	    </div>
	    <div class="layui-form-mid layui-word-aux">通过指定缺省数据源来替代default-ds数据源，仅限于开发时候配置！</div>
	  </div>
	  <div class="layui-form-item"  style="display: none;">
	    <label class="layui-form-label">缺省Portal风格</label>
	    <div class="layui-input-inline">
	      <input type="text" name="con.G_PORTAL_STYLE" class="layui-input" value="">
	    </div>
	    <div class="layui-form-mid layui-word-aux">系统内置了多种portal样式来满足各类项目的需要，取值：v1/v2/v3等</div>
	  </div>
	  <div class="layui-form-item" style="display:none">
	    <label class="layui-form-label">Portal默认页面</label>
	    <div class="layui-input-inline">
	      <input type="text" name="con.G_WELCOME_PATH" class="layui-input" value="">
	    </div>
	    <div class="layui-form-mid layui-word-aux">Portal首页链接</div>
	  </div>
	  <div class="layui-form-item" style="display:none">
	    <label class="layui-form-label">缺省Portal应用</label>
	    <div class="layui-input-inline">
	      <input type="text" name="con.G_ROOT_URL" class="layui-input" value="">
	    </div>
	    <div class="layui-form-mid layui-word-aux">系统缺省显示配置的应用，缺省为：/easitline-portal，不配置则显示Mars的登录页面</div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">Lucene索引存储目录</label>
	    <div class="layui-input-inline">
	      <input type="text" name="con.LUCENE_INDEX_PATH" class="layui-input" value="">
	    </div>
	    <div class="layui-form-mid layui-word-aux">用于指定Lucene全文检索索引的存储目录，例如：/home/<USER>/indexdir</div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">缺省文件存储目录</label>
	    <div class="layui-input-inline">
	      <input type="text" name="con.G_EFS_DIR"  class="layui-input" value="">
	    </div>
	    <div class="layui-form-mid layui-word-aux">文件上传存储目录，例如：/home/<USER>/div>
	  </div>
	  <div class="layui-form-item layui-hide">
	    <label class="layui-form-label">版本中心地址</label>
	    <div class="layui-input-inline">
	      <input type="text" name="con.G_VERSION_CENTER_URL"  class="layui-input" value="http://183.6.189.146:3123">
	    </div>
	    <div class="layui-form-mid layui-word-aux">用来检测获取最新应用版本。</div>
	  </div>
	  
	  <div id="defaultConfigList" data-mars="server.defaultList" data-template="default-list-template"></div>
	  
	  <div id="configList" data-mars="server.moreList" data-template="list-template"></div>
	  
	  <div class="layui-form-item">
	    <label class="layui-form-label">启动调度服务</label>
	    <div class="layui-input-inline" style="width:20px;">
	      <input type="checkbox" name="con.G_JOB_SERVER" lay-skin="primary">
	    </div>
	    <div class="layui-form-mid layui-word-aux" style="width: 500px;">如果勾选了启动调度服务，则当前节点的mars将运行已经配置的调度服务，通常在集群情况下，只需启动一个调度服务器。</div>
	  </div>
	  <div class="layui-form-item" id="addConfigDiv" style="display: none;">
				<label class="layui-form-label"><input type="text" style="width: 100px;float: right;"  placeholder="参数名" name="config.CONF_TITLE" class="layui-input"/></label>
				<div class="layui-input-inline">
					<input type="hidden" value="0" name="config.CAN_DELETE">
					<input type="text" style="display: inline;width: 120px;" placeholder="参数Key" name="config.CONF_KEY" class="layui-input">
					<input type="text" style="display: inline;width: 216px"  placeholder="参数Value" name="config.CONF_VALUE" class="layui-input">
				</div>
				<div class="layui-form-mid layui-word-aux"  style="width: 500px;"><input type="text" name="config.CONF_DESC"  placeholder="参数描述" class="layui-input"/></div>
		</div>
	    <script id="default-list-template" type="text/x-jsrender">
			{{for data}}
				<div class="layui-form-item">
					<label class="layui-form-label">{{:CONF_TITLE}}</label>
					<div class="layui-input-inline">
						<input type="hidden" value="{{:ORDER_INDEX}}" name="{{:CONF_KEY}}.ORDER_INDEX" class="layui-input">
						<input type="hidden" value="{{:CAN_DELETE}}" name="{{:CONF_KEY}}.CAN_DELETE" class="layui-input">
						<input type="hidden" value="{{:CONF_KEY}}" name="{{:CONF_KEY}}.CONF_KEY" class="layui-input">
						<input type="hidden" value="{{:CONF_KEY}}" name="confKeys.{{:CONF_KEY}}" class="layui-input">
						<input type="text" onmouseenter="layer.tips('{{:CONF_KEY}}',$(this))" title="{{:CONF_KEY}}" value="{{:CONF_VALUE}}"  placeholder="参数值" name="{{:CONF_KEY}}.CONF_VALUE" class="layui-input">
					</div>
					<div style="padding-top:4px;">
						 {{:CONF_DESC}}
					</div>
				</div>
			{{/for}}					         
	   </script>
	    <script id="list-template" type="text/x-jsrender">
			{{for data}}
				<div class="layui-form-item">
					<label class="layui-form-label"><input type="text" name="{{:CONF_KEY}}.CONF_TITLE"  placeholder="参数名" value="{{:CONF_TITLE}}" class="layui-input title input"/></label>
					<div class="layui-input-inline">
						<input type="hidden" value="{{:ORDER_INDEX}}" name="{{:CONF_KEY}}.ORDER_INDEX" class="layui-input">
						<input type="hidden" value="{{:CAN_DELETE}}" name="{{:CONF_KEY}}.CAN_DELETE" class="layui-input">
						<input type="hidden" value="{{:CONF_KEY}}" name="{{:CONF_KEY}}.CONF_KEY" class="layui-input">
						<input type="hidden" value="{{:CONF_KEY}}" name="confKeys.{{:CONF_KEY}}" class="layui-input">
						<input type="text" onmouseenter="layer.tips('{{:CONF_KEY}}',$(this))" title="{{:CONF_KEY}}" value="{{:CONF_VALUE}}"  placeholder="参数值" name="{{:CONF_KEY}}.CONF_VALUE" class="layui-input">
					</div>
					<div class="layui-form-mid layui-word-aux"  style="width: 200px;"><input type="text" name="{{:CONF_KEY}}.CONF_DESC" value="{{:CONF_DESC}}" style="color:#777"  placeholder="" class="layui-input input"/>
					</div>
					{{if CAN_DELETE!=1}}<a style="font-size:12px;margin-top:15px;color:#1E9FFF" href="javascript:deleteConfig('{{:CONF_KEY}}')" class="del">删除</a>{{/if}}				
					</div>
			{{/for}}					         
	   </script>
	  <div class="layui-form-item">
	    <div class="layui-input-block" style="margin-left:210px">
	      <button type="button" class="layui-btn layui-btn-small layui-btn-warm" onclick = "$('#addConfigDiv').show();$(this).hide();" style="padding: 0 20px;"> 新增 </button>
	      <button type="button" class="layui-btn layui-btn-small layui-btn-normal" onclick = "dosave()" style="padding: 0 20px;"> 保存 </button>
	    </div>
	  </div>
	</form>
</div>

<script>
	
	$(function(){
		$("#easyform").render({success:function(data){
			if(data&&data["server.config"]&&data["server.config"].data){
				var encryptStr = data["server.config"].data;
				var datas = JSON.parse(aesDecrypt(encryptStr));
				
				fillRecord(datas,'con.',',','#easyform');
				
				$("input[type='checkbox']").each(function(){
					var name = $(this)[0].name;
					name = name.replace("con.","");
					if(datas[name] =="true"){
						$(this).attr("checked","checked");
					}
				});
				$(this).find("input").css("border","none");
				$("#configList .layui-form-item").mouseenter(function(){
					$(this).find(".input").css("border","1px solid #e6e6e6");
					$(this).find(".del").css("display","inline-block");
				});
				$("#configList .layui-form-item").mouseleave(function(){
					$(this).find(".input").css("border","none");
					$(this).find(".del").css("display","none");
				});
			}
			//开启表单渲染
			layui.use('form', function(){
				var layuiForm = layui.form;
			    layuiForm.render('select'); //刷新select选择框渲染
			    layuiForm.render('checkbox');
		    });
		}});
	});
	function dosave(){
 	    var data = form.getJSONObject("easyform"); 
 		ajax.remoteCall("${ctxPath}/servlet/serverInfo?action=saveConfig", data, function(result) { 
 			if(result.state == 1){
 				layer.msg(result.msg,{icon:1,time:800},function(){
 					layer.closeAll();
 				});
 			}else{
 				layer.alert(result.msg);
 			}
 		});
   }
	function exportConf(){
 		ajax.remoteCall("${ctxPath}/index?action=consoleInfo",{}, function(result) { 
	 		layer.msg("已导出至："+result.data);
 		});
    }
	
	function getGlobalConfig(){
 		ajax.remoteCall("${ctxPath}/servlet/serverInfo?action=globalConfig",{}, function(result) { 
	 		layer.alert(JSON.stringify(result));
 		});
   }
	function configReload(){
 		ajax.remoteCall("${ctxPath}/servlet/serverInfo?action=reloadConfig",{}, function(result) { 
	 		layer.alert(JSON.stringify(result));
 		});
   }
	function reloadLog(){
 		ajax.remoteCall("${ctxPath}/servlet/serverInfo?action=reloadLog",{}, function(result) { 
	 		layer.msg('ok');
 		});
    }
	
	function genSecurityKey(){
 		ajax.remoteCall("${ctxPath}/servlet/serverInfo?action=genSecurityKey",{}, function(result) { 
	 		$("[name='con.SECURITY_KEY']").val(result.data);
 		});
    }
	
	
	function deleteConfig(confKey){
 		ajax.remoteCall("${ctxPath}/servlet/serverInfo?action=deleteConfig",{confKey:confKey}, function(result) { 
 			if(result.state == 1){
 				layer.msg(result.msg,{icon:1,time:800},function(){
 					location.reload();
 				});
 			}else{
 				layer.alert(result.msg);
 			}
 		});
   }
</script>
