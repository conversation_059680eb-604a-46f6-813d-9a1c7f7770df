<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
   
    <parent>
        <groupId>com.yunqu.mars</groupId>
        <artifactId>mars-all</artifactId>
        <version>3.5</version>
    </parent>

    <artifactId>easitline-console</artifactId>
    <packaging>war</packaging>
    <version>3.5.1</version>

	<properties>
	    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
	    <java.version>1.8</java.version>
	    <maven.compiler.source>${java.version}</maven.compiler.source>
	    <maven.compiler.target>${java.version}</maven.compiler.target>
	    <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
	    <maven-war-plugin.version>3.3.2</maven-war-plugin.version>
	    <maven.build.timestamp.format>yyyy-MM-dd HH:mm:ss</maven.build.timestamp.format>
	    <!-- <maven.test.skip>true</maven.test.skip> -->
	    <tomcat.version>8.0.53</tomcat.version>
	    <sigar.version>1.6.4</sigar.version>
	</properties>
	
	<dependencies>
         <dependency>
             <groupId>org.apache.tomcat</groupId>
             <artifactId>tomcat-catalina</artifactId>
             <version>${tomcat.version}</version>
             <scope>provided</scope>
         </dependency>
         <dependency>
            <groupId>io.klib.tools</groupId>
            <artifactId>org.hyperic.sigar</artifactId>
            <version>${sigar.version}</version>
            <scope>provided</scope>
        </dependency>
        
          <!-- Elasticsearch Java Client -->
        <dependency>
            <groupId>co.elastic.clients</groupId>
            <artifactId>elasticsearch-java</artifactId>
            <version>8.15.3</version>
            <scope>provided</scope>
        </dependency>
        
        <!-- Elasticsearch REST Client -->
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-client</artifactId>
            <version>8.15.3</version>
            <scope>provided</scope>
        </dependency>
        
        <!-- HttpAsyncClient -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpasyncclient</artifactId>
            <version>4.1.5</version>
            <scope>provided</scope>
        </dependency>
        
        <!-- HttpCore NIO -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore-nio</artifactId>
            <version>4.4.16</version>
            <scope>provided</scope>
        </dependency>
        
        <!-- Jackson Annotations -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.18.1</version>
            <scope>provided</scope>
        </dependency>
        
        <!-- Jackson Core -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.18.1</version>
            <scope>provided</scope>
        </dependency>
        
        <!-- Jackson Databind -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.18.1</version>
            <scope>provided</scope>
        </dependency>
        
        <!-- Jakarta JSON API -->
        <dependency>
            <groupId>jakarta.json</groupId>
            <artifactId>jakarta.json-api</artifactId>
            <version>2.1.3</version>
            <scope>provided</scope>
        </dependency>
        
        <!-- OpenTelemetry API -->
        <dependency>
            <groupId>io.opentelemetry</groupId>
            <artifactId>opentelemetry-api</artifactId>
            <version>1.43.0</version>
            <scope>provided</scope>
        </dependency>
        
        <!-- OpenTelemetry Context -->
        <dependency>
            <groupId>io.opentelemetry</groupId>
            <artifactId>opentelemetry-context</artifactId>
            <version>1.43.0</version>
            <scope>provided</scope>
        </dependency>
        
        <!-- Parsson -->
        <dependency>
            <groupId>org.eclipse.parsson</groupId>
            <artifactId>jakarta.json</artifactId>
            <version>1.1.7</version>
            <scope>provided</scope>
        </dependency>
	</dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <excludes>
                        <exclude>**/test/**</exclude>
                    </excludes>
                 </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>${maven-war-plugin.version}</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <packagingExcludes>
                        WEB-INF/lib/*.jar
                    </packagingExcludes>
                </configuration>
            </plugin>

            <!-- Maven Surefire Plugin for JUnit 5 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M7</version>
                <configuration>
                    <includes>
                        <include>**/*Test.java</include>
                    </includes>
                </configuration>
            </plugin>

            <plugin>
				 <groupId>com.yunqu.mars.plugin</groupId>
				 <artifactId>console-upgrade</artifactId>
				 <version>1.0</version>
				<executions>
					<execution>
						<goals>
							<goal>generate-version</goal>
							<goal>push-mars</goal>
						</goals>
						<configuration>
							<cleanLibDirectory>true</cleanLibDirectory>
							<primaryVersion>3.5</primaryVersion>
							<versionFormat>yyyyMMdd</versionFormat>
							<versionOne>true</versionOne>
							<enableWarPushMars>true</enableWarPushMars>
							<consoleUrl>http://172.16.85.100:9060/easitline-console</consoleUrl>
							<consoleUsername>admin@mars</consoleUsername>
							<consolePassword>admin#mars</consolePassword>
						</configuration>
					</execution>
				</executions>
			</plugin>
        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>
</project>
