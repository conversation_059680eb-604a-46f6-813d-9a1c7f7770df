<%@page pageEncoding="UTF-8"%>
<div>
	<fieldset class="layui-elem-field layui-field-title">
		<legend>SOA服务列表</legend>
	</fieldset>

	<div class="layui-form" style="padding-left:15px">
		<table class="layui-table">
			<thead>
				<tr>
					<th>服务ID</th>
					<th>服务名称</th>
					<th>所属应用</th>
					<th>服务实现类</th>
					<th>服务描述</th>
				</tr>
			</thead>
			<tbody id="data-area">
			</tbody>
		</table>
	</div>	
</div>
				<script id="list-template" type="text/x-jsrender">
				{{for}}
				<tr>
					<td class="td-textcut" onclick="layer.alert('{{:serviceId}}');" title="{{:serviceId}}">{{:#getIndex()+1}}、{{:serviceId}}</td>
					<td class="td-textcut" onclick="layer.alert('{{:serviceName}}');" title="{{:serviceName}}">{{:serviceName}}</td>
					<td title="{{:appName}}">{{:appName}}</td>
					<td class="td-textcut" onclick="layer.alert('{{:className}}');" title="{{:className}}">{{:className}}</td>
					<td class="td-textcut" onclick="layer.alert('{{:description}}');" title="{{:description}}">{{:description}}</td>
				</tr>
				{{/for}}
				</script>

<script type="text/javascript">
	$(function(){
		getData();
	});
	
	function getData(){
		ajax.remoteCall("${ctxPath}/servlet/soa?action=list", {},function(result){  
			if(result.state==1){
				//模板渲染数据
				var jsRenderTpl = $.templates("#list-template");
	          	var html = jsRenderTpl(result.data);
				$("#data-area").html(html); 
			}
		});	
	}
</script>


 