package org.easitline.console.filter;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.easitline.console.base.Constants;

@WebFilter(urlPatterns={"/pages/*","/servlet/*","/index/*","/webcall/*"})
public class SecurityFilter implements Filter {

    public SecurityFilter() {
    	
    }

	public void destroy() {
		
	}

	public void doFilter(ServletRequest request, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
		request.setAttribute("path", request.getServletContext().getContextPath());
		if(request instanceof HttpServletRequest){
			String acct = (String)((HttpServletRequest) request).getSession().getAttribute("MARS_CONSOLE_USER");
			if(acct == null || "".equals(acct)){
				HttpServletResponse response = (HttpServletResponse)servletResponse;  
				response.sendRedirect(Constants.getContextPath()+"/login");
				return;
			}
			chain.doFilter(request, servletResponse);
		}
	}

	public void init(FilterConfig fConfig) throws ServletException {
	}

}
