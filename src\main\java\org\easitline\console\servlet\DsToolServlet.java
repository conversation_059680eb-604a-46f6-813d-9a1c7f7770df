package org.easitline.console.servlet;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.Globals;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.console.base.ConsoleBaseServlet;
import org.easitline.console.service.ConfigCryptorService;
import org.easitline.console.utils.AesUtils;
import org.easitline.console.utils.PropKit;
import org.easitline.console.utils.ScriptHelper;

import com.alibaba.druid.DbType;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.util.JdbcConstants;
import com.alibaba.fastjson.JSONObject;

@WebServlet("/servlet/dsTool")
public class DsToolServlet extends ConsoleBaseServlet{
	
	private static final long serialVersionUID = -4326655512821449505L;

	private static long authTime = 0;
	
	public void actionForCreateJson(){
		JSONObject jsonObject = getJSONObject();
		String dsName = jsonObject.getString("dsName");
		String dbName = jsonObject.getString("dbName");
		if(StringUtils.isBlank(dbName)){
			renderJson(EasyResult.fail());
			return;
		}
		JSONObject array = new ScriptHelper(EasyQuery.getQuery(dsName)).buildTableJson(dbName);
		String fileName = Globals.DB_DIR+File.separator+"schema"+File.separator+dbName.toUpperCase()+".json";
		try {
			File file=new File(fileName);
			if(file.exists()){file.delete();}
			FileKit.saveToFile(array.toJSONString(),fileName);
		} catch (IOException e) {
			this.error(e.getMessage(),e);
			renderJson(EasyResult.fail(e.getMessage()));
			return;
		}
		renderJson(EasyResult.ok(null,fileName+"生成OK"));
	}
	
	public EasyResult actionForEditContent() throws SQLException {
		this.warn(WebKit.getIP(getRequest())+">>>"+getRequest().getHeader("User-Agent"),null);
		if(!hasSqlExcuteAuth()) {
			return EasyResult.fail("无权限");
		}
		JSONObject jsonObject = getJSONObject();
		String dsName=jsonObject.getString("dsName");
		String inputPwd=jsonObject.getString("pwd");
		String pwd=PropKit.get("sqlExcutePwd","");
		if(StringUtils.isBlank(inputPwd)){
			return EasyResult.error(403,"请输入密码");
		}
		if(!inputPwd.equals(pwd)){
			if(!inputPwd.equals(pwd)){
				String password=getConsoleQuery().queryForString("select PASSWORD from EASI_DS_INFO where SYS_DS_NAME= ? ", dsName);
				if(password!=null){
					password = ConfigCryptorService.decryptString(password, "DS");
				}
				if(!inputPwd.equals(password)){
					this.error("pwd is error.",null);
					return EasyResult.error(403,"密码错误!");
				}
			}
		}
		String sqlStr = jsonObject.getString("contentStr");
		if(StringUtils.isBlank(sqlStr)){
			return EasyResult.fail("SQL语句不能为空！");
		}
		sqlStr = AesUtils.getInstance().decrypt(sqlStr);
		sqlStr = sqlStr.replaceAll("\\{", "'");
		sqlStr = sqlStr.replaceAll("\\}", "'");
		sqlStr = sqlStr.replaceAll("\\[", "'");
		sqlStr = sqlStr.replaceAll("\\]", "'");
		
		if(StringUtils.isBlank(dsName)){
			return EasyResult.fail("数据源名称不能为空！");
		}
		EasyQuery query=null;
		if("consoleDs".equals(dsName)){
			query=getConsoleQuery();
		}else{
			query=EasyQuery.getQuery(jsonObject.getString("dsName"));
		}
		
		this.warn("执行SQL："+sqlStr,null );
		long begin=System.currentTimeMillis();
		long end=0;
		try {
			int row=query.executeUpdate(sqlStr);
			end=System.currentTimeMillis();
			JSONObject object=new JSONObject();
			object.put("time",end-begin);
			object.put("sql", sqlStr);
			object.put("row",row);
			return EasyResult.ok(object);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			
			JSONObject object=new JSONObject();
			object.put("time",end-begin);
			object.put("sql", sqlStr);
			object.put("row",0);
			EasyResult result=new EasyResult();
			result.addFail(e.getMessage());
			result.setData(object);
			return result;
		}
	}
	
	public EasyResult actionForGetContent() throws Exception{
		this.warn(WebKit.getIP(getRequest())+">>>"+getRequest().getHeader("User-Agent"),null);
		if(!hasSqlExcuteAuth()) {
			return EasyResult.fail("无权限");
		}
		JSONObject jsonObject = getJSONObject();
		String dsName=jsonObject.getString("dsName");
		String inputPwd=jsonObject.getString("pwd");
		String pwd=PropKit.get("sqlExcutePwd","");
		if(StringUtils.isBlank(dsName)){
			return EasyResult.error(403,"dsName is empty.");
		}
		if(StringUtils.isBlank(inputPwd)){
			return EasyResult.error(403,"请输入密码");
		}
		if(!inputPwd.equals(pwd)){
			String password=getConsoleQuery().queryForString("select PASSWORD from EASI_DS_INFO where SYS_DS_NAME= ? ", dsName);
			if(password!=null){
				password = ConfigCryptorService.decryptString(password, "DS");
			}
			if(!inputPwd.equals(password)){
				this.error("pwd is error.",null);
				return EasyResult.error(403,"密码错误!");
			}
		}
		
		String sqlStr = jsonObject.getString("contentStr");
		if(StringUtils.isBlank(sqlStr)){
			return EasyResult.fail("SQL语句不能为空！");
		}
		
		sqlStr = AesUtils.getInstance().decrypt(sqlStr);
		sqlStr = sqlStr.replaceAll("\\{", "'");
		sqlStr = sqlStr.replaceAll("\\}", "'");
		sqlStr = sqlStr.replaceAll("\\[", "'");
		sqlStr = sqlStr.replaceAll("\\]", "'");
		
		if(StringUtils.isBlank(jsonObject.getString("dsName"))){
			return EasyResult.fail("数据源名称不能为空！");
		}
		EasyQuery query=null;
		if("consoleDs".equals(dsName)){
			query=getConsoleQuery();
		}else{
			query=EasyQuery.getQuery(dsName);
		}
		query.setMaxRow(jsonObject.getIntValue("maxRow"));	
		long begin=System.currentTimeMillis();
		long end=0;
		try {
			this.warn("执行SQL："+sqlStr,null );
			List<JSONObject> obj=query.queryForList(sqlStr, new Object[]{}, new JSONMapperImpl());
			end=System.currentTimeMillis();
			
			JSONObject object=new JSONObject();
			object.put("time",end-begin);
			object.put("sql", sqlStr); 
			object.put("data",obj);
			object.put("row",obj.size());
			return EasyResult.ok(object);
		} catch (Exception ex) {
			this.error(ex.getMessage(), ex);
			JSONObject object=new JSONObject();
			object.put("time",end-begin);
			object.put("sql", sqlStr);
			object.put("row",0);
			
			EasyResult result=new EasyResult();
			result.addFail(ex.getMessage());
			result.setData(object);
			return result;
		}
		
	}
	
	public EasyResult actionForAddAuth() {
		authTime = System.currentTimeMillis();
		return EasyResult.ok();
	}
	
	protected boolean hasSqlExcuteAuth() {
		//30分钟内
		if(System.currentTimeMillis() - authTime < 30*1000*60) {
			return true;
		}
		return PropKit.getBoolean("hasSqlExcuteAuth", false);
	}
	
	public EasyResult actionForFormatContent() {
		String sql = getPara("content");
		EasyQuery query = this.getQuery();
		DBTypes type = query.getTypes();
		DbType dbType = JdbcConstants.MYSQL;
		if(type==DBTypes.ORACLE) {
			dbType = JdbcConstants.ORACLE;
		}else if(type==DBTypes.PostgreSql){
			dbType = JdbcConstants.POSTGRESQL;
		}
		String result = SQLUtils.format(sql,dbType);
//      String result_lcase = SQLUtils.format(sql, JdbcConstants.MYSQL, SQLUtils.DEFAULT_LCASE_FORMAT_OPTION);
		return EasyResult.ok(result);
	}
}
