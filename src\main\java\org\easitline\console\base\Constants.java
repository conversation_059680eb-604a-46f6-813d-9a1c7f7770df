package org.easitline.console.base;

import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasyQuery;

public class Constants {

	public final static String EASITLINE_CONSOLE = "easitline-console";
	
	//默认数据源名称
	public final static String DEFAULT_DS_NAME = "default-ds";     
	
	//默认数据源描述
	public final static String DEFAULT_DS_DESC = "默认数据源";     
	
	public static String serverBasePath = null;
	
	public static String contextPath = null;
	
	public static String contextPrefix = null;
	
	public static boolean consoleDsPool = true;
	
	/**
	 * 调度服务前缀
	 */
	public final static String CRON_SERVICE_PREFIX = "CRON-";
	
	
	public static EasyQuery getDb() {
		if(consoleDsPool) {
			return ServerContext.getSqliteQuery();
		}else {
			return ServerContext.getSqliteQuery();
		}
	}
	
	public static String getContextPath() {
		if(contextPath==null) {
			String prefix = getContextPrefix();
			contextPath = prefix+"/"+EASITLINE_CONSOLE;
		}
		return contextPath;
	}
	
	public static String getContextPrefix() {
		if(contextPrefix==null) {
			return System.getProperty("mars.app.context.path", "");
		}
		return contextPrefix;
	}
	
	public static boolean fileTamperedCheck() {
		String fileTamperedCheck = AppContext.getContext(EASITLINE_CONSOLE).getProperty("fileTamperedCheck", "0");
		return "1".equals(fileTamperedCheck);
	}
	
	/**
	 * 调度任务类型
	 */
	public static enum JobType {
		
		SERIAL(1, "串行"),
		CONCURENT(2, "并行");
		
		private final int key;
		private final String value;
		
		private JobType(int key, String value) {
			this.key = key;
			this.value = value;
		}

		public int getKey() {
			return key;
		}

		public String getValue() {
			return value;
		}
		
		public static String getValue(int key){
			for(JobType inst : values()){
				if(key==inst.key)
					return inst.value;
			}
			throw new IllegalArgumentException("不支持的常量：" + key);
		}
		
		public static JobType valueOf(int key){
			for(JobType inst : values()){
				if(key==inst.key)
					return inst;
			}
			throw new IllegalArgumentException("不支持的常量：" + key);
		}
	}
	
	/**
	 * 调度任务状态（用于EASI_JOB_LOG表的JOB_STATE字段）
	 */
	public static enum JobExecState {
		
		RUNNING(0, "调度中"),
		SUCCESS(1, "成功"),
		FAIL(-1, "失败");
		
		private final int key;
		private final String value;
		
		private JobExecState(int key, String value) {
			this.key = key;
			this.value = value;
		}

		public int getKey() {
			return key;
		}

		public String getValue() {
			return value;
		}
		
		public static String getValue(int key){
			for(JobExecState inst : values()){
				if(key==inst.key)
					return inst.value;
			}
			throw new IllegalArgumentException("不支持的常量：" + key);
		}
		
		public static JobExecState valueOf(int key){
			for(JobExecState inst : values()){
				if(key==inst.key)
					return inst;
			}
			throw new IllegalArgumentException("不支持的常量：" + key);
		}
	}
	
	/**
	 * 调度任务状态
	 */
	public static enum JobState {
		
		NORMAL(1, "运行"),
		PAUSE(0, "暂停");
		
		private final int key;
		private final String value;
		
		private JobState(int key, String value) {
			this.key = key;
			this.value = value;
		}

		public int getKey() {
			return key;
		}

		public String getValue() {
			return value;
		}
		
		public static String getValue(int key){
			for(JobState inst : values()){
				if(key==inst.key)
					return inst.value;
			}
			throw new IllegalArgumentException("不支持的常量：" + key);
		}
		
		public static JobState valueOf(int key){
			for(JobState inst : values()){
				if(key==inst.key)
					return inst;
			}
			throw new IllegalArgumentException("不支持的常量：" + key);
		}
	}
}
