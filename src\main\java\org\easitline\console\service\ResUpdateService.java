package org.easitline.console.service;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.apache.commons.lang3.StringUtils;
import org.easitline.console.base.Constants;
import org.easitline.console.utils.ConsoleUtils;
import org.easitline.console.vo.ApplicationModel;
import org.easitline.console.vo.ResourceModel;

public class ResUpdateService {
    
    
    /**
     * 更新应用资源
     * @param appInfo 应用信息
     * @param appResources 资源列表
     * @throws SQLException 
     */
    public static void updateAppResource(ApplicationModel appInfo, List<ResourceModel> appResources) throws SQLException {
        String resTable = appInfo.getResTable();
        if("ycEcRes".equals(resTable)) {
            updateAppResourceForYcNodeRes(appResources);
        } else if(resTable != null && resTable.startsWith("ycBusiRes")) {
            String busiId = resTable.substring(resTable.indexOf("#")+1);
            updateAppResourceForYcBusiRes(busiId, appResources);
        } else {
            updateAppResourceForEasiRes(appResources);
        }
    }
    
    /**
     * 更新业务资源
     * @param busiId 业务ID
     * @param appResources 资源列表
     */
    private static void updateAppResourceForYcBusiRes(String busiId, List<ResourceModel> appResources) {
        String sql = "";
        try {
            String resUpdateSwitch = AppContext.getContext(Constants.EASITLINE_CONSOLE).getProperty("resUpdateSwitch", "");
            String[] array = resUpdateSwitch.split(",");
            
            EasyQuery query = ServerContext.getAdminQuery();
            
            for(ResourceModel res : appResources) {
                EasyRecord record = new EasyRecord("CC_BUSI_RES", "RES_ID", "BUSI_ID", "ENT_ID");
                record.set("BUSI_ID", busiId);
                record.set("ENT_ID", res.getEntId());
                record.set("RES_ID", res.getResId());
                record.set("RES_NAME", res.getResName());
                record.set("P_RES_ID", res.getPresId());
                record.set("RES_TYPE", Integer.valueOf(res.getResType()));
                record.set("APP_ID", res.getAppId());
                record.set("IDX_ORDER", Integer.valueOf(res.getIdxOrder()));
                record.set("RES_ICON", res.getResIcon());
                if(StringUtils.isNotBlank(res.getAuthFlag())) {
                    record.set("AUTH_FLAG", res.getAuthFlag());
                }
                if(StringUtils.isNotBlank(res.getResRemark())) {
                    record.set("RES_REMARK", res.getResRemark());
                }
                if(StringUtils.isNotBlank(res.getResCode())) {
                    record.set("RES_CODE", res.getResCode());
                }
                
                sql = "select count(*) as tcount from cc_busi_res where RES_ID = ? and BUSI_ID = ?";
                
                if(query.queryForExist(sql, new Object[]{res.getResId(), busiId})) {
                    String resUpdate = res.getResUpdate();
                    if("0".equals(resUpdate)) {
                        record.set("RES_URL", res.getResUrl());
                    }
                    
                    if(array != null && array.length > 0) {
                        record.remove(array);
                    }
                    if("9".equals(resUpdateSwitch)) {
                        ConsoleUtils.getLogger().info("resUpdateSwitch不允许修改菜单资源", null);
                        return;
                    }
                    query.update(record);
                } else {
                    record.set("RES_URL", res.getResUrl());
                    record.set("RES_STATE", Integer.valueOf(res.getResState()));
                    query.save(record);
                }
            }
        } catch (SQLException e) {
            ConsoleUtils.getLogger().error(null, e);
        }
    }
    
    /**
     * 更新节点资源
     * @param appResources 资源列表
     */
    private static void updateAppResourceForYcNodeRes(List<ResourceModel> appResources) {
        String sql = "";
        try {
            EasyQuery query = ServerContext.getAdminQuery();
            
            for(ResourceModel res : appResources) {
                sql = "select count(*) as tcount from cc_ec_res where RES_ID = ?";
                if(query.queryForExist(sql, new Object[]{res.getResId()})) {
                    sql = "update cc_ec_res set RES_NAME = ? ,RES_URL=? ,P_RES_ID = ?,RES_TYPE=?,IDX_ORDER=?,RES_ICON=?,OWNER_ENT_ID = ? where RES_ID = ? ";
                    Object[] params = new Object[]{
                            res.getResName(), res.getResUrl(),
                            res.getPresId(), Integer.valueOf(res.getResType()),
                            Integer.valueOf(res.getIdxOrder()),
                            res.getResIcon(), res.getEntId(), res.getResId()};
                    query.execute(sql, params);
                } else {
                    sql = "insert into cc_ec_res(RES_ID,RES_NAME,P_RES_ID,RES_URL,RES_TYPE,RES_STATE,IDX_ORDER,RES_ICON,OWNER_ENT_ID)"
                            + " values(?,?,?,?,?,?,?,?,?)";
                    Object[] params = new Object[]{
                            res.getResId(), res.getResName(),
                            res.getPresId(), res.getResUrl(),
                            Integer.valueOf(res.getResType()), Integer.valueOf(res.getResState()),
                            Integer.valueOf(res.getIdxOrder()), res.getResIcon(), res.getEntId()
                    };
                    query.execute(sql, params);
                }
            }
        } catch (SQLException e) {
            ConsoleUtils.getLogger().error(null, e);
        }
    }
    
    /**
     * 更新Easi资源
     * @param appResources 资源列表
     */
    private static void updateAppResourceForEasiRes(List<ResourceModel> appResources) {
        String sql = "";
        try {
            EasyQuery query = ServerContext.getAdminQuery();
            
            for(ResourceModel res : appResources) {
                EasyRecord record = new EasyRecord("EASI_RES", "RES_ID");
                record.set("RES_NAME", res.getResName());
                record.set("P_RES_ID", res.getPresId());
                record.set("RES_TYPE", Integer.valueOf(res.getResType()));
                record.set("APP_ID", res.getAppId());
                record.set("PORTAL", res.getPortal());
                record.set("IDX_ORDER", Integer.valueOf(res.getIdxOrder()));
                record.set("RES_ICON", res.getResIcon());
                record.set("RES_ID", res.getResId());
                
                sql = "select count(*) as tcount from EASI_RES where RES_ID = ?";
                if(query.queryForExist(sql, new Object[]{res.getResId()})) {
                    String resUpdate = res.getResUpdate();
                    if("0".equals(resUpdate)) {
                        record.set("RES_URL", res.getResUrl());
                    }
                    query.update(record);
                } else {
                    record.set("RES_URL", res.getResUrl());
                    record.set("RES_STATE", Integer.valueOf(res.getResState()));
                    query.save(record);
                }
            }
        } catch (SQLException e) {
            ConsoleUtils.getLogger().error(null, e);
        }
    }
}
