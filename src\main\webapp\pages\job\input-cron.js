$(function(){
	
    changeDesc("#DailyTab");
    
    //保存任务计划
    $(".layui-layer-btn .layui-layer-btn0").click(function savePlan(){		
    	var cron = generate();
    	var result = cronValidate(cron);
    	if(result != true){
    		alert(result);
    		return;
    	}
    	var dateIntervalObj = getDateInterval();
    	var timeIntervalObj = getTimeInterval();
	    	
    	var jobDesc = $("#summarySpan").html();
    	var activeTab = $(".layui-tab-title .layui-this").attr("name");
    	if("MinutesTab" != activeTab && "OneTimeTab" != activeTab && "CronTxt" != activeTab) {
    		callbackCron(cron, jobDesc, dateIntervalObj, timeIntervalObj);
    	} else {
    		callbackCron(cron, jobDesc);
    	}
    });
    
    $(".layui-tab .layui-tab-title li").click(function(){
    	var activeTab = $(this).attr("name");
    	activeTab = "#"+activeTab;
    	showCommonDiv();
    	if("#CronTxt" == activeTab){
    		hideCommonDiv();
    	}
    	if("#MinutesTab" == activeTab || "#OneTimeTab" == activeTab) {
    		$("#keepTimeTable").hide();
    		$("#everyDayTable").hide();
    	} else {
    		$("#keepTimeTable").show();
    		$("#everyDayTable").show();
    	}
    	changeDesc(activeTab);
    });
    
    $("input[name='timeTypeRadio']").click(function() {
    	changeDesc();
    });
    
    $("input[name='endDateRadio']").click(function(){
    	changeDesc();
    });
    
    $("input[name='executeTypeRadio']").click(function(){
    	changeDesc();
    });
    $("#intervalInput").change(function(){
    	if($(this).val()<=0){
    		$("#intervalInput").val(1);
    	}
    	changeDesc();
    });
    
    $("#WeeklyTab input:checkbox").click(function(){
    	changeDesc();
    });
    
    $("#MonthlyTab input:checkbox").click(function(){
    	changeDesc();
    });
    
    
	$("#beginHour").change(function(){
    	changeDesc();
    });
	
	$("#beginMinute").change(function(){
    	changeDesc();
    });
	
    $("#MinutesTab-intervalInput").change(function(){
    	changeDesc();
    });

});

var comDate = new Date();
var comDateTime = new Date().Format("yyyy-MM-dd HH:mm:ss"); 

//获取今天的日期
function getToday() {
	var today = new Date();
    var toyear = today.getFullYear(); 
    var tomonth = today.getMonth()+1;
    var todate = today.getDate();
    
    if(tomonth<10) {
        tomonth = "0" + tomonth;
     }
     
     if(todate<10) {
     	todate = "0" + todate;
     }
    
    return toyear + "-" + tomonth + "-" + todate;
}

//处理定时Tab
function doWithMinuteTab() {
	 var result = "";
	 var intervalType = $("#MinutesTab-intervalTypeSel :checked").val();
	 var intervalInput = $("#MinutesTab-intervalInput").val();//执行间隔
	 
     var radioVal = $("input[name='executeTypeRadio']:checked").val();//执行类型，now：立即执行；time：延时执行；
     
     
     if(intervalInput == "0"||intervalInput == "") {
    	 alert("间隔时间不能为空或为0");
    	 return result;
     }
     var cronHour = "";
     var cronMinute = "";
     var cronSecond = "";
     
     if(radioVal == 'time') {
    	 var lateTimeVal = $("#lateTimeVal").val();//延迟时间
    	 var lateHours = lateTimeVal.substring(0,2);//延迟小时
    	 var lateMinutes = lateTimeVal.substring(3,5);//延迟分钟
    	 var lateSeconds = lateTimeVal.substring(6,8);//延迟秒
    	 if(radioVal == 'time'&&lateTimeVal == "") {
        	 alert("请选择延迟执行时间");
        	 return result;
         }
    	 if(lateHours=="00"){
    		 lateHours = "0";
    	 }
    	 if(lateMinutes=="00"){
    		 lateMinutes = "0";
    	 }
    	 if(lateSeconds=="00"){
    		 lateSeconds = "0";
    	 }
    	 cronHour = lateHours;
    	 cronMinute = lateMinutes;
    	 cronSecond = lateSeconds;
    	 if(intervalType == "hour"){
    		 cronHour = lateHours + "/" + intervalInput;
         }else if(intervalType == "minute"){
        	 cronMinute = lateMinutes + "/" + intervalInput;
         }else if(intervalType == "second"){
        	 cronSecond = lateSeconds + "/" + intervalInput;
         }
     }else{
    	 cronHour = "*";
    	 cronMinute = "*";
    	 cronSecond = "*";
    	 if(intervalType == "hour"){
    		 cronHour = comDate.getHours()+"/"+intervalInput;
    		 cronMinute = comDate.getMinutes()+"";
    		 cronSecond = comDate.getSeconds()+"";
         }else if(intervalType == "minute"){
        	 cronMinute = comDate.getMinutes()+"/"+intervalInput;
    		 cronSecond = comDate.getSeconds()+"";
         }else if(intervalType == "second"){
        	 cronSecond = comDate.getSeconds()+"/"+intervalInput;
         }
     }
     
     var cronStr = new Array();
     cronStr.push(cronSecond, " ", cronMinute, " ", cronHour, " * * ?");
     
     result = cronStr.join("");
     return  result;
}

//处理每天Tab
function doWithDailyTab() {
	var result = "";
	result = doWithCommonPart("*", "*", "?");
	return result;
}

//处理每周的Tab
function doWithWeeklyTab() {
	  var result = "";
	  var selectedDays = "";
      $("#WeeklyTab input:checkbox:checked").each(function () { selectedDays += $(this).val() + ","; });
      if (selectedDays.length > 0)
          selectedDays = selectedDays.substr(0, selectedDays.length - 1);
      
      var dayOfMonth = "?";
      if(null == selectedDays || selectedDays.length == 0) {
    	  selectedDays = "?";
    	  dayOfMonth = "*";
      }
      
      result = doWithCommonPart(dayOfMonth, "*", selectedDays);
      
      return result;
}

//处理每月的Tab
function doWithMonthlyTab() {
	var result = "";
	var selectedDays = "";
    $("#MonthlyTab input:checkbox:checked").each(function () { selectedDays += $(this).val() + ","; });
    if (selectedDays.length > 0)
        selectedDays = selectedDays.substr(0, selectedDays.length - 1);
    
    if(null == selectedDays || selectedDays.length == 0) {
  	  selectedDays = "*";
    }
    
    result = doWithCommonPart("*", selectedDays, "?");
    
    return result;
}

//处理公共部分(每天频率)
function doWithCommonPart(dayOfMonth, month, dayOfWeek) {
	var result = "";
	var timeType = $("input[name='timeTypeRadio']:checked").val();
	var retObj = null;
	var cronAry = new Array();
	
	if("oneTime" == timeType) {  //执行一次
		retObj = getOneTimeExpression();   //获取执行时间的小时和分钟
		cronAry.push(retObj.second," ", retObj.minute, " ", retObj.hour, " ", dayOfMonth, " ", month, " ", dayOfWeek);
	} else if("moreTime" == timeType) {   //执行多次
		retObj = getMoreTimesExpression();
	
		var intervalType = retObj.intervalType;
		
		if("hour" == intervalType) {
			cronAry.push(retObj.beginTimeSecond," ", retObj.beginTimeMinute, " ",  retObj.beginTimeHour, "-", retObj.endTimeHour, "/",retObj.interval, " ", dayOfMonth, " ", month, " ", dayOfWeek);
		} else if("minute" == intervalType) {
			cronAry.push(retObj.beginTimeSecond," ", retObj.beginTimeMinute, "/", retObj.interval, " ", retObj.beginTimeHour, "-", retObj.endTimeHour, " ", dayOfMonth, " ", month, " ", dayOfWeek);
		} else if("second" == intervalType){
			cronAry.push(retObj.beginTimeSecond,"/", retObj.interval, " ", "*", " ", retObj.beginTimeHour, "-", retObj.endTimeHour, " ", dayOfMonth, " ", month, " ", dayOfWeek);
		}
	}
	
	result = cronAry.join("");
	
	return result;
}

//处理执行一次
function doWithOneTimeTab() {
	var result = "";
	var oneTimeDate = $("#oneTimeDate").val();
	var oneTimeTime = $("#oneTimeTime").val();
	
	var oneTimeDateAry = oneTimeDate.split("-");
    var oneTimeTimeAry = oneTimeTime.split(":");
	
	var cronAry = new Array();
	
	cronAry.push(parseInt(oneTimeTimeAry[2])," ", parseInt(oneTimeTimeAry[1]), " ",  parseInt(oneTimeTimeAry[0]), " ", parseInt(oneTimeDateAry[2]), " ", parseInt(oneTimeDateAry[1]), " ?");
	
	result = cronAry.join("");
	
	return result;
}

//获取执行一次的表达式
function getOneTimeExpression() {
	var oneTimeTimerVal = $("#oneTimeTimer").val();
	var oneTimeAry = oneTimeTimerVal.split(":");
	var hour = oneTimeAry[0];
	var minute = oneTimeAry[1];
	var second = oneTimeAry[2];

	var obj = new Object();
	obj.hour = parseInt(hour);
	obj.minute = parseInt(minute);
	obj.second = parseInt(second);
	
	return obj;
}

//获取执行多次的表达式
function getMoreTimesExpression () {
	var intervalVal = $("#intervalInput").val();
	var intervalType = $("#intervalTypeSel :checked").val();
	var beginTime = $("#beginTime").val();
	var endTime = $("#endTime").val();
	
	var beginTimeAry = beginTime.split(":");
	var endTimeAry = endTime.split(":"); 

	var obj = new Object();
	obj.interval = intervalVal;
	obj.intervalType = intervalType;
	obj.beginTimeHour = parseInt(beginTimeAry[0]);
	obj.beginTimeMinute = parseInt(beginTimeAry[1]);
	obj.beginTimeSecond = parseInt(beginTimeAry[2]);
	obj.endTimeHour = parseInt(endTimeAry[0]);
	obj.endTimeMinute = parseInt(endTimeAry[1]);
	obj.endTimeSecond = parseInt(endTimeAry[2]);
	
	return obj;
}

//获取开始日期和结束日期
function getDateInterval() {
	var beginDate = $("#beginDate").val();
	var endDate = $("#endDate").val();
	var obj = new Object();
	obj.beginDate = beginDate;
	obj.endDate = endDate;
	//判断是否有结束日期
	/*var endDateChoose = $("input[name='endDateRadio']:checked").val();
	if(endDateChoose == "hasEndDate") {
		obj.endDate = endDate;
	} */
	return obj;
}

//获取开始时间和结束时间
function getTimeInterval() {
	var timeType = $("input[name='timeTypeRadio']:checked").val();
	var obj = new Object();
	if("moreTime" == timeType) {
		var beginTime = $("#beginTime").val();
		var endTime = $("#endTime").val();
		
		obj.beginTime = beginTime;
		obj.endTime = endTime;
		
		return obj;
	}
}

//验证开始时间和结束时间
function checkBeginTimeAndEndTime() {
	var moreTimesBeginHour = Number($("#dailyBeginHour").val());
	var moreTimesEndHour = Number($("#dailyEndHour").val());
	
	if(moreTimesBeginHour>moreTimesEndHour) {
		alert("开始时间必须大于结束时间");
		return;
	}
	
}

//生成cron表达式
function generate() {
	var activeTab = $(".layui-tab-title .layui-this").attr("name");
    var results = "";
    switch (activeTab) {
        case "MinutesTab":
        	 results = doWithMinuteTab();
        	 break;
        case "DailyTab":
        	results = doWithDailyTab();
            break;
        case "WeeklyTab":
            results = doWithWeeklyTab();
            break;
        case "MonthlyTab":
        	results = doWithMonthlyTab();
            break;
        case "OneTimeTab":
        	results = doWithOneTimeTab();
            break;
        case "CronTxt":
        	results = $("#cronContent").val();
        	$("#summarySpan").html(results);
            break;
    }
    return results;
}

//隐藏公共Div
function hideCommonDiv() {
   $("#commonDiv").hide();
}

//显示公共Div
function showCommonDiv() {
   $("#commonDiv").show();
}

//显示调度任务的描述
function changeDesc(tab) {
	var activeTab = tab;
	
	if(null == activeTab) {
		activeTab = $(".layui-tab-title .layui-this").attr("name");
	} else {
		activeTab = activeTab.substring(1,activeTab.length); 
	}

	var evreyStr = "";
	var summaryStrAry = new Array();
	
	if("MinutesTab" == activeTab) {
		createMinuteDesc();
		return;
	} else if("DailyTab" == activeTab) {
		evreyStr = "每天";
	} else if("WeeklyTab" ==  activeTab) {
		evreyStr = "每周";
	} else if("MonthlyTab" == activeTab) {
		evreyStr = "每月";
	} else if("OneTimeTab" == activeTab) {
		createOneTimeDesc();
		return;
	}
	

	if(!checkTime()) {
		$("#endTime").val("23:59:59");
	}
	
	if(!checkDate()) {
		var today = getToday();
		$("#endDate").val(today);
	} 

	var beginTime = $("#beginTime").val();
	var endTime = $("#endTime").val();
	var interval = $("#intervalInput").val();
	var intervalType = $("#intervalTypeSel :checked").val();    //间隔类型，时，分，秒
	var beginDate = $("#beginDate").val();
	var endDate = $("#endDate").val();
	var endDateStatus = $("input[name='endDateRadio']:checked").val();   //是否需要结束日期
	var timeType = $("input[name='timeTypeRadio']:checked").val();  //每天频率
	var oneTimeStr = $("#oneTimeTimer").val();
	
	if("hour" ==  intervalType) {
		intervalType = "小时";
	} else if("minute" ==  intervalType) {
		intervalType = "分钟";
	} else if("second" ==  intervalType){
		intervalType = "秒";
	}
	
	//计算每天频率
	var everyDateTimesStr = "";
	if("oneTime" == timeType) {
		everyDateTimesStr = "在" + oneTimeStr + "执行一次。";
	} else {
		everyDateTimesStr = "在" + beginTime +  "" + "和" + endTime + "" + "之间，每" +  interval + intervalType + "执行。";
	}
	
	var endDateStr = "";
/*	if("hasEndDate" == endDateStatus) {
		endDateStr = "，" + endDate + "结束。";
	} else {
		endDateStr = "。";
	}*/
	
	if(endDate&&endDate!="") {
		endDateStr = "，" + endDate + "结束。";
	} else {
		endDateStr = "。";
	}
	
	
	//显示执行周和月
	var selectedDays = "";
	if("WeeklyTab" ==  activeTab) {
	     $("#WeeklyTab input:checkbox:checked").each(function () { selectedDays += $(this).attr("txt") + ","; });
	     if (selectedDays.length > 0) {
	    	 selectedDays = selectedDays.substr(0, selectedDays.length - 1);
	     }   
	} else if("MonthlyTab" == activeTab) {
	    $("#MonthlyTab input:checkbox:checked").each(function () { selectedDays += $(this).val() + ","; });
	    if (selectedDays.length > 0) {
	    	 selectedDays = selectedDays.substr(0, selectedDays.length - 1);
	    	 selectedDays += "号";
	    }
	} 
	summaryStrAry.push(evreyStr, selectedDays, everyDateTimesStr , "将从", beginDate, "开始使用计划", endDateStr);
	
	$("#summarySpan").html(summaryStrAry.join(""));
}

//验证开始时间是否小于结束时间
function checkTime() {
	
	var isOk=true;
	var beginTime = $("#beginTime").val();
	var endTime = $("#endTime").val();
	
	if(null == beginTime || null == endTime)return isOk;
	
	var beginTimeAry = beginTime.split(":");
	var endTimeAry = endTime.split(":");
	var beginHour = parseInt(beginTimeAry[0]);
	var beginMinute = parseInt(beginTimeAry[1]);
	var beginSecond = parseInt(beginTimeAry[2]);
	var endHour = parseInt(endTimeAry[0]);
	var endMinute = parseInt(endTimeAry[1]);
	var endSecond = parseInt(beginTimeAry[21]);
	
	if(endHour<beginHour) {//验证小时
		isOk = false;
	} else if(endHour==beginHour){
		if(endMinute<beginMinute) {//验证分
			isOk = false;
		}else if(endMinute==beginMinute){
			if(endSecond<=beginSecond) {//验证秒
				isOk = false;
			}
		}
	}
	
	return isOk;
} 

//生成定时描述
function createMinuteDesc() {
	var intervalType = $("#MinutesTab-intervalTypeSel :checked").val();
	var intervalInput = $("#MinutesTab-intervalInput").val();//执行间隔
    var executeType = $("input[name='executeTypeRadio']:checked").val();//执行类型，now：立即执行；time：延时执行；
    var descStrAry = new Array();
    var beginTimeStr = "";
    
    //间隔类型
    if("time" == executeType) {
    	var lateTimeVal = $("#lateTimeVal").val();//延迟时间
		var lateHours = lateTimeVal.substring(0,2);//延迟小时
		var lateMinutes = lateTimeVal.substring(3,5);//延迟分钟
		var lateSeconds = lateTimeVal.substring(6,8);//延迟秒
		if(lateHours=="00"){
			lateHours = "0";
		}
		if(lateMinutes=="00"){
			lateMinutes = "0";
		}
		if(lateSeconds=="00"){
			lateSeconds = "0";
		}
		beginTimeStr ="从" + lateTimeVal + "后";
    	if(intervalType == "hour"){
        	intervalInput += "小时";
        }else if(intervalType == "minute"){
        	intervalInput += "分钟";
        }else if(intervalType == "second"){
        	intervalInput += "秒钟";
        }
	} else {
		comDate = new Date();
		comDateTime = new Date().Format("yyyy-MM-dd HH:mm:ss"); 
		var dateTimeArr = comDateTime.split(" ");
		var time = dateTimeArr[1];
		beginTimeStr = "从每天的"+time;
		if(intervalType == "hour"){
        	intervalInput += "小时";
        }else if(intervalType == "minute"){
        	intervalInput += "分钟";
        }else if(intervalType == "second"){
        	intervalInput += "秒钟";
        }
	}
    descStrAry.push(beginTimeStr, "开始每隔", intervalInput, "执行一次");
    $("#summarySpan").html(descStrAry.join(""));
    return descStrAry.join("");
	
}

//生成执行一次的描述
function createOneTimeDesc() {
	
	var oneTimeDate = $("#oneTimeDate").val();
	var oneTimeTime = $("#oneTimeTime").val();
	
	var oneTimeStr = "将在" + oneTimeDate + " " + oneTimeTime + "执行一次后结束。";
	
	$("#summarySpan").html(oneTimeStr);
}

//验证开始日期是否小于结束日期
function checkDate() {
	var beginDate = $("#beginDate").val();
	var endDate = $("#endDate").val();
	var isOk = true;
	
	if(null == beginDate || null == endDate)return isOk;
	
	var endDateStatus = $("input[name='endDateRadio']:checked").val();
	var beginDateAry = beginDate.split("-");
	var endDateAry = endDate.split("-");
	var beginYear = parseInt(beginDateAry[0]);
	var beginMonth = parseInt(beginDateAry[1]);
	var beginDay = parseInt(beginDateAry[2]);
	var endYear = parseInt(endDateAry[0]);
	var endMonth = parseInt(endDateAry[1]);
	var endDay = parseInt(endDateAry[2]);
	
	if("hasEndDate" == endDateStatus) {
		
		if(endYear<beginYear) {
			isOk = false;
		} else if(endYear==beginYear) {
			if(endMonth<beginMonth) {
				isOk = false;
			} else if(endMonth==beginMonth){
				if(endDay<beginDay) {
					isOk = false;
				}
			}
		}
	}
	return isOk;
} 

/**
 * 格式化日期时间
 */
Date.prototype.Format = function (fmt) { //author: meizz 
    var o = {
        "M+": this.getMonth() + 1, //月份 
        "d+": this.getDate(), //日 
        "H+": this.getHours(), //小时 
        "m+": this.getMinutes(), //分 
        "s+": this.getSeconds(), //秒 
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度 
        "S": this.getMilliseconds() //毫秒 
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
    if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
}