<%@ page language="java" contentType="text/html; charset=UTF-8"  pageEncoding="UTF-8"%>
<div>
	<fieldset class="layui-elem-field layui-field-title">
		<legend>Mars应用监控/金蝶apusic</legend>
	</fieldset>

	<div class="layui-form" style="padding-left:15px">
		<table class="layui-table text-l">
			<thead>
				<tr>
					<th>序号</th>
					<th>应用名称</th>
					<th>部署时间</th>
					<th>应用部署目录</th>
					<th>运行状态</th>
					<th>操作</th>
				</tr>
			</thead>
			<tbody id="remote-app-data"></tbody>
			<tbody id="app-data"></tbody>
		</table>
				<script id="hisListTemp" type="text/x-jsrender">
						{{for list}}
							<tr {{if !state}}style="background-color:#e9cdcd;"{{/if}}>
								<td>{{:#index+1}}</td>
								<td>{{:contextPath}}</td>
								<td>{{:deployTime}}</td>
								<td>{{:warName}}</td>
								<td>{{:stateName}}</td>
								<td>
										{{if !state}}
											<button type="button" class="layui-btn layui-btn-sm" onclick="todo('{{:appName}}','start')">启动</button>&nbsp;&nbsp;
										{{else}}
											<button type="button" class="layui-btn layui-btn-warm layui-btn-sm" onclick="todo('{{:appName}}','stop')">停止</button>&nbsp;&nbsp;
											<button type="button" class="layui-btn layui-btn-danger layui-btn-sm" onclick="todo('{{:appName}}','reload')">重新加载</button>
										{{/if}}
								</td>
							</tr>
						{{/for}}					         
				</script>
	</div>	
</div>

<script>
 $(function(){
		getData();
  });

  function getData(){
	$.ajax({
		 dataType:'json',
		 url:"${ctxPath}/servlet/monitor/server",
		 data:{action:'apusicApp'},
		 success:function(result){
			var jsRenderTpl = $.templates("#hisListTemp");
	       	var html = jsRenderTpl({list:result.data});
			$("#app-data").html(html);
		},beforeSend:function(result){
			
		},complete:function(result){
			
		}
	});
}

function todo(appName,operator){
	$.ajax({
		 dataType:'json',
		 url:"${ctxPath}/servlet/monitor/server",
		 data:{action:'apusic',appName:appName,operator:operator},
		 success:function(result){
			 if(result.state==0){
				 layer.msg(result.msg,{icon:7,offset:'20px',time:1200});
				 return;
			 }
			 layer.msg("操作成功,稍后自动刷新查看状态！",{time:5000,icon:1},function(){
				 layer.closeAll();
				 location.reload();
			 });
		},beforeSend:function(result){
			
		},complete:function(result){
			
		},error:function(e){
			
		}
	});
}


</script>
