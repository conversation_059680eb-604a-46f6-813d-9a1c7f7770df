package org.easitline.console.dbscript;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.easitline.common.core.EasyPool;
import org.easitline.common.core.Globals;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.resource.DSResource;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.console.base.Constants;
import org.easitline.console.utils.AesUtils;
import org.easitline.console.utils.DESUtil;

import com.alibaba.fastjson.JSONObject;



/**
 * Easitline console  运行时数据库脚本管理
 * 
 *  应用启动
 *   |
 *   |
 * checkInit()检查数据库脚本是否已经初始化
 * 	 |
 * 	 |
 * initDB() 初始化数据库脚本
 *   |
 *   |
 * checkVersion() 检查数据库当前的版本号
 * 	 |
 * 	 |
 * updateDB() 更新数据库脚本，执行差异化更新 
 * 
 * <AUTHOR>
 *
 */

public class DBHelper {
	
	/**
	 * 数据库操作对象
	 */
	private static EasyQuery queryHelper;

	/**
	 * 数据库版本号，有更新的时候需要修改该版本号配合
	 */
	public static final float CONSOLE_DB_VERSIOIN = 1.0f;
	
	public static Logger getLogger() {
		return LogEngine.getLogger(Constants.EASITLINE_CONSOLE);
	}
	
	public DBHelper(){
		DBHelper.queryHelper = Constants.getDb();
	}
	
	/**
	 * 执行数据初始化操作
	 * @throws Exception
	 */
	public void run() throws Exception{
		if(this.initCheck()){  //如果库表不存在，则执行初始化脚本操作
			this.initDB();
		}
	  //if(Constants.fileTamperedCheck()&&!this.tableCheck("EASI_LOG_HASH")){
	  //	this.initDB("easitline_log_hash.sql");
	  //}
		this.checkAccount();
		/*if(this.updateCheck()){  //如果版本有更新，则执行更新脚本工作
			this.updateDB();
		}*/
	}
	
	private void checkAccount() {
		try {
			boolean bl = queryHelper.queryForExist("select count(1) from easi_user where login_acct = ?", "admin@mars");
			if(bl) {
				queryHelper.executeUpdate("delete from easi_user where login_acct = ?", "admin");
			}
			
			String sql = "select LOGIN_ACCT,LOGIN_PWD from EASI_USER";
			List<JSONObject> list = queryHelper.queryForList(sql, new Object[]{},new JSONMapperImpl());
			for(JSONObject row:list) {
				String pwd = row.getString("LOGIN_PWD");
				String loginAcct = row.getString("LOGIN_ACCT");
				if(pwd.startsWith("3DES_")){
					pwd = pwd.replace("3DES_", "");
					pwd = DESUtil.getInstance().decryptStr(pwd);
					pwd = AesUtils.encrypt(pwd, AesUtils.USER_KEY);
					pwd = "AES_"+pwd;
					sql = "update EASI_USER  set LOGIN_PWD = ? where LOGIN_ACCT = ? ";
					queryHelper.executeUpdate(sql,pwd,loginAcct);
				}else if(!pwd.startsWith("3DES_")&&!pwd.startsWith("AES_")){
					pwd = AesUtils.encrypt(pwd, AesUtils.USER_KEY);
					pwd = "AES_"+pwd;
					sql = "update EASI_USER  set LOGIN_PWD = ? where LOGIN_ACCT = ? ";
					queryHelper.executeUpdate(sql,pwd,loginAcct);
				}
			}
		} catch (SQLException e) {
			getLogger().error(e.getMessage(), e);
		}
	}
	
	public void resetAccountAesPwd(String oldKey,String newKey) {
		String sql = "select LOGIN_ACCT,LOGIN_PWD from EASI_USER";
		List<JSONObject> list;
		try {
			list = queryHelper.queryForList(sql, new Object[]{},new JSONMapperImpl());
			if(list!=null) {
				for(JSONObject row:list) {
					String pwd = row.getString("LOGIN_PWD");
					String loginAcct = row.getString("LOGIN_ACCT");
					if(pwd.startsWith("AES_")){
						pwd = pwd.replace("AES_", "");
						String pwdStr = AesUtils.decrypt(pwd, oldKey);
						String newPwdStr = AesUtils.encrypt(pwdStr,newKey);
						pwd = "AES_"+newPwdStr;
						sql = "update EASI_USER  set LOGIN_PWD = ? where LOGIN_ACCT = ? ";
						queryHelper.executeUpdate(sql,pwd,loginAcct);
					}
				}
			}
		} catch (SQLException e) {
			getLogger().error(e.getMessage(), e);
		}
	}
	
	/**
	 * 检查数据库脚本是否已经初始化,通过判断一些核心的表是否存在
	 * @return
	 */
	public boolean initCheck(){
		if(!this.tableCheck("EASI_VERSION")) 	return true;  //检查版本表
		if(!this.tableCheck("EASI_USER")) 		return true;  //检查用户表
		if(!this.tableCheck("EASI_APP_INFO"))	return true;  //检查应用配置表
		if(!this.tableCheck("EASI_APP_INFO_DEPLOY_HIS"))	return true;  //检查应用升级历史表
		if(!this.tableCheck("EASI_DS_INFO")) 	return true;  //检查数据源配置表
		if(!this.tableCheck("EASI_CONF")) 	return true;  //检查数据源配置表
		if(!this.tableCheck("EASI_LOGIN_LOG")) 	return true;  //检查登录日志表
		if(!this.tableCheck("EASI_OPERATE_LOG")) 	return true;  //检查操作日志表
		if(!this.tableCheck("EASI_APP_LOG")) 	return true;  //检查应用日志表
		if(!this.tableCheck("EASI_NODE")) 	return true;  //检查应用日志表
		return false;
	}
	
	/**
	 * 检查表是否存在
	 * @param tableName
	 * @return
	 */
	private boolean tableCheck(String tableName){
		try {
			String sql = "select count(*)  as tcount from "+tableName ;
			queryHelper.queryForInt(sql);
		} catch (Exception ex) {
			getLogger().error("数据库表不存在",ex);
			return false;
		}
		return true;
	}
	
	
	/**
	 * 检查当前数据库版本，如果当前数据库版本的版本号比CONSOLE_DB_VERSIOIN要低的时候，执行升级脚本。
	 * @return
	 */
	private boolean updateCheck(){
		String sql = "select VERSION from EASI_VERSION";
		String tmpVerId = "";
		try {
			tmpVerId = queryHelper.queryForString(sql);
			if(tmpVerId == null){
				 sql  = "insert into EASI_VERSION(VERSION) values("+DBHelper.CONSOLE_DB_VERSIOIN+")";
				 queryHelper.execute(sql);
				 return false;
			}
		} catch (Exception e) {  //如果初始化EASI_VERSION失败，则不作任何事情，正常情况下不会有异常出现。
			return false;
		}
		
		float  verId = Float.parseFloat(tmpVerId);  
		if(CONSOLE_DB_VERSIOIN > verId) return true;  //把当前程序版本的版本号和数据库的版本号进行比较，如果大于书库的版本号，则执行更新操作
		return false;
	}
	
	/**
	 * 更新数据库脚本
	 */
	private void  updateDB(){
		
	}
	
	private void initDB() throws Exception{
		initDB("easitline_derby.sql");
	}
	
	private void initDB(String name) throws Exception{
		String[] sqls = {};
		try {
			sqls =  this.getTableInitScriptResource(name);
		} catch (Exception ex) {
			getLogger().error(null,ex);
			throw new Exception("获取Easitline console初始化脚本失败，请检查脚本文件["+name+"]是否存在!",ex);
		}
		
		//执行数据库初始化操作
		for(String sql:sqls){
			try {
				System.out.println("执行初始化SQL："+sql);
				queryHelper.execute(sql);
				
			} catch (Exception ex) {
				System.out.println("ERROR SQL:"+sql);
				System.out.println(ex);
			}
		}
//		String sql  = "";
//		try {
//			//初始化数据库版本号
//			 sql = "insert into EASI_VERSION(VERSION) values('"+DBHelper.CONSOLE_DB_VERSIOIN+"')";
//			queryHelper.execute(sql);
//		} catch (Exception ex) {
//			System.out.println("ERROR SQL:"+sql);
//			System.out.println(ex);
//		}
		
	}
	
	/**
	 * 获得初始化执行的SQL脚本，通过文件读取并解析
	 * @return
	 * @throws Exception
	 */
	private String[] getTableInitScriptResource(String name) throws Exception {
		String scriptFile ="/org/easitline/console/dbscript/"+name;
		byte[] script = this.readResourceContent(scriptFile);
		String sqlStr = new String(script);
		return sqlStr.split(";;");
	}
	
	/**
	 * 读取资源内容
	 * @param resourceName
	 * @return
	 * @throws Exception
	 */
	private byte[] readResourceContent(String resourceName) throws Exception{
	    InputStream in =  this.getClass().getResourceAsStream(resourceName);
	    java.io.ByteArrayOutputStream bos = new ByteArrayOutputStream();
	    int size = 0;
	    byte[] content = new byte[10240];
	    while(true){
	       size= in.read(content);
	       if(size ==  -1) break;
	       bos.write(content,0,size);
	    }
	    in.close();
	    return bos.toByteArray();
	  }
	
	/**
	 * 获取queryHelper
	 * @return
	 */
	public static EasyQuery getQueryHelper() {
		return queryHelper;
	}
	
	/**
	 * 获取queryHelper
	 * @return
	 */
	public static EasyQuery getQueryHelper(String driverName,String url,String userName,String password) {
		try {
			Class.forName(driverName);
		} catch (ClassNotFoundException e) {
			getLogger().error(e.getMessage(),e);
		}
		return EasyQuery.getQuery(driverName, url, userName, password);
	}
	
	public static void initPool() {
		if(Constants.consoleDsPool) {
			try {
				EasyPool.getInstance().remove(Constants.EASITLINE_CONSOLE);
				String dbpath = Globals.DB_DIR+File.separator+"mars.db";
				String url    = "jdbc:sqlite:" + dbpath;
				DSResource ds = new DSResource();
				ds.setDatasourceName(Constants.EASITLINE_CONSOLE);
				ds.setDriverName("org.sqlite.JDBC");
				Map<String,String> properties = new HashMap<String,String>();
				properties.put("filters", "stat");
				ds.setProperties(properties);
				ds.setMinIdle(1);
				ds.setDbType("sqlite");
				ds.setMaxActive(10);
				ds.setUrl(url);
				
				Method deployMethod = EasyPool.class.getDeclaredMethod("deploy", DSResource.class);
                deployMethod.setAccessible(true);
                deployMethod.invoke(EasyPool.getInstance(), ds);
				Constants.consoleDsPool = true;
				getLogger().info("console数据源加载到连接池成功");
			} catch (Exception e) {
				Constants.consoleDsPool = false;
				getLogger().error(e.getMessage(),e);
			}
		}
	}
	
	public static void main(String[] args) throws Exception{
		DBHelper  dbhelper = new DBHelper();
		dbhelper.run();
	}
}
