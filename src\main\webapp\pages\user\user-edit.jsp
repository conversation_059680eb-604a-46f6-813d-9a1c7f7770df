<%@page pageEncoding="UTF-8"%>
<div>
	<fieldset class="layui-elem-field layui-field-title">
		<legend>console用户</legend>
	</fieldset>
	<form class="layui-form" method="post" id="easyform"  autocomplete="off">
		  <input type="hidden" name="userId" value="${param.userId}"/>
		  <div class="layui-form-item">
		    <label class="layui-form-label">账号</label>
		    <div class="layui-input-block">
		      <input type="text" lay-verify="required" name="userAcct" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">角色</label>
		    <div class="layui-input-block">
		      <select lay-verify="required" name="roleId" class="layui-input">
		      	<option value="1">管理员</option>
		      	<option value="2">只读账户</option>
		      	<option value="9">日志查看</option>
		      </select>
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">状态</label>
		    <div class="layui-input-block">
		      <select lay-verify="required" name="userState" class="layui-input">
		      	<option value="0">正常</option>
		      	<option value="1">暂停</option>
		      </select>
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">手机号码</label>
		    <div class="layui-input-block">
		      <input type="text" name="mobile" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <div class="layui-input-block">
		      <button type="button" lay-submit lay-filter="submitBtn" class="layui-btn layui-btn-sm">保存</button>
		    </div>
		  </div>
	</form>
</div>

<script>
   	
	
    var pkId = "${param.userId}";
    var recordObj = {};
    
    $(function(){
    	if(pkId){
    		ajax.remoteCall("${ctxPath}/servlet/user?action=getUser",{userId:pkId}, function(result) { 
    			var data = result.data;
    			$("[name='userAcct']").val(data.LOGIN_ACCT);
    			$("[name='roleId']").val(data.ROLE_ID);
    			$("[name='userState']").val(data.STATE);
    			$("[name='mobile']").val(data.MOBILE);
    			initRender();
    		  }
    	    );
    	}else{
    		initRender();
    	}
	   
    	function initRender(){
    		layui.use('form', function(){
   	         var layuiform = layui.form;
   	         layuiform.render();
   			 layuiform.on('submit(submitBtn)', function(data){
   				 saveData(data.field);
   			 });
   	     });
    	}
    	
	});
  
	//保存
	function saveData(data) {
		var op = pkId==''?'add':'update';
		ajax.remoteCall("${ctxPath}/servlet/user?action="+op+"User",data,function(result) { 	
			if(result.state==1){
				var msg = result.msg;
				if(op=='add'){
					msg = msg+',初始化密码：'+result.data+' <br>请保存好，只显示这一次。';
				}
				layer.alert(msg,{icon: 1},function(){
					location.reload();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
	
</script>