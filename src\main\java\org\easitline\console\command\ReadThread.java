package org.easitline.console.command;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;

import org.easitline.common.core.log.LogEngine;
import org.easitline.console.base.Constants;

public class ReadThread extends Thread {
    private String name;
    private InputStream inputStream;

    /**
     * @param name
     * @param inputStream
     */
    public ReadThread(String name, InputStream inputStream) {
        this.name = name;
        this.inputStream = inputStream;
    }

    /**
     * @param name
     * @param inputStream
     * @param daemon
     */
    public static void execute(String name, InputStream inputStream, boolean daemon) {
        ReadThread thread = new ReadThread(name, inputStream);
        thread.setDaemon(daemon);
        thread.start();
    }

    @Override
    public void run() {
        read(this.inputStream, this.name, "utf-8");
    }

    /**
     * @param inputStream
     * @param encoding
     */
    protected static void read(InputStream inputStream, String prefix, String encoding) {
        String line = null;
        BufferedReader buffer = null;
        try {
            buffer = new BufferedReader(new InputStreamReader(inputStream, encoding));

            while((line = buffer.readLine()) != null) {
            	LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info(prefix+":"+line,null);
            }
        }
        catch(Exception e) {
        	LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(e.getMessage(),e);
        }
        finally {
            IO.close(buffer);
            IO.close(inputStream);
        }
        LogEngine.getLogger(Constants.EASITLINE_CONSOLE).info(prefix+":"+line+" end.",null);
    }
}
