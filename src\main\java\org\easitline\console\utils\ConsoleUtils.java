package org.easitline.console.utils;

import java.io.File;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.Globals;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.console.base.Constants;
import org.easitline.console.service.ConfigCryptorService;

import com.alibaba.fastjson.JSONObject;

public class ConsoleUtils {
	
	public static Logger getLogger() {
		return LogEngine.getLogger(Constants.EASITLINE_CONSOLE,Constants.EASITLINE_CONSOLE);
	}
	
	
	public static void addOperateLog(HttpServletRequest request,String msg,String params) {
		EasyRecord record = new EasyRecord("EASI_OPERATE_LOG","LOG_ID");
		record.setPrimaryValues(RandomKit.uuid());
		if(request!=null) {
			record.set("username", request.getSession().getAttribute("MARS_CONSOLE_USER"));
			record.set("url", request.getRequestURL().toString());
			record.set("ip", WebKit.getIP(request));
		}else {
			record.set("username","自动部署");
		}
		record.set("operate_time", EasyDate.getCurrentDateString());
		record.set("msg", msg);
		if(params!=null) {
			record.set("params", params);
		}
		try {
			Constants.getDb().save(record);
		} catch (SQLException e) {
			getLogger().error(e.getMessage(), e);
		}
		
	}
	
	public static String getProfileActive() {
		String profileActive  = ServerContext.getProperties("PROFILE_ACTIVE", "");
		return profileActive;
	}
	
	public static String getServerType() {
		 String _containerType = "tomcat";
		 String tongweb = System.getProperty("tongweb.base");
		 if(StringUtils.isNotBlank(tongweb)){
			 _containerType = "tongweb";
		}
		String apusic = System.getProperty("com.apusic.aas.instanceRoot");
		if(StringUtils.isNotBlank(apusic)){
			_containerType = "apusic";
		}
		String bes = System.getProperty("bes.base");
		if(StringUtils.isNotBlank(bes)){
			_containerType = "bes";
		}
		return _containerType;
	}
	
	public static boolean isTomcat() {
		return "tomcat".equals(getServerType());
	}
	
	 public static void deleteDirectory(File directory) {
        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    deleteDirectory(file);
                } else {
                    file.delete();
                }
            }
        }
        directory.delete();
    }
	 
	 public static String getConsoleInfoPath() {
		File confFile = null;
		try {
    		String basePath = Globals.CONF_DIR;
    		confFile = new File(basePath+File.separator+"console.txt");
    		if(!confFile.exists()) {
    			confFile.createNewFile();
    		}else {
    			confFile.delete();
    		}
			StringBuffer sb = new StringBuffer();
			sb.append("生成时间："+EasyDate.getCurrentDateString()+"\n\n");
			
			sb.append("全局参数配置：\n");
			Map<String,String> config = ServerContext.getConfig();
			for(String key:config.keySet()) {
				sb.append("key>"+key+">value>"+config.get(key)+"\n");
			}
			sb.append("\n\n");
			
			List<JSONObject> userList = Constants.getDb().queryForList("select LOGIN_PWD,LOGIN_ACCT from EASI_USER", new Object[] {}, new JSONMapperImpl());
			for(JSONObject row:userList) {
				String dbpwd = row.getString("LOGIN_PWD");
				if(dbpwd.startsWith("AES")){
					dbpwd = dbpwd.replace("AES_", "");
					dbpwd = AesUtils.decrypt(dbpwd, AesUtils.USER_KEY);
				}
				sb.append("console登录账号："+row.getString("LOGIN_ACCT")+",密码："+dbpwd+"\n");
			}
			sb.append("\n\n");
			
			List<JSONObject> dsList = Constants.getDb().queryForList("select * from EASI_DS_INFO", new Object[] {}, new JSONMapperImpl());
			for(JSONObject row:dsList) {
				String dbpwd = row.getString("PASSWORD");
				if(dbpwd.startsWith("3DES")){
					dbpwd = dbpwd.replace("3DES_", "");
					dbpwd = DESUtil.getDsInstance().decryptStr(dbpwd);
				}
				if(dbpwd.startsWith(ConfigCryptorService.prefixFlag)){
					dbpwd = ConfigCryptorService.decryptString(dbpwd,"DS");
				}
				sb.append("数据源："+row.getString("SYS_DS_NAME")+"，IP地址："+row.getString("IP_ADDR")+",端口："+row.getString("IP_PORT")+"，用户名："+row.getString("USERNAME")+"，密码"+dbpwd+"\n");
			}
			FileUtil.writeTextToFile(sb.toString(), confFile.getAbsolutePath());
    	} catch (Exception e) {
    		LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(e.getMessage(),e);
    	}
		return confFile.getAbsolutePath();
	}
	 
	
}
