package org.easitline.console.utils;

import java.net.URLDecoder;

import javax.servlet.http.HttpServletRequest;

public class SafeRequestWrapper {
	
    public static String getParameter(HttpServletRequest request, String name) {
        String val = request.getParameter(name);
        if (val == null) {
            val = getParameterFromQuery(request, name);
        }
        return val;
    }

    private static String getParameterFromQuery(HttpServletRequest request, String name) {
        String query = request.getQueryString();
        if (query == null || name == null) return null;

        try {
            for (String pair : query.split("&")) {
                int idx = pair.indexOf('=');
                if (idx > 0) {
                    String key = URLDecoder.decode(pair.substring(0, idx), "UTF-8");
                    if (name.equals(key)) {
                        return URLDecoder.decode(pair.substring(idx + 1), "UTF-8");
                    }
                } else if (pair.equals(name)) {
                    return "";
                }
            }
        } catch (Exception ignored) {
        	ConsoleUtils.getLogger().error(ignored.getMessage(),ignored);
        }
        return null;
    }
}
