package org.easitline.console.utils;
import java.net.NetworkInterface;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Enumeration;

import org.easitline.common.core.log.LogEngine;
import org.easitline.console.base.Constants;

public class MachineCodeGenerator {

    public static void main(String[] args) {
        try {
            String machineCode = generateMachineCode();
            System.out.println("Machine Code: " + machineCode);
        } catch (Exception e) {
        	e.printStackTrace();
        }
    }

    public static String generateMachineCode(){
        // 获取MAC地址
        String macAddress = getMacAddress();
        // 获取操作系统名称
        String osName = System.getProperty("os.name");

        // 将MAC地址和操作系统名称组合生成机器码
        String combinedInfo = macAddress + osName;

        // 生成机器码的哈希值
        return sha256(combinedInfo).toUpperCase();
    }

    private static String getMacAddress(){
        try {
			Enumeration<NetworkInterface> networks = NetworkInterface.getNetworkInterfaces();
			while (networks.hasMoreElements()) {
			    NetworkInterface network = networks.nextElement();
			    byte[] mac = network.getHardwareAddress();
			    if (mac != null && mac.length > 0) {
			        StringBuilder sb = new StringBuilder();
			        for (int i = 0; i < mac.length; i++) {
			            sb.append(String.format("%02X%s", mac[i], (i < mac.length - 1) ? "-" : ""));
			        }
			        return sb.toString();
			    }
			}
		} catch (Exception e) {
			LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(e.getMessage(),e);
		}
        return "0";
    }


    private static String sha256(String base){
        StringBuilder hexString = new StringBuilder("");
		try {
			MessageDigest digest = MessageDigest.getInstance("SHA-256");
			byte[] hash = digest.digest(base.getBytes(StandardCharsets.UTF_8));
			hexString = new StringBuilder();

			for (byte b : hash) {
			    String hex = Integer.toHexString(0xff & b);
			    if (hex.length() == 1) hexString.append('0');
			    hexString.append(hex);
			}
		} catch (NoSuchAlgorithmException e) {
			LogEngine.getLogger(Constants.EASITLINE_CONSOLE).error(e.getMessage(),e);
		}
        return hexString.toString();
    }
}
