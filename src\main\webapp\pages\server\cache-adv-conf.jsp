<%@page pageEncoding="UTF-8"%>
<style type="text/css">
	.layui-form-label{width: 250px;}
	.layui-input-block{margin-left: 280px;}
	.layui-form-item .layui-input-inline{width: 400px;}
	.uptime_in_seconds,.total_net_input_bytes,.redis_version,.used_memory_human,.role,.used_memory_peak_human,.used_memory_rss_human,.connected_clients,.total_commands_processed,.instantaneous_ops_per_sec,.connected_slaves,.total_system_memory_human,.mem_fragmentation_ratio,.aof_enabled,.rdb_last_bgsave_status,.aof_last_bgrewrite_status,.total_connections_received,.used_memory,.used_cpu_sys,.used_cpu_user{
		font-weight: 700;
		background-color: #ebf0db;
	}
</style>
<div>
	<blockquote class="layui-elem-quote">
		<a style="cursor: pointer;" onclick="cacheMgr()">缓存查询</a>&nbsp;&nbsp;
		<a style="cursor: pointer;margin-left: 30px;" onclick="cacheTest()">测试连接</a>
		<a style="margin-left: 30px;" href="javascript:reloadCache();">重载连接</a>
		<a style="margin-left: 30px;" href="javascript:readRedisInfo();">Redis监控</a>
	</blockquote>
	<fieldset class="layui-elem-field layui-field-title">
		<legend>缓存服务基本配置</legend>
	</fieldset>
		<form class="layui-form" method="post" id="easyform2"  autocomplete="off">
			  <div class="layui-form-item">
			    <label class="layui-form-label">请选择缓存类型/CACHE_TYPE</label>
			     <div class="layui-input-inline">
			      <select name="CACHE_TYPE" class="layui-select">
			        <option value="">请选择</option>
			        <option value="memcache"  selected="selected">memcache</option>
			        <option value="redis">redis</option>
			      </select>
			    </div>
			    <div class="layui-form-mid layui-word-aux"></div>
			  </div>
			  <div class="layui-form-item">
				    <label class="layui-form-label">缓存服务器地址/G_MEMCACHE_ADDR</label>
				    <div class="layui-input-inline">
				      <textarea name="G_MEMCACHE_ADDR" class="layui-textarea" value="" style="display: inline-block;"></textarea>
				    </div>
					  <div class="layui-form-mid layui-word-aux" style="margin-left: 280px;">
						    memcache格式如：127.0.0.1:11211 多个地址之间采用逗号分隔,redis格式:127.0.0.1:6379 集群多个逗号隔开&nbsp;
					  </div>
			   </div>
			   <div class="layui-form-item">
			    <div class="layui-input-block">
			      <button type="button" lay-submit lay-filter="submitBtn2" class="layui-btn layui-btn-sm"> 保 存 </button>&nbsp;&nbsp;
			    </div>
			  </div>
		  </form>
		  
		 <form class="layui-form" method="post" id="easyform"  autocomplete="off"> 
		  <fieldset class="layui-elem-field layui-field-title">
			<legend>redis缓存参数管理</legend>
		  </fieldset>
		  <div class="layui-form-item">
		    <label class="layui-form-label">连接模式/connType</label>
		    <div class="layui-input-block">
			      <input type="radio" name="connType" value="single" title="单点" checked="checked">
			      <input type="radio" name="connType" value="cluster" title="集群">
			      <input type="radio" name="connType" value="sentinel" title="哨兵">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">数据库/database</label>
		    <div class="layui-input-block">
		      <input type="number" value="0" placeholder="默认无需填写,0-15" name="database" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">账号/user(redis6.x支持)</label>
		    <div class="layui-input-block">
		      <input type="text" placeholder="如无账号无需填写" name="user" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">密码/auth</label>
		    <div class="layui-input-block">
		      <input type="password" placeholder="如无密码无需填写，密码存在不会显示，每次修改需带上密码" name="auth" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">哨兵主名称/masterName</label>
		    <div class="layui-input-block">
		      <input type="text" value="" placeholder="哨兵模式才需要填写" name="masterName" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">最大连接数/maxTotal</label>
		    <div class="layui-input-block">
		      <input type="number" lay-verify="required" value="500" placeholder="最大连接数" name="maxTotal" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">最大空闲连接数/maxIdle</label>
		    <div class="layui-input-block">
		      <input type="number" lay-verify="required" value="30" placeholder="最大空闲连接数" name="maxIdle" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">初始化连接数/minIdle</label>
		    <div class="layui-input-block">
		      <input type="number" lay-verify="required" value="10" placeholder="初始化连接数" name="minIdle" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">多长空闲时间之后回收空闲连接/minEvictableIdleTimeMillis</label>
		    <div class="layui-input-block">
		      <input type="number" lay-verify="required" value="60000" placeholder="多长空闲时间之后回收空闲连接(毫秒)" name="minEvictableIdleTimeMillis" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">连接最大等待毫秒数/maxWait</label>
		    <div class="layui-input-block">
		      <input type="number" lay-verify="required" value="5000" placeholder="连接时最大等待毫秒数" name="maxWait" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
			     <label class="layui-form-label">redis健康检测</label>
			     <div class="layui-input-inline">
			      <select name="redisHealthChecker" class="layui-select">
			        <option value="0" selected="selected">开启</option>
			        <option value="1">关闭</option>
			      </select>
			    </div>
		  </div>
		  <div class="layui-form-item">
			     <label class="layui-form-label">启用SSL/enableSSL</label>
			     <div class="layui-input-inline">
			      <select name="enableSSL" class="layui-select">
			        <option value="0" selected="selected">禁用</option>
			        <option value="1">启用</option>
			      </select>
			    </div>
		  </div>
		  <div class="layui-form-item">
		    <div class="layui-input-block">
		      <button type="button" lay-submit lay-filter="submitBtn" class="layui-btn layui-btn-sm">保存</button>&nbsp;&nbsp;
		    </div>
		  </div>
	</form>
</div>

<script>
	$(function(){
		layui.use('form', function(){
			getConf();
			getConf2();
	         var layuiform = layui.form;
	         layuiform.render('select');
	         layuiform.render();
			 layuiform.on('submit(submitBtn)', function(data){
				 doExcute(data.field);
			 });
			 layuiform.on('submit(submitBtn2)', function(data){
				 doExcute2(data.field);
			 });
	     });
		
	});
	function cacheTest(){
		ajax.remoteCall("${ctxPath}/servlet/cacheMgr?action=cacheTest",{type:''}, function(result) {
			if(result.state==1){
				layer.msg('测试成功');
			}else{
				layer.alert(result.msg||'连接失败');
			}
		});
	}
	
	function readRedisInfo(){
		ajax.remoteCall("${ctxPath}/servlet/cacheMgr?action=redisInfo",{}, function(result) {
			if(result.state==1){
				var json = result.data;
				var html = [];
				html.push('<table class="layui-table">');
				for(var key in json){
					html.push('<tr class="'+key+'">');
					html.push('<td>'+mapEnglishToChinese(key)+'</td>')
					html.push('<td>'+key+'</td>')
					html.push('<td>'+json[key]+'</td>')
					html.push('</tr>');
				}
				html.push('</table>');
				layer.alert(html.join(''),{title:'Redis监控信息',area:['80%','80%']});
			}else{
				
			}
		});
	}
	
	function mapEnglishToChinese(englishKey) {
	    const keyMap = {
   		  "uptime_in_seconds": "运行时间（秒）",
   		  "maxmemory_human": "最大内存（人类可读）",
   		  "aof_last_cow_size": "AOF最后COW大小",
   		  "master_replid2": "主复制ID2",
   		  "mem_replication_backlog": "内存复制回溯",
   		  "aof_rewrite_scheduled": "AOF重写计划",
   		  "total_net_input_bytes": "总网络输入字节数",
   		  "rss_overhead_ratio": "RSS开销比率",
   		  "hz": "每秒时钟中断次数",
   		  "redis_build_id": "Redis构建ID",
   		  "aof_last_bgrewrite_status": "AOF最后BG重写状态",
   		  "multiplexing_api": "多路复用API",
   		  "client_recent_max_output_buffer": "客户端最近最大输出缓冲区",
   		  "allocator_resident": "分配器驻留内存",
   		  "mem_fragmentation_bytes": "内存碎片化字节数",
   		  "aof_current_size": "AOF当前大小",
   		  "repl_backlog_first_byte_offset": "复制回溯第一字节偏移量",
   		  "redis_mode": "Redis模式",
   		  "redis_git_dirty": "Redis Git修改状态",
   		  "aof_delayed_fsync": "AOF延迟FSYNC",
   		  "allocator_rss_bytes": "分配器RSS字节数",
   		  "repl_backlog_histlen": "复制回溯历史长度",
   		  "rss_overhead_bytes": "RSS开销字节数",
   		  "total_system_memory": "总系统内存",
   		  "loading": "加载状态",
   		  "evicted_keys": "驱逐的键数量",
   		  "cluster_enabled": "集群启用状态",
   		  "redis_version": "Redis版本",
   		  "repl_backlog_active": "复制回溯活动状态",
   		  "mem_aof_buffer": "AOF缓冲区内存",
   		  "allocator_frag_bytes": "分配器碎片字节数",
   		  "instantaneous_ops_per_sec": "每秒瞬时操作数",
   		  "used_memory_human": "已使用内存（人类可读）",
   		  "role": "角色",
   		  "maxmemory": "最大内存",
   		  "used_memory_lua": "已使用内存（Lua脚本）",
   		  "rdb_current_bgsave_time_sec": "当前RDB后台保存时间（秒）",
   		  "used_memory_startup": "启动时使用的内存",
   		  "lazyfree_pending_objects": "懒惰释放挂起对象",
   		  "aof_pending_bio_fsync": "AOF挂起的BIO FSYNC",
   		  "used_memory_dataset_perc": "数据集使用内存百分比",
   		  "allocator_frag_ratio": "分配器碎片率",
   		  "arch_bits": "架构位数",
   		  "mem_clients_normal": "普通客户端内存",
   		  "expired_time_cap_reached_count": "已达到过期时间限制的计数",
   		  "mem_fragmentation_ratio": "内存碎片率",
   		  "aof_last_rewrite_time_sec": "AOF最后重写时间（秒）",
   		  "master_replid": "主复制ID",
   		  "aof_rewrite_in_progress": "AOF重写进行中",
   		  "config_file": "配置文件",
   		  "lru_clock": "LRU时钟",
   		  "maxmemory_policy": "最大内存策略",
   		  "run_id": "运行ID",
   		  "latest_fork_usec": "最新fork时间（微秒）",
   		  "total_commands_processed": "总处理命令数",
   		  "expired_keys": "过期键数量",
   		  "used_memory": "已使用内存",
   		  "aof_buffer_length": "AOF缓冲区长度",
   		  "mem_clients_slaves": "从客户端内存",
   		  "keyspace_misses": "键空间未命中次数",
   		  "executable": "可执行文件",
   		  "db0": "数据库0信息",
   		  "db3": "数据库3信息",
   		  "used_memory_peak_human": "已使用峰值内存（人类可读）",
   		  "keyspace_hits": "键空间命中次数",
   		  "rdb_last_cow_size": "RDB最后COW大小",
   		  "aof_pending_rewrite": "AOF挂起重写",
   		  "used_memory_overhead": "已使用内存开销",
   		  "active_defrag_hits": "主动碎片整理命中",
   		  "tcp_port": "TCP端口",
   		  "uptime_in_days": "运行时间（天）",
   		  "used_memory_peak_perc": "内存峰值百分比",
   		  "blocked_clients": "阻塞客户端数量",
   		  "sync_partial_err": "部分同步错误次数",
   		  "used_memory_scripts_human": "已使用内存（脚本，人类可读）",
   		  "aof_current_rewrite_time_sec": "当前AOF重写时间（秒）",
   		  "aof_enabled": "AOF启用状态",
   		  "master_repl_offset": "主复制偏移量",
   		  "used_memory_dataset": "已使用内存（数据集）",
   		  "used_cpu_user": "用户态CPU使用时间",
   		  "rdb_last_bgsave_status": "RDB最后BG保存状态",
   		  "atomicvar_api": "原子变量API",
   		  "allocator_rss_ratio": "分配器RSS比率",
   		  "client_recent_max_input_buffer": "客户端最近最大输入缓冲区",
   		  "aof_last_write_status": "AOF最后写入状态",
   		  "mem_allocator": "内存分配器",
   		  "used_memory_scripts": "已使用内存（脚本）",
   		  "used_memory_peak": "已使用峰值内存",
   		  "process_id": "进程ID",
   		  "used_cpu_sys": "系统态CPU使用时间",
   		  "repl_backlog_size": "复制回溯大小",
   		  "connected_slaves": "连接的从服务器数量",
   		  "gcc_version": "GCC版本",
   		  "total_system_memory_human": "总系统内存（人类可读）",
   		  "sync_full": "完全同步次数",
   		  "connected_clients": "连接的客户端数量",
   		  "allocator_active": "分配器活动内存",
   		  "total_net_output_bytes": "总网络输出字节数",
   		  "pubsub_channels": "发布订阅频道数量",
   		  "active_defrag_key_hits": "主动碎片整理键命中",
   		  "rdb_changes_since_last_save": "自上次保存后的RDB更改数",
   		  "instantaneous_input_kbps": "瞬时输入速度（kbps）",
   		  "configured_hz": "配置的HZ",
   		  "used_memory_rss_human": "已使用RSS内存（人类可读）",
   		  "expired_stale_perc": "过期但未被删除的键百分比",
   		  "active_defrag_misses": "主动碎片整理未命中",
   		  "used_cpu_sys_children": "子进程系统态CPU使用时间",
   		  "number_of_cached_scripts": "缓存脚本数量",
   		  "sync_partial_ok": "部分同步成功次数",
   		  "used_memory_lua_human": "已使用内存（Lua脚本，人类可读）",
   		  "rdb_last_save_time": "RDB最后保存时间",
   		  "pubsub_patterns": "发布订阅模式数量",
   		  "slave_expires_tracked_keys": "从服务器跟踪的过期键数量",
   		  "redis_git_sha1": "Redis Git SHA1",
   		  "used_memory_rss": "已使用RSS内存",
   		  "rdb_last_bgsave_time_sec": "RDB最后BG保存时间（秒）",
   		  "os": "操作系统",
   		  "mem_not_counted_for_evict": "未计算用于驱逐的内存",
   		  "active_defrag_running": "主动碎片整理运行中",
   		  "rejected_connections": "拒绝的连接数",
   		  "aof_rewrite_buffer_length": "AOF重写缓冲区长度",
   		  "active_defrag_key_misses": "主动碎片整理键未命中",
   		  "allocator_allocated": "分配器已分配内存",
   		  "aof_base_size": "AOF基准大小",
   		  "instantaneous_output_kbps": "瞬时输出速度（kbps）",
   		  "second_repl_offset": "第二复制偏移量",
   		  "rdb_bgsave_in_progress": "RDB后台保存进行中",
   		  "used_cpu_user_children": "子进程用户态CPU使用时间",
   		  "total_connections_received": "总接收连接数",
   		  "migrate_cached_sockets": "迁移缓存的套接字"
	    };
	    return keyMap[englishKey] || englishKey;
	}

	
	function reloadCache(){
		ajax.remoteCall("${ctxPath}/servlet/cacheMgr?action=reloadMemcache",{}, function(result) { 
 			layer.msg('ok');
		});
	}
	function getConf(){
		ajax.remoteCall("${ctxPath}/servlet/cacheMgr?action=getConf",{}, function(result) { 
			var encryptStr = result.data;
			var dataStr = aesDecrypt(encryptStr);
			if(dataStr){
				var json = eval('(' + dataStr + ')');
				fillRecord(json);
				layui.use('form', function(){
			         var layuiform = layui.form;
			         layuiform.render('select');
			         layuiform.render('radio');
			         layuiform.render();
			     });
			}
		});
	}
	function getConf2(){
		ajax.remoteCall("${ctxPath}/servlet/cacheMgr?action=getConf2",{}, function(result) { 
			var encryptStr = result.data;
			var data = JSON.parse(aesDecrypt(encryptStr));
			if(data){
				fillRecord(data);
				layui.use('form', function(){
			         var layuiform = layui.form;
			         layuiform.render('select');
			         layuiform.render();
			     });
			}
		});
	}
	
	 
	function doExcute2() {
		var data = JSON.stringify(form.getJSONObject("easyform2")); 
		var dataStr = aesEncrypt(data);
		ajax.remoteCall("${ctxPath}/servlet/cacheMgr?action=saveConf2",{encryptStr:dataStr},function(result) { 	
			if(result.state==1){
				layer.msg(result.msg+"请重启服务器配置生效。",{icon: 1,time:800},function(){
					layer.closeAll();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
	function doExcute() {
		var _data  = form.getJSONObject("easyform");
		var data = JSON.stringify(_data); 
		if(_data.auth==''||_data.auth==undefined){
			layer.confirm('当前密码为空,是否继续提交',{icon:3},function(){
				doSubmit();
			});
		}else{
			doSubmit();
		}
		function doSubmit(){
			var dataStr = aesEncrypt(data);
			ajax.remoteCall("${ctxPath}/servlet/cacheMgr?action=saveConf",{encryptStr:dataStr},function(result) { 	
				if(result.state==1){
					layer.msg(result.msg+"请重启服务器配置生效。",{icon: 1,time:800},function(){
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
	}
	
	function cacheMgr(){
		loadPage("server/cache-mgr.jsp",{});
	}
	
	function cacheAdvConfig(){
		var val=$("[name='con.G_MEMCACHE_ADDR']").val();
		loadPage("server/cache-adv-conf.jsp?url="+val,{});
	}
	
</script>