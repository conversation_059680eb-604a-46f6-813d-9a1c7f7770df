package org.easitline.console.job;

import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;
import org.quartz.CronScheduleBuilder;
import org.quartz.CronTrigger;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.TriggerBuilder;
import org.quartz.TriggerKey;
import org.quartz.impl.StdSchedulerFactory;

/**
 * 调度任务工具类
 * <AUTHOR>  2017-05-08
 */
public final class QuartzSchedulerEngine { 
	

    private static Scheduler scheduler;
    
    private static Logger getLogger(){
    	return  LogEngine.getLogger("easitline-job");
    }
        
    /**
     * 启动engin
     */
    public static void startSchedulerEngin() {
    	try {
     	   if(null == scheduler) {
     		   scheduler = StdSchedulerFactory.getDefaultScheduler();
    		   scheduler.start();
     	   } 
 		} catch (SchedulerException e) {
 			getLogger().error(e.getMessage(),e);
 		}
    }
    
    /**
     * 停止所有调度任务
     */
    public static void stopSchedulerEngin() {
    	try {
     	   if(null != scheduler) {
     		   if(scheduler.isStarted()) {
     			  scheduler.shutdown();
     		   }
     	   } 
 		} catch (SchedulerException e) {
 			getLogger().error(e.getMessage(),e);
 		}
    }

    /**
     * 新增调度任务
     * @param jobGroup 转入应用名称作为job分组
     * @param jobName  转入jobId用为jobName
     * @param cronExpression
     * @return
     * @throws SchedulerException
     */
	public static boolean addJob(String jobGroup, String jobName, String cronExpression) throws SchedulerException {
    	
    	// TriggerKey : name + group
        TriggerKey triggerKey = TriggerKey.triggerKey(jobName, jobGroup);
        JobKey jobKey = new JobKey(jobName, jobGroup);
        
        //验证job是否存在
        if (checkExists(jobName, jobGroup)) {
        	getLogger().error(String.format("创建job失败，job已存在, jobGroup:%s, jobName:%s", jobGroup, jobName));
            return false;
        }
        
        // CronTrigger : TriggerKey + cronExpression	// withMisfireHandlingInstructionDoNothing 忽略掉调度终止过程中忽略的调度
        CronScheduleBuilder cronScheduleBuilder = CronScheduleBuilder.cronSchedule(cronExpression).withMisfireHandlingInstructionDoNothing();
        CronTrigger cronTrigger = TriggerBuilder.newTrigger().withIdentity(triggerKey).withSchedule(cronScheduleBuilder).build();


		/*Class<? extends Job> jobClass =  null;	
		if(Constants.JobType.CONCURENT.getValue().equals(jobType)) {
			jobClass =  QuartzCronTiggerJob.class;
		} else {
			jobClass =  QuartzStatefulJob.class;
		}*/
        //暂时只支持串行
        Class<? extends Job> jobClass = QuartzStatefulJob.class;
        
		JobDetail jobDetail = JobBuilder.newJob(jobClass).withIdentity(jobKey).build();
        
        // schedule : jobDetail + cronTrigger
        scheduler.scheduleJob(jobDetail, cronTrigger);
        
        String.format("创建job成功");
        
        return true;
    }
	
	/**
	 * 修改调度任务
	 * @param jobGroup
	 * @param jobName
	 * @param cronExpression
	 * @param jobType
	 * @return
	 * @throws SchedulerException
	 */
	public static boolean updateJob(String jobGroup, String jobName, String cronExpression) throws SchedulerException {
		
		TriggerKey triggerKey = TriggerKey.triggerKey(jobName, jobGroup);
        
        boolean result = false;
        if (checkExists(jobName, jobGroup)) {
            scheduler.unscheduleJob(triggerKey);  //卸载调度任务
            addJob(jobGroup, jobName, cronExpression);   //新建调度任务
            result = true;
            getLogger().info("更新job成功");
        } else {
        	getLogger().info("更新job失败，调度任务不存在，jobName:" + jobName + " jobGroup:" + jobGroup);
        }
        return result;
	}
    
    /**
     * 验证job是否已存在
     * @param jobName
     * @param jobGroup
     * @return
     * @throws SchedulerException
     */
 	public static boolean checkExists(String jobName, String jobGroup) throws SchedulerException{
 		if(scheduler==null){
 			return false;
 		}
 		TriggerKey triggerKey = TriggerKey.triggerKey(jobName, jobGroup);
 		return scheduler.checkExists(triggerKey);
 	}
 	
 	/**
     * 马上执行调度任务
     * @param jobName  转入jobId用为jobName
     * @param jobGroup 转入应用名称作为job分组
     * @param cronExpression
     * @return
     * @throws SchedulerException
     */
    public static boolean triggerJob(String jobName, String jobGroup) throws SchedulerException {
    	// TriggerKey : name + group
    	JobKey jobKey = new JobKey(jobName, jobGroup);
        
        boolean result = false;
        if (checkExists(jobName, jobGroup)) {
            scheduler.triggerJob(jobKey);
            result = true;
            getLogger().info("运行job成功");
        } else {
        	getLogger().error("运行job失败，调度任务不存在，jobName:" + jobName + " jobGroup:" + jobGroup);
        }
        return result;
    }
    
    /**
     * 卸载调度任务
     * @param jobName  转入jobId用为jobName
     * @param jobGroup 转入应用名称作为job分组
     * @param cronExpression
     * @return
     * @throws SchedulerException
     */
    public static boolean removeJob(String jobName, String jobGroup) throws SchedulerException {
    	// TriggerKey : name + group
        TriggerKey triggerKey = TriggerKey.triggerKey(jobName, jobGroup);
        
        boolean result = false;
        if (checkExists(jobName, jobGroup)) {
            result = scheduler.unscheduleJob(triggerKey);
            getLogger().info("移除job成功");
        } else {
        	getLogger().error("移除job失败，调度任务不存在，jobName:" + jobName + " jobGroup:" + jobGroup);
        }
        return result;
    }
    
    /**
     * 暂停调度任务
     * @param jobName  转入jobId用为jobName
     * @param jobGroup 转入应用名称作为job分组
     * @param cronExpression
     * @return
     * @throws SchedulerException
     */
    public static boolean pauseJob(String jobName, String jobGroup) throws SchedulerException {
    	// TriggerKey : name + group
    	TriggerKey triggerKey = TriggerKey.triggerKey(jobName, jobGroup);
    	JobKey jobKey = JobKey.jobKey(jobName, jobGroup);
        
        boolean result = false;
        if (checkExists(jobName, jobGroup)) {
            scheduler.pauseTrigger(triggerKey);
            scheduler.pauseJob(jobKey);
            result = true;
            getLogger().info("暂停job成功");
        } else {
        	getLogger().error("暂停调度任务失败，调度任务不存在，jobName:" + jobName + " jobGroup:" + jobGroup);
        }
        return result;
    }
    
    /**
     * 重启调度任务
     * @param jobName  转入jobId用为jobName
     * @param jobGroup 转入应用名称作为job分组
     * @param cronExpression
     * @return
     * @throws SchedulerException
     */
    public static boolean resumeJob(String jobName, String jobGroup, String cronExpression) throws SchedulerException {
    	// TriggerKey : name + group
    	TriggerKey triggerKey = TriggerKey.triggerKey(jobName, jobGroup);
    	JobKey jobKey = JobKey.jobKey(jobName, jobGroup);
        
        boolean result = false;
        if (checkExists(jobName, jobGroup)) {
            scheduler.resumeTrigger(triggerKey);
            scheduler.resumeJob(jobKey);
            result = true;
            getLogger().info("重启job成功");
        } else {	
        	getLogger().info("重启调度任务失败，调度任务不存在，jobName:" + jobName + " jobGroup:" + jobGroup);
        }
        return result;
    }
    
}