package org.easitline.console.servlet;


import java.lang.management.GarbageCollectorMXBean;
import java.lang.management.ManagementFactory;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import javax.servlet.annotation.WebServlet;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.RandomKit.SMSAuthCodeType;
import org.easitline.console.base.ConsoleBaseServlet;
import org.easitline.console.service.ConfigCryptorService;
import org.easitline.console.utils.ConsoleUtils;
import org.easitline.console.utils.ServerInfoUtils;
import org.easitline.console.vo.DatasourceModel;
import org.easitline.console.vo.DatasourceRowMapper;
import org.easitline.console.vo.ServerInfoModel;

import com.alibaba.fastjson.JSONObject;


@WebServlet(urlPatterns="/servlet/serverInfo")
public class ServerInfoServlet extends ConsoleBaseServlet {

	private static final long serialVersionUID = -8378306707995318208L;

	Thread findThread(long threadId) {
        ThreadGroup group = Thread.currentThread().getThreadGroup();
        while(group != null) {
            Thread[] threads = new Thread[(int)(group.activeCount() * 1.2)];
            int count = group.enumerate(threads, true);
            for(int i = 0; i < count; i++) {
                if(threadId == threads[i].getId()) {
                    return threads[i];
                }
            }
            group = group.getParent();
        }
        return null;
    } 
	
	public EasyResult  actionForKillThead(){
		JSONObject param = getJSONObject();
		long threadId = param.getLongValue("id");
		Thread thread = findThread(threadId);
		if(thread.isAlive()){
			thread.interrupt();
			thread.isInterrupted();
		}
		return EasyResult.ok();
	}
	 
	public EasyResult  actionForServerInfo(){
		if(!ConsoleUtils.isTomcat()) {
			return EasyResult.ok(ServerInfoUtils.getService().getCommonData());
		}
		ServerInfoModel model = null;
		try {
			model = new ServerInfoModel();
			return EasyResult.ok(model);
		} catch (Exception e) {
			this.error(e.getMessage(),e);
			return EasyResult.ok(ServerInfoUtils.getService().getCommonData());
		}
	}
	
	public EasyResult  actionForGc(){
		long beginTs = System.currentTimeMillis();
		Runtime runtime = Runtime.getRuntime();
		long beforeMemory = runtime.totalMemory() - runtime.freeMemory();
	    for (GarbageCollectorMXBean gcBean : ManagementFactory.getGarbageCollectorMXBeans()) {
            getLogger().info("GC Name: " + gcBean.getName());
        }
		System.gc();
		long afterMemory = runtime.totalMemory() - runtime.freeMemory();
		long endTs = System.currentTimeMillis();
		long memoryReclaimed = beforeMemory - afterMemory;
		double memoryReclaimedMB = bytesToMB(memoryReclaimed);
		String message = "手动垃圾回收 - 耗时: " + (endTs - beginTs) + "ms, 回收内存: " + String.format("%.2f", memoryReclaimedMB) + " MB";
		this.getLogger().info(message);
		this.addOperateLog(message, getConsoleUser());
		return EasyResult.ok(null,message);
	}
	
	private static double bytesToMB(long bytes) {
	    return bytes / (1024.0 * 1024.0);
	}
	
	
	public EasyResult actionForGenSecurityKey(){
		return EasyResult.ok(RandomKit.smsAuthCode(8, SMSAuthCodeType.CharAndNumbers));
	}
	
	
	/**
	 * 获取最新版本
	 * @return
	 * @throws Exception
	 */
	public EasyResult  queryForGetVersion() throws Exception {
		return EasyResult.ok();
	}
	
	/**
	 * 查看JVM运行时信息
	 * @return
	 */
	public String actionForRuntimeInfo(){
		this.getRequest().setAttribute("_page", "/pages/server/runtimeInfo.jsp");
		return "/pages/home.jsp";
	}
	
	/**
	 * 修改管理员信息
	 * @return
	 */
	public String actionForManagerInfo(){
		this.getRequest().setAttribute("_page", "/pages/server/manager-info.jsp");
		return "/pages/home.jsp";
	}
	
	/**
	 * 修改管理员信息
	 * @return
	 */
	public String actionForConfig(){
		String sql = "select * from  EASI_CONF ";
		try {
			List<EasyRow> list = this.getConsoleQuery().queryForList(sql);
			for(EasyRow row :list){
				this.getRequest().setAttribute(row.getColumnValue("CONF_KEY"),ConfigCryptorService.decryptString(row.getColumnValue("CONF_VALUE"),"GV"));
			}
			this.setDS();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
	
		this.getRequest().setAttribute("_page", "/pages/server/server-config.jsp");
		return "/pages/home.jsp";
	}
	
	
	
	private void  setDS() throws SQLException {

		String sql = "select * from EASI_DS_INFO";
		List<DatasourceModel> list = this.getConsoleQuery().queryForList(sql, null,new DatasourceRowMapper());
		this.getRequest().setAttribute("dsList", list);
	}
	
	public EasyResult actionForGlobalConfig(){
		return EasyResult.ok(ServerContext.getConfig());
	}
	
	public EasyResult actionForReloadConfig(){
		ServerContext.reload();
		return EasyResult.ok();
	}
	
	public EasyResult actionForGetAllConfig() {
		JSONObject obj = new JSONObject();
		String sql = "select * from  EASI_CONF";
		try {
			List<EasyRow> list = this.getConsoleQuery().queryForList(sql);
			for(EasyRow row :list){
				obj.put(row.getColumnValue("CONF_KEY"),ConfigCryptorService.decryptString(row.getColumnValue("CONF_VALUE"),"GV"));
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok(obj);
	}
	
	public EasyResult actionForSaveLoginConfig(){
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		JSONObject jsonObject = this.getJSONObject();
		Set<String> keys = jsonObject.keySet();
		for(String key:keys) {
			String sql = "update EASI_CONF set CONF_VALUE = ? where CONF_KEY = ?";
			try {
				String value = jsonObject.getString(key);
//				value = ConfigCryptorService.encryptString(value, "GV");
				int result = this.getConsoleQuery().executeUpdate(sql, new Object[]{value,key});
				if(result==0) {
					sql = "insert into EASI_CONF(CONF_KEY,CONF_VALUE,CAN_DELETE) values(?,?,3)";
					this.getConsoleQuery().executeUpdate(sql, new Object[]{key,value});
				}
				ServerContext.reload();
			} catch (SQLException e) {
				this.error(null, e);
				return EasyResult.fail(e.getMessage());
			}
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDeleteConfig(){
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		String pk=getJsonPara("confKey");
		try {
			this.getConsoleQuery().executeUpdate("delete from EASI_CONF where conf_key = ?", pk);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	/**
	 * 保存或更新配置项
	 * @param configKey 配置键
	 * @param configValue 配置值
	 * @throws SQLException
	 */
	private void saveOrUpdateConfig(String configKey, String configValue) throws SQLException {
	    configValue = ConfigCryptorService.encryptString(configValue, "GV");
		String updateSql = "UPDATE EASI_CONF SET CONF_VALUE = ? WHERE CONF_KEY = ?";
		int result = this.getConsoleQuery().executeUpdate(updateSql, new Object[]{configValue, configKey});
		
		if (result == 0) {
			String insertSql = "INSERT INTO EASI_CONF(CONF_KEY, CONF_VALUE, CAN_DELETE) VALUES(?, ?, 1)";
			this.getConsoleQuery().execute(insertSql, new Object[]{configKey, configValue});
		}
	}
	
	/**
	 * 批量保存配置项
	 * @param configMap 配置映射
	 * @throws SQLException
	 */
	private void batchSaveConfigs(Map<String, String> configMap) throws SQLException {
		for (Map.Entry<String, String> entry : configMap.entrySet()) {
			saveOrUpdateConfig(entry.getKey(), entry.getValue());
		}
	}

	public EasyResult actionForSaveConfig(){
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		
		try {
			JSONObject jsonObject = this.getJSONObject("con");
			Map<String, String> configMap = new HashMap<>();
			
			// 布尔类型配置项（需要特殊处理）
			configMap.put("G_JOB_SERVER", StringUtils.isNotBlank(jsonObject.getString("G_JOB_SERVER")) ? "true" : "false");
			configMap.put("SECURITY_ENTER", StringUtils.isNotBlank(jsonObject.getString("SECURITY_ENTER")) ? "1" : "0");
			
			// 标准字符串配置项（键名和JSON字段名相同）
			String[] standardConfigs = {
				"G_ROOT_URL","SECURITY_KEY", "CONSOLE_ALLOC_ACCESS_IP", "ALLOC_ACCESS_SERVER_ADDR",
				"CONSOLE_DEPLOY_DIR", "PROFILE_ACTIVE", "G_VERSION_CENTER_URL",
				"G_SYS_NAME", "G_NODE_NAME", "G_EFS_DIR", "G_SYS_DS",
				"G_WELCOME_PATH", "G_PORTAL_STYLE", "LUCENE_INDEX_PATH"
			};
			
			// 批量处理标准配置项
			for (String configKey : standardConfigs) {
				configMap.put(configKey, StringUtils.trimToEmpty(jsonObject.getString(configKey)));
			}
			
			// 批量保存配置
			batchSaveConfigs(configMap);
			
			// 处理动态配置键
			JSONObject confKeysData = getJSONObject("confKeys");
			if (confKeysData != null) {
				for (Entry<String, Object> entry : confKeysData.entrySet()) {
					String key = entry.getKey();
					JSONObject object = getJSONObject(key);
					EasyRecord record = new EasyRecord("EASI_CONF", "CONF_KEY");
					record.setColumns(object);
					this.getConsoleQuery().update(record);
				}
			}
			
			// 处理新配置项
			JSONObject newConfig = getJSONObject("config");
			if (newConfig != null && StringUtils.isNotBlank(newConfig.getString("CONF_KEY"))) {
				EasyRecord record = new EasyRecord("EASI_CONF", "CONF_KEY");
				record.setColumns(newConfig);
				this.getConsoleQuery().save(record);
			}
			
			// 记录操作日志
			this.addOperateLog("修改平台参数", jsonObject.toJSONString());
			
			// 重新加载配置
			ServerContext.reload();
			
			return EasyResult.ok(null, "保存成功");
			
		} catch (Exception ex) {
			this.error(ex.getMessage(), ex);
			return EasyResult.error(0, "保存失败，原因：" + ex.getMessage());
		}
	}

	
	public EasyResult actionForDelAppLog() {
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		JSONObject param = getJSONObject();
		EasySQL sql = new EasySQL("delete from EASI_APP_LOG where 1=1");
		sql.append(param.getString("logId"),"AND LOG_ID = ?");
	    sql.append(param.getString("appId"),"AND APP_ID = ?");
	    sql.append(param.getString("beginDate"),"AND DATE_ID >= ?");
	    sql.append(param.getString("endDate"),"AND DATE_ID <= ?");
//		try {
//			this.getConsoleQuery().executeUpdate(sql.getSQL(), sql.getParams());
//		} catch (SQLException e) {
//			this.error(e.getMessage(), e);
//			return EasyResult.fail(e.getMessage());
//		}  
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForDelLoginLog() {
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		JSONObject param = getJSONObject();
		EasySQL sql = new EasySQL("delete from EASI_LOGIN_LOG where 1=1");
		sql.append(param.getString("logId"),"AND LOG_ID = ?");
//		try {
//			this.getConsoleQuery().executeUpdate(sql.getSQL(), sql.getParams());
//		} catch (SQLException e) {
//			this.error(e.getMessage(), e);
//			return EasyResult.fail(e.getMessage());
//		}  
		return EasyResult.ok();
	}
	
	public EasyResult actionForDelOperateLog() {
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		JSONObject param = getJSONObject();
		EasySQL sql = new EasySQL("delete from EASI_OPERATE_LOG where 1=1");
		sql.append(param.getString("logId"),"AND LOG_ID = ?");
//		try {
//			this.getConsoleQuery().executeUpdate(sql.getSQL(), sql.getParams());
//		} catch (SQLException e) {
//			this.error(e.getMessage(), e);
//			return EasyResult.fail(e.getMessage());
//		}  
		return EasyResult.ok();
	}

	public EasyResult actionForReloadLog(){
		LogEngine.removeAll();
		return EasyResult.ok();
	}
	
	@Override
	protected String getResId() {
		// TODO Auto-generated method stub
		return null;
	}

}
