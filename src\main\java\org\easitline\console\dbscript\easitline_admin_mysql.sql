/*==============================================================*/
/* Table: EASI_OPERATOR_LOG                                         */
/*==============================================================*/
create table EASI_OPERATOR_LOG  (
   SERIAL_ID            varchar(32)   not null,
   USER_ID              varchar(32)   not null,
   USER_ACCT            varchar(50)   not null,
   OPR_TIME             varchar(19)      not null,
   IP_ADDR              varchar(32),
   OPR_TYPE             int,
   OPR_MODULE           varchar(100),
   OPR_CONTENT          varchar(2000),
   primary key (SERIAL_ID)
);

/*==============================================================*/
/* Table: EASI_JOB_STEP                                         */
/*==============================================================*/
create table EASI_JOB_STEP
(
   JOB_ID               varchar(32) not null comment '调度ID',
   JOB_NAME             varchar(100) comment '调度名称',
   CRON                 varchar(20) comment 'CRON',
   JOB_TYPE             int comment '调度类型 1:串行 2:并行',	
   JOB_DESC             varchar(1000) comment '调度描述',
   APP_NAME             varchar(100) comment '应用名称，同时也用于jobGroup',
   SERVER_NAME          varchar(50) comment '执行MARS服务名称，为空时，缺省采用当前的mars进行调度',
   JOB_STATE            int comment '状态 1:正常 0:暂停',
   SERVICE_ID           varchar(50) comment 'ESB服务ID',
   SERVICE_NAME         varchar(100) comment 'ESB服务名',
   BEGIN_DATE           varchar(10) comment '任务开始日期',
   END_DATE             varchar(10) comment'任务结束日期',
   BEGIN_TIME           varchar(5) comment'任务开始时间',
   END_TIME             varchar(5) comment'任务结束时间',
   NEXT_TIME            varchar(19) comment'下次执行时间',
   OK_COUNT             int comment '成功执行次数',
   FAIL_COUNT           int comment '失败次数',
   LAST_OK_TIME         varchar(32) comment '最后执行成功时间',
   LAST_FAIL_TIME       varchar(32) comment '最后执行失败时间',
   DEPEND_JOBS          varchar(200) comment '依赖调度，多个依赖之间采用"|"分隔，调度必须是月和日的调度才生效',
   primary key (JOB_ID)
);

/*==============================================================*/
/* Table: EASI_JOB_LOG                                          */
/*==============================================================*/
create table EASI_JOB_LOG
(
   DATE_KEY             varchar(32) not null comment 'date_key',
   JOB_ID               varchar(32) not null comment '调度ID',
   DATE_ID              int comment '调度日期，格式：YYYYMMDD',
   JOB_STATE            int comment '调度状态，0:调度中  1：成功  -1：失败',
   FIRST_TIME           varchar(19) comment '首次调度时间',
   LAST_TIME            varchar(19) comment '最后执行时间',
   EXEC_COUNT           int comment '执行次数',
   EXCEPTION_INFO       text comment '异常信息',
   primary key (DATE_KEY, JOB_ID)
);

alter table EASI_JOB_LOG comment '调度日志';

/*==============================================================*/
/* Table: EASI_DEPT                                             */
/*==============================================================*/
create table EASI_DEPT
(
   DEPT_ID              varchar(32) not null comment '部门ID，唯一',
   DEPT_CODE            varchar(90) comment '部门代码，格式：001002003....',
   DEPT_NAME            varchar(100) comment '部门名称',
   DEPT_PATH_NAME       varchar(500)  comment '部门路径',
   P_DEPT_CODE          varchar(90) comment '父部门代码',
   DEPT_TYPE            varchar(30) comment '部门类型，参考：EASI_DEPT_TYPE',
   LINK_MAN             varchar(30),
   LINK_MOBILE          varchar(30),
   QRCODE_URL           varchar(255),
   RESERVE1             varchar(100) comment '保留字段1',
   RESERVE2             varchar(100) comment '保留字段2',
   RESERVE3             varchar(100) comment '保留字段3',
   RESERVE4             varchar(100) comment '保留字段4',
   RESERVE5             varchar(100) comment '保留字段5',
   RESERVE6             varchar(100) comment '保留字段6',
   RESERVE7             varchar(100) comment '保留字段7',
   RESERVE8             varchar(100) comment '保留字段8',
   RESERVE9             varchar(100) comment '保留字段9',
   RESERVE10            varchar(100) comment '保留字段10',
   IDX_ORDER			INT  comment '排序',
   primary key (DEPT_ID)
)
;

/*==============================================================*/
/* Index: IDX_FD_DEPT_NAME                                      */
/*==============================================================*/
create index IDX_FD_DEPT_NAME on EASI_DEPT
(
   DEPT_NAME
);

/*==============================================================*/
/* Index: IDX_FD_DEPT_CODE                                      */
/*==============================================================*/
create index IDX_FD_DEPT_CODE on EASI_DEPT
(
   DEPT_CODE
);

/*==============================================================*/
/* Table: EASI_DEPT_REF                                         */
/*==============================================================*/
create table EASI_DEPT_REF
(
   F_DEPT_TYPE          varchar(20) not null comment '上级部门',
   N_DEPT_TYPE          varchar(20) not null comment '下级部门',
   primary key (F_DEPT_TYPE, N_DEPT_TYPE)
);

alter table EASI_DEPT_REF comment '定义部门之间的关系,  例如：全国中心-省中心， 通常由应用来定义';

/*==============================================================*/
/* Table: EASI_DEPT_TYPE                                        */
/*==============================================================*/
create table EASI_DEPT_TYPE
(
   DEPT_TYPE            varchar(20) not null comment '部门类型',
   DEPT_TYPE_NAME       varchar(50) not null comment '部门类型名称',
   NODE_TYPE 			INT  comment '节点类型',
   primary key (DEPT_TYPE)
)
;

alter table EASI_DEPT_TYPE comment '用于定义部门的类型，缺省：部门'
;


INSERT INTO EASI_DEPT_TYPE(DEPT_TYPE,DEPT_TYPE_NAME,NODE_TYPE) VALUES('SYS_SF','省份',1)
;

INSERT INTO EASI_DEPT_TYPE(DEPT_TYPE,DEPT_TYPE_NAME,NODE_TYPE) VALUES('SYS_DS','地市',1)
;

INSERT INTO EASI_DEPT_TYPE(DEPT_TYPE,DEPT_TYPE_NAME,NODE_TYPE) VALUES('SYS_ZQ','镇区',1)
;

INSERT INTO EASI_DEPT_TYPE(DEPT_TYPE,DEPT_TYPE_NAME,NODE_TYPE) VALUES('SYS_JD','街道',1)
;

INSERT INTO EASI_DEPT_TYPE(DEPT_TYPE,DEPT_TYPE_NAME,NODE_TYPE) VALUES('SYS_DW','单位',2)
;

INSERT INTO EASI_DEPT_TYPE(DEPT_TYPE,DEPT_TYPE_NAME,NODE_TYPE) VALUES('SYS_BM','部门',3)
;


/*==============================================================*/
/* Table: EASI_DEPT_USER                                        */
/*==============================================================*/
create table EASI_DEPT_USER
(
   DEPT_ID              varchar(32) not null comment '部门ID',
   USER_ID              varchar(32) not null comment '用户账号，全局唯一',
   POST_TYPE            INT,
   IDX_ORDER            INT,
   primary key (DEPT_ID, USER_ID)
)
;
CREATE TABLE EASI_USER_DEPT (
  USER_ID varchar(32) NOT NULL COMMENT '用户ID',
  DEPT_ID varchar(32) NOT NULL COMMENT '部门ID',
  PRIMARY KEY (USER_ID,DEPT_ID)
) 
;

/*==============================================================*/
/* Table: EASI_LABEL                                            */
/*==============================================================*/
create table EASI_LABEL
(
   LABEL_ID             varchar(32) not null comment '标签ID',
   LABEL_NAME           varchar(30) not null comment '标签名称',
   OWNER_DEPT_ID        varchar(32),
   primary key (LABEL_ID)
)
;

/*==============================================================*/
/* Table: EASI_LABEL_DEPT                                       */
/*==============================================================*/
create table EASI_LABEL_DEPT
(
   LABEL_ID             varchar(32) not null comment '标签ID',
   DEPT_ID              varchar(32) not null comment '部门ID',
   IDX_ORDER            INT comment '索引排序号',
   primary key (LABEL_ID, DEPT_ID)
)
;

/*==============================================================*/
/* Table: EASI_LABEL_ROLE                                       */
/*==============================================================*/
create table EASI_LABEL_ROLE
(
   LABEL_ID             varchar(32) not null comment '标签ID',
   ROLE_ID              varchar(32) not null comment '角色ID',
   IDX_ORDER            INT comment '索引排序号',
   primary key (LABEL_ID, ROLE_ID)
)
;

/*==============================================================*/
/* Table: EASI_LABEL_USER                                       */
/*==============================================================*/
create table EASI_LABEL_USER
(
   USER_ID              varchar(32) comment '用户账号，全局唯一',
   LABEL_ID             varchar(32) not null comment '标签ID',
   IDX_ORDER            INT comment '索引',
   primary key (LABEL_ID)
)
;

/*==============================================================*/
/* Table: EASI_LOGIN_LOG                                        */
/*==============================================================*/
create table EASI_LOGIN_LOG
(
   LOGIN_LOG_ID         varchar(32) not null comment 'ID',
   USER_ID              varchar(32) not null comment '用户账号，全局唯一',
   USER_ACCT            varchar(50) not null,
   LOGIN_TYPE           int comment '1：系统账号（人工设定）
            2：手机号码
            3：邮箱
            4：微信扫描登陆',
   SESSION_ID           varchar(50),
   LOGIN_TIME           VARCHAR(19) comment '格式:YYYY-MM-DD HH:MM:DD',
   LOGIN_STATE          INT comment '1：成功 0：失败',
   LOGIN_IP             VARCHAR(30),
   LOGIN_CLIENT_TYPE    INT,
   LOGOUT_TIME          VARCHAR(19),
   REMARK               VARCHAR(200),
   primary key (LOGIN_LOG_ID)
)
;

/*==============================================================*/
/* Index: IDX_EASI_LOGIN_LOG_1                                  */
/*==============================================================*/
create index IDX_EASI_LOGIN_LOG_1 on EASI_LOGIN_LOG
(
   USER_ID
)
;

/*==============================================================*/
/* Index: IDX_EASI_LOGIN_LOG_2                                  */
/*==============================================================*/
create index IDX_EASI_LOGIN_LOG_2 on EASI_LOGIN_LOG
(
   USER_ACCT
)
;

/*==============================================================*/
/* Index: IDX_EASI_LOGIN_LOG_3                                  */
/*==============================================================*/
create index IDX_EASI_LOGIN_LOG_3 on EASI_LOGIN_LOG
(
   LOGIN_TIME
)
;

CREATE TABLE easi_post (
  post_id varchar(32) NOT NULL COMMENT '岗位ID',
  post_code varchar(64) NOT NULL COMMENT '岗位编码',
  post_name varchar(50) NOT NULL COMMENT '岗位名称',
  post_sort int(4) default 1 COMMENT '显示顺序',
  status char(1) NOT NULL COMMENT '状态（0正常 1停用）',
  create_by varchar(64) DEFAULT '' COMMENT '创建者',
  create_time varchar(19) DEFAULT NULL COMMENT '创建时间',
  update_by varchar(32) COMMENT '更新者',
  update_time varchar(19) COMMENT '更新时间',
  remark varchar(500) COMMENT '备注',
  PRIMARY KEY (`post_id`)
)
;
alter table easi_post comment '岗位信息表'
;

/*==============================================================*/
/* Table: EASI_POSI_TYPE                                        */
/*==============================================================*/
create table EASI_POSI_TYPE
(
   POST_TYPE            INT not null,
   POST_TYPE_NAME       VARCHAR(20),
   primary key (POST_TYPE)
)
;

alter table EASI_POSI_TYPE comment '1：负责人  9：员工';

insert into EASI_POSI_TYPE(POST_TYPE,POST_TYPE_NAME)   values(1,'负责人')
;
insert into EASI_POSI_TYPE(POST_TYPE,POST_TYPE_NAME)   values(9,'员工')
;

/*==============================================================*/
/* Table: EASI_RES                                              */
/*==============================================================*/
create table EASI_RES
(
   RES_ID               varchar(100) not null comment '资源ID，全局唯一，由应用本身定义',
   RES_NAME             varchar(50) not null comment '资源名称',
   P_RES_ID             varchar(100) not null comment '父资源ID',
   RES_URL              varchar(200) comment 'URL访问地址',
   RES_ICON             varchar(50) comment '图标',
   RES_TYPE             int default 1 comment '资源类型，1：应用 ，2：菜单，3：功能，9: 其它',
   RES_TARGET           varchar(20) comment '打开方式，_blank 在新的窗口打开，否则在portal中打开',
   RES_STATE            int default 0 comment '状态，0:正常，缺省 ，1：暂停',
   IDX_ORDER            int comment '排序',
   APP_ID               varchar(30),
   PORTAL               varchar(30),
   primary key (RES_ID)
)
;


alter table EASI_RES comment '对系统的应用、菜单、功能进行进行统一的定义。'
;

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN', '系统管理', '2000', null, 2, null, 0, 99, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_USER', '用户管理', 'EASI_ADMIN', '/easitline-admin/servlet/user?action=List', 2, '_default', 0, 11, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_ROLE', '角色管理', 'EASI_ADMIN', '/easitline-admin/servlet/role?action=List', 2, '_default', 0, 21, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_FRAMEWORK', '组织架构管理', 'EASI_ADMIN', '/easitline-admin/servlet/framework?action=main', 2, '_default', 0, 31, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_RES', '资源管理', 'EASI_ADMIN', '/easitline-admin/servlet/resource?action=main', 2, '_default', 0, 41, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_POST', '岗位管理', 'EASI_ADMIN', '/easitline-admin/pages/post/post-list.jsp', 2, '_default', 0, 51, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_PC_DICT', '数据字典管理', 'EASI_ADMIN', '/easitline-admin/servlet/dict?action=dictTypeList', 2, '_default', 0, 61, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_LOG_LOGIN', '登录日志', 'EASI_ADMIN', '/easitline-admin/pages/log/login-log.jsp', 2, '_default', 0, 71, 'easitline-admin', '2000');

insert into easi_res (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_LOG_OPERATOR', '操作日志', 'EASI_ADMIN', '/easitline-admin/pages/log/operator-log.jsp', 2, '_default', 0, 81, 'easitline-admin', '2000');


insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_LABEL', '标签管理', 'EASI_ADMIN', '/easitline-admin/servlet/label?action=main', 2, '_default', 0, 32, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_USER_NEW', '添加用户', 'EASI_ADMIN_USER', null, 3, '_default', 0, 12, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_ROLE_NEW', '添加角色', 'EASI_ADMIN_ROLE', null, 3, '_default', 0, 22, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_FRAMEWORK_USER_BDEL', '批量删除成员分管部门', 'EASI_ADMIN_FRAMEWORK', null, 3, '_default', 0, 37, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_ROLE_DEL', '删除角色', 'EASI_ADMIN_ROLE', null, 3, '_default', 0, 23, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_ROLE_MOD', '修改角色', 'EASI_ADMIN_ROLE', null, 3, '_default', 0, 24, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_ROLE_DIS_USER', '分配用户', 'EASI_ADMIN_ROLE', null, 3, '_default', 0, 25, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_ROLE_DIS_RES', '分配资源', 'EASI_ADMIN_ROLE', null, 3, '_default', 0, 26, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_FRAMEWORK_DEPT_NEW', '新增部门', 'EASI_ADMIN_FRAMEWORK', null, 3, '_default', 0, 32, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_FRAMEWORK_DEPT_MOD', '修改部门信息', 'EASI_ADMIN_FRAMEWORK', null, 3, '_default', 0, 33, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_FRAMEWORK_DEPT_DEL', '删除部门', 'EASI_ADMIN_FRAMEWORK', null, 3, '_default', 0, 34, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_LABEL_LABEL_NEW', '新增标签', 'EASI_ADMIN_LABEL', null, 3, '_default', 0, 52, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_LABEL_MEMBER_NEW', '添加标签成员', 'EASI_ADMIN_LABEL', null, 3, '_default', 0, 53, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_LABEL_MEMBER_DEL', '移除标签成员', 'EASI_ADMIN_LABEL', null, 3, '_default', 0, 54, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_RES_RES_MOD', '修改资源', 'EASI_ADMIN_RES', null, 3, '_default', 0, 62, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_RES_RES_DEL', '删除资源', 'EASI_ADMIN_RES', null, 3, '_default', 0, 63, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_FRAMEWORK_DUSER_NEW', '新增部门成员', 'EASI_ADMIN_FRAMEWORK', null, 3, '_default', 0, 35, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_FRAMEWORK_DUSER_DEL', '移除部门成员', 'EASI_ADMIN_FRAMEWORK', null, 3, '_default', 0, 36, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_FRAMEWORK_UDEPT_NEW', '添加成员分管部门', 'EASI_ADMIN_FRAMEWORK', null, 3, '_default', 0, 38, 'easitline-admin', '2000');

insert into EASI_RES (RES_ID, RES_NAME, P_RES_ID, RES_URL, RES_TYPE, RES_TARGET, RES_STATE, IDX_ORDER, APP_ID, PORTAL)
values ('EASI_ADMIN_FRAMEWORK_UDEPT_DEL', '移除成员分管部门', 'EASI_ADMIN_FRAMEWORK', null, 3, '_default', 0, 39, 'easitline-admin', '2000');


/*==============================================================*/
/* Index: PK_SYS_FUNLIST                                        */
/*==============================================================*/
create unique index PK_EASI_RES on EASI_RES
(
   RES_ID
);

/*==============================================================*/
/* Index: SYS_FUNLIST_MENU_IDX                                  */
/*==============================================================*/
create index IDX_EASI_RES_1 on EASI_RES
(
   RES_TYPE
);

/*==============================================================*/
/* Table: EASI_ROLE                                             */
/*==============================================================*/
create table EASI_ROLE
(
   ROLE_ID              varchar(32) not null comment '角色ID',
   ROLE_NAME            varchar(30) not null comment '角色名称',
   ROLE_TYPE            int not null comment '角色类型： 0:系统角色，SYS_开头，不可更改，1：业务角色，由应用根据需要定义，2：自定义角色，由系统提供维护',
   APP_ID               varchar(30) comment '所属应用，这里指针对应用的自定义业务角色来确定',
   ROLE_DESC            varchar(100) comment '角色描述',
   OWNER_DEPT_ID        varchar(32),
   IDX_ORDER            int not null default 99,
   primary key (ROLE_ID)
)
;

insert into EASI_ROLE(ROLE_ID,ROLE_NAME,ROLE_TYPE,APP_ID ,ROLE_DESC,OWNER_DEPT_ID,IDX_ORDER) values('SYS_SUPER_USER','超级管理员',0,'SYSTEM','系统超级用户,拥有最高权限!','0',1)
;

insert into EASI_ROLE(ROLE_ID,ROLE_NAME,ROLE_TYPE,APP_ID ,ROLE_DESC,OWNER_DEPT_ID,IDX_ORDER) values('SYS_NODE_ADMIN','单位管理员',0,'SYSTEM','维护单位人员及部门信息!','0',2)
;


/*==============================================================*/
/* Table: EASI_ROLE_RES                                         */
/*==============================================================*/
create table EASI_ROLE_RES
(
   ROLE_ID              varchar(32) not null comment '角色ID',
   RES_ID               varchar(100) not null comment '资源ID，全局唯一，由应用本身定义',
   primary key (ROLE_ID, RES_ID)
);

/*==============================================================*/
/* Table: EASI_ROLE_USER                                        */
/*==============================================================*/
create table EASI_ROLE_USER
(
   ROLE_ID              varchar(32) not null comment '角色ID',
   USER_ID              varchar(32) not null comment '用户账号，全局唯一',
   CREATE_TIME          VARCHAR(19),
   primary key (ROLE_ID, USER_ID)
);

insert into EASI_ROLE_USER(ROLE_ID,USER_ID) values ('SYS_SUPER_USER','9432e4c15d464bfcbb8078f28187f17e')
;


create table EASI_USER
(
   USER_ID              varchar(32) not null comment '用户账号，全局唯一',
   USERNAME             varchar(20) not null comment '用户名',
   ROOT_ID              varchar(30) comment '组织ID',
   NIKE_NAME            varchar(20) comment '昵称',
   SEX                  varchar(10) comment '性别：男 女  保密',
   EDU                  varchar(10) comment '学历',
   BORN                 varchar(10) comment '出生年月，格式：yyyy-mm-dd',
   NATION               varchar(20)  comment '民族',
   MAJOR                varchar(20) comment '专业',
   UNIVERSITY           varchar(50) comment '毕业院校',
   OFFICE               varchar(50) comment '办公电话',
   MOBILE               varchar(50) comment '手机',
   EMAIL                varchar(50) comment 'EMAIL',
   ADDR                 varchar(200) comment '地址',
   IDCARD               varchar(20) comment '身份证',
   POST_ID              varchar(32) comment '岗位ID',
   EMAIL_SIGN           int default 0 comment 'EMAIL通知标志， 1：通知 0：不通知',
   SMS_SIGN             int default 0 comment '短信通知标志，1：通知  0：不通知',
   STATE                int default 0 comment '用户状态，0：正常 1：暂停 2：锁定 9：删除',
   IP_RANGE             varchar(200),
   PIC_URL              varchar(100) comment '个人图片保存路径',
   LOGIN_TIME           varchar(20),
   LOGIN_IP             varchar(20),
   PLAFORM              varchar(50) comment 'JSON格式,格式：{PC:1,MOBILE:1}',
   IMEI                 VARCHAR(50) comment '终端标识',
   QRCODE_URL           VARCHAR(50) comment '个人二维码',
   DEPTS                varchar(100) comment '所属部门，描述当前用户所在的组织架构位置',
   ROLES                varchar(100) comment '系统冗余，用于显示不用维护，系统自动根据用户角色的关系进行填写。',
   DESCRIPTION          varchar(500) comment '用户备注',
   DATA1                varchar(100) comment '保留字段1',
   DATA2                varchar(100) comment '保留字段2',
   DATA3                varchar(100) comment '保留字段3',
   DATA4                varchar(100) comment '保留字段4',
   DATA5                varchar(100) comment '保留字段5',
   DATA6                varchar(100) comment '保留字段6',
   DATA7                varchar(100) comment '保留字段7',
   DATA8                varchar(100) comment '保留字段8',
   DATA9                varchar(100) comment '保留字段9',
   DATA10               varchar(100) comment '保留字段10',
   APP_ID               varchar(100),
   OPEN_ID              varchar(100),
   CREATE_TIME          varchar(19),
   CREATE_MAN           varchar(50),
   UPDATE_TIME          varchar(19),
   UPDATE_MAN           varchar(50),
   OWNER_DEPT_ID        varchar(32),
   primary key (USER_ID)
);

/*==============================================================*/
/* Index: IDX_EASI_USER_1                                       */
/*==============================================================*/
create index IDX_EASI_USER_1 on EASI_USER
(
   OPEN_ID,
   APP_ID
);

/*==============================================================*/
/* Index: IDX_EASI_USER_2                                       */
/*==============================================================*/
create index IDX_EASI_USER_2 on EASI_USER
(
   USERNAME
);


insert  into EASI_USER(USER_ID,USERNAME) values('9432e4c15d464bfcbb8078f28187f17e','系统管理员')
;



/*==============================================================*/
/* Table: EASI_USER_LOGIN                                       */
/*==============================================================*/
create table EASI_USER_LOGIN
(
   USER_ID              varchar(32) comment '用户账号，全局唯一',
   USER_ACCT            varchar(50) not null,
   USER_PWD             varchar(32) comment 'MD5加密',
   LOGIN_TYPE           int not null comment '1：系统账号（人工设定）
            2：手机号码
            3：邮箱
            4：微信扫描登陆',
   ACCT_STATE           int comment '0：正常  1：暂停使用',
   LOCK_STATE           int comment '0：正常  1：已锁定',
   LAST_LOGIN_TIME      varchar(19),
   TRY_LOGIN_TIMES      INT,
   LOGIN_IP_ADDR        varchar(200),
   primary key (USER_ACCT, LOGIN_TYPE)
);


/*==============================================================*/
/* Table: EASI_USER_LOGIN_SECURITY                                       */
/*==============================================================*/
create table EASI_USER_LOGIN_SECURITY
(
   USER_ACCT                varchar(50) comment '用户账号',
   HISTORY_PWDS             varchar(500) comment '修改过的密码记录',
   LOGIN_TIMES      		INT comment '用户账号，全局唯一',
   LAST_UPDATE_PWD_TIME     varchar(19) comment '用户账号，全局唯一',
   LOGIN_FAILED_TIME        varchar(19) comment '最后一次登录失败时间',
   LOGIN_FAILED_COUNT       INT comment '账号连续登录错误次数',
   LOGIN_LOCK      			INT comment '锁住:1,解锁：0',
   LOCK_TIME        		varchar(19) comment '账号被锁时间',
   LOGIN_MSG        		varchar(255) comment '提醒消息',
   UPDATE_TIME        		varchar(19) comment '更新时间',
   primary key (USER_ACCT)
);


/*==============================================================*/
/* Index: IDX_EASI_USER_LOGIN_1                                 */
/*==============================================================*/
create index IDX_EASI_USER_LOGIN_1 on EASI_USER_LOGIN
(
   USER_ID
);

/*==============================================================*/
/* pwd : admin#mars                                 */
/*==============================================================*/

insert into EASI_USER_LOGIN(USER_ID,USER_ACCT,USER_PWD,LOGIN_TYPE,ACCT_STATE,LOCK_STATE)   values('9432e4c15d464bfcbb8078f28187f17e','admin','21232F297A57A5A743894A0E4A801FC3',1,0,0)
;



/*==============================================================*/
/* Table: EASI_USER_RES                                         */
/*==============================================================*/
create table EASI_USER_RES
(
   USER_ID              varchar(32) not null comment '用户账号，全局唯一',
    RES_ID               varchar(100) not null comment '资源ID，全局唯一，由应用本身定义',
  primary key (USER_ID, RES_ID)
);


create table EASI_DEPT_RES
(
   DEPT_ID              varchar(32) not null comment '部门ID，唯一',
   RES_ID               varchar(100) not null comment '资源ID，全局唯一，由应用本身定义',
   primary key (DEPT_ID, RES_ID)
);



/*==============================================================*/
/* Table: EASI_FILES                                            */
/*==============================================================*/
create table EASI_FILES
(
   FILE_ID              varchar(32) not null comment '文件ID',
   FS_TYPE              INT comment '1：本地存储  2:阿里云OSS存储',
   GROUP_ID             varchar(32) not null comment '分组，0：不指定分组下的缺省分组 其它：代表其他的应用分组',
   FILE_NAME            varchar(255) comment '文件名',
   FILE_TYPE            varchar(50) comment '文件类型，根据文件的后缀进行判定',
   FILE_SIZE            numeric(12) comment '文件大小',
   FILE_MD5             varchar(50) comment '文件MD5签名',
   CONTENT_TYPE         varchar(255) comment 'CONTENT_TYPE，主要用于HTTP的头部分对文件的类型描述',
   FILE_URI             varchar(255) comment '文件访问URL',
   LOGIN_ACCESS         INT comment '1：是 必须登录才能访问  0：可以公网访问',
   CREATOR              varchar(32) comment '创建人',
   CREATOR_NAME         varchar(50) comment '创建人姓名',
   CREATE_TIME          varchar(19) comment '创建时间',
   DATA1                varchar(255),
   DATA2                varchar(255),
   DATA3                varchar(255),
   DATA4                varchar(255),
   DATA5                varchar(255),
   primary key (FILE_ID)
);

alter table EASI_FILES comment '文件存储';

/*==============================================================*/
/* Index: IDX_EASI_FILES_1                                      */
/*==============================================================*/
create index IDX_EASI_FILES_1 on EASI_FILES
(
   CREATE_TIME
);

/*==============================================================*/
/* Index: IDX_EASI_FILES_2                                      */
/*==============================================================*/
create index IDX_EASI_FILES_2 on EASI_FILES
(
   FILE_NAME
);


/*==============================================================*/
/* Table: EASI_PC_DICT_TYPE                                     */
/*==============================================================*/
create table EASI_PC_DICT_TYPE
(
   DICT_TYPE_ID         varchar(50) not null comment '字典类型ID',
   DICT_TYPE_NAME       varchar(50) comment '字典类型名称',
   IDX_ORDER            int comment '排序',
   DICT_RES             varchar(50) comment '字典来源，sys:系统　　app:应用　user:用户自定义',
   APP_ID               varchar(50),
   primary key (DICT_TYPE_ID)
);

alter table EASI_PC_DICT_TYPE comment '字典表类型';


/*==============================================================*/
/* Table: EASI_PC_DICT                                          */
/*==============================================================*/
create table EASI_PC_DICT
(
   DICT_TYPE_ID         varchar(50) not null comment '字典类型',
   DICT_ID              varchar(50) not null comment '字典ID',
   DICT_NAME            varchar(100) comment '字典名称',
   DICT_DESC            varchar(100) comment '字典描述',
   P_DICT_ID            varchar(50) comment '父字典ID',
   IDX_ORDER            int comment '排序',
   primary key (DICT_TYPE_ID, DICT_ID)
);

alter table EASI_PC_DICT comment '字典表';


/*==============================================================*/
/* Index: IDX_EASI_PC_DICT_1                                    */
/*==============================================================*/
create unique index IDX_EASI_PC_DICT_1 on EASI_PC_DICT
(
   DICT_TYPE_ID,
   DICT_ID
);




/*==============================================================*/
/* Index: 用户资源视图                                                                                       */
/*==============================================================*/

create view V_EASI_USER_RES as 

select t2.USER_ID ,t1.RES_ID,t1.RES_NAME,t1.P_RES_ID,t1.RES_URL,t1.RES_TYPE,t1.RES_TARGET,t1.RES_STATE,
t1.IDX_ORDER,t1.APP_ID ,t1.PORTAL,t1.RES_ICON from EASI_RES t1  , EASI_USER_RES  t2 where t1.RES_ID = t2.RES_ID 

union

select distinct  t3.USER_ID ,t1.RES_ID,t1.RES_NAME,t1.P_RES_ID,t1.RES_URL,t1.RES_TYPE,t1.RES_TARGET,t1.RES_STATE,
t1.IDX_ORDER,t1.APP_ID ,t1.PORTAL,t1.RES_ICON from EASI_RES t1 ,EASI_ROLE_RES t2 , EASI_ROLE_USER t3  where t1.RES_ID = t2.RES_ID and 
t2.ROLE_ID = t3.ROLE_ID

union

select t2.USER_ID ,t1.RES_ID,t1.RES_NAME,t1.P_RES_ID,t1.RES_URL,t1.RES_TYPE,t1.RES_TARGET,t1.RES_STATE,
t1.IDX_ORDER,t1.APP_ID ,t1.PORTAL,t1.RES_ICON from EASI_RES t1  , easi_dept_user t2,easi_dept_res t3 where
t3.RES_ID=t1.RES_ID and t3.DEPT_ID=t2.DEPT_ID
;

/*==============================================================*/
/* 部门用户视图                                                                                                        */
/*==============================================================*/

create view V_EASI_DEPT_USER as 
select
	t3.DEPT_ID,t3.DEPT_CODE,t3.DEPT_NAME,t3.P_DEPT_CODE,t3.DEPT_TYPE, t2.POST_TYPE, t4.POST_TYPE_NAME,t1.USER_ID,t1.USERNAME,
	t1.STATE,t1.MOBILE,t1.ROLES ,t1.OPEN_ID,t1.APP_ID,t2.IDX_ORDER
from
	EASI_USER  t1 ,EASI_DEPT_USER  t2  left join EASI_POSI_TYPE t4 on t2.POST_TYPE = t4.POST_TYPE
  ,EASI_DEPT  t3
where
	t2.USER_ID = t1.USER_ID and t2.DEPT_ID = t3.DEPT_ID;

/*==============================================================*/
/* 用户视图                                                                                              */
/*==============================================================*/
create or replace view V_EASI_USER as
select
       t1.USER_ID,t1.USERNAME,t1.NIKE_NAME,t1.SEX,t1.EDU,t1.BORN,t1.OFFICE,t1.MOBILE,t1.EMAIL,t1.ADDR,t1.IDCARD,t1.EMAIL_SIGN,t1.SMS_SIGN,t1.STATE,t1.IP_RANGE,t1.PIC_URL,t1.LOGIN_TIME,t1.LOGIN_IP,t1.PLAFORM,t1.DEPTS,t1.ROLES,t1.DESCRIPTION,t1.DATA1,t1.DATA2,t1.DATA3,t1.DATA4,t1.DATA5,t1.DATA6,t1.DATA7,t1.DATA8,t1.DATA9,t1.DATA10,t1.APP_ID,t1.OPEN_ID,t1.CREATE_TIME,t1.CREATE_MAN,t1.QRCODE_URL,t1.IMEI,t2.USER_ACCT
from
       EASI_USER t1 left join EASI_USER_LOGIN t2 on t1.USER_ID = t2.USER_ID and t2.LOGIN_TYPE = 1;
	
	
/*==============================================================*/
/* 标签用户 视图                                                                                               */
/*==============================================================*/
create view V_EASI_LABEL_USER  as 

select  t1.LABEL_ID ,t1.LABEL_NAME ,t4.* from EASI_LABEL t1 , EASI_LABEL_USER t2 , V_EASI_USER t4   where t1.LABEL_ID = t2.LABEL_ID  and t2.USER_ID = t4.USER_ID
union 
select  t1.LABEL_ID ,t1.LABEL_NAME,t4.*  from EASI_LABEL t1 , EASI_LABEL_DEPT t2, EASI_DEPT_USER t3, V_EASI_USER t4    where t1.LABEL_ID = t2.LABEL_ID and t2.DEPT_ID = t3.DEPT_ID    and t3.USER_ID = t4.USER_ID
union 
select t1.LABEL_ID ,t1.LABEL_NAME,t4.* from EASI_LABEL t1 , EASI_LABEL_ROLE t2, EASI_ROLE_USER t3, V_EASI_USER t4    where t1.LABEL_ID = t2.LABEL_ID and t2.ROLE_ID = t3.ROLE_ID    and t3.USER_ID = t4.USER_ID

