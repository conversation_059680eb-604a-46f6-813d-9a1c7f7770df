<%@page pageEncoding="UTF-8"%>
<style type="text/css">
	.layui-table tr td,.layui-table tr th{font-size: 13px;}	
</style>
	<div>
		<blockquote class="layui-elem-quote">
			部署Mars平台应用，请上传符合Mars平台规范的应用，当前版本支持WAR和JAR。
			<a href="javascript:;" style="display: none;" onclick="appStroe();">应用商店</a>
			<a href="javascript:;" onclick="upgradeLog();" style="margin-left: 20px;color:red;">升级记录</a>
			<a href="javascript:;" onclick="warList();" style="margin-left: 20px;color:red;">War</a>
			<a href="javascript:;" onclick="jarList();" style="margin-left: 20px;color:red;">Jar</a>
			<a href="javascript:;" onclick="createProjectCode();" style="margin-left: 20px;color:red;display: none;">+创建模板项目</a>
			
		</blockquote>
	
		<fieldset class="layui-elem-field layui-field-title">
			<legend>系统应用列表</legend>
		</fieldset>
		<form id="easyform-app-list">
			<div class="layui-form" style="padding-left:15px" data-mars="app.list">
				<table class="layui-table text-l">
					<thead>
						<tr>
							<th>序号</th>
							<th>应用ID</th>
							<th>应用名称</th>
							<th>版本号</th>
							<th>WAR名称</th>
							<th>文件大小</th>
							<th>部署时间</th>
							<th>变更配置</th>
							<th>操作</th>
						</tr>
					</thead>
					<tbody id="dataList">
					</tbody>
				</table>
				<button type="button" style="margin-top:6px;margin-left:10px" class="layui-btn layui-btn-small layui-btn-normal" id="addbut" onclick="deploy('','local')">部署应用</button>
				<button type="button" style="margin-top:6px;margin-left:10px;display: none;" class="layui-btn layui-btn-small layui-btn-primary" onclick="deploy('','server')">应用市场</button>
		    	<a href="${ctxPath}/servlet/application?action=reloadAll" target="_blank" style="margin-top:6px;margin-right:10px;float: right;color: #28a3ef" >重载所有应用</a>
		    </div>
		</form>
	</div>
	<script id="list-template" type="text/x-jsrender">
				{{for list}}
							<tr>
								<td>{{:#index+1}}</td>
								<td title="点击查看历史版本" onclick="hisList('{{:APP_ID}}')">
									<a href="javascript:void(0);">{{:APP_ID}}</a>
								</td>
								<td  onclick="log('{{:APP_ID}}')">{{:APP_NAME}}</td>
								<td>{{:APP_VERSION}}</td>
 								<td title="{{:APP_FILE_PATH}}">{{:WAR_NAME}}
									<a style="float: right;margin-left:5px;display:none;" href="javascript:void(0)" onclick="downloadWar('{{:WAR_NAME}}')"><i class="layui-icon layui-icon-download-circle"></i></a>
									{{if WAR_NAME.indexOf('jar')>-1}} 
										<a style="float: right;" href="javascript:void(0)" onclick="processLook('{{:WAR_NAME}}')"><i class="layui-icon layui-icon-auz"></i></a>
									{{/if}}
								</td>
								<td>{{:WAR_SIZE}}</td>
								<td>{{:DEPLOY_TIME}}</td>
								<td>{{if LAST_EDIT_CONF_TIME!='0'}}{{:LAST_EDIT_CONF_TIME}}{{/if}}</td>
								<td>
									<a href="javascript:edit('{{:APP_ID}}');">修改</a>&nbsp;&nbsp;
									<a data-upgrade="{{:WAR_NAME}}" href="javascript:upgrade('{{:APP_ID}}');">升级</a>&nbsp;&nbsp;
									{{if CONF_COUNT>0}}<a href="javascript:config('{{:APP_ID}}');">配置({{:CONF_COUNT}})</a>{{/if}}
								</td>
							</tr>
				{{/for}}					         
	</script>
	

<script>
	$(function(){
		$("#easyform-app-list").render();
	});

	//查看历史版本
	function hisList(appId){
		popup.layerShow({type:1,title:'历史版本',offset:'auto',area:['70%','60%']},ctxPath+"/pages/application/his-list.jsp",{appId:appId});		
	}
	
	function processLook(warName){
		popup.layerShow({id:'processLook',title:'进程查询',scrollbar:false,area:['70%','400px'],url:ctxPath+'/pages/server/app-linux.jsp',data:{warName:warName}});
	}
	//修改
	function edit(appId){
		loadPage("application/modify.jsp",{appId:appId});
	}
	//部署应用
	function deploy(appId,type){
		loadPage("application/add.jsp",{appId:appId,'type':type});
	}

	//升级
    function upgrade(appId){
    	loadPage("application/upgrade.jsp",{appId:appId});
	}
	//配置
	function config(appId){
		loadPage("application/config.jsp",{appId:appId});
	}
	function log(appId){
		loadPage("logger/app-log-list.jsp",{appId:appId});
	}
	
	function appStroe(){
		loadPage("application/stroe/list.jsp",{});
	}
	
	function upgradeLog(){
		loadPage("logger/upgrade-log-list.jsp",{});
	}
	
	function warList(){
		loadPage("application/war-list.jsp",{});
	}
	
	function jarList(){
		loadPage("application/jar-list.jsp",{});
	}
	
	function downloadWar(warName){
		window.open('${ctxPath}/servlet/application?action=downloadWar&warName='+warName);
	}
	
	function createProjectCode(){
		//打模板zip包放入，创建时候先解压到临时目录，渲染数据后生成新的文件到目录，然后打包目录导出，再删除临时目录
		layer.open({title:'创建项目',content:$('#createProjectTpl'),area:['500px','400px']});
	}

	
</script>

