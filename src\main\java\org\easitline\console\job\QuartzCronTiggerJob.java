package org.easitline.console.job;

import java.util.Date;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.service.IService;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.console.vo.AppJobModel;
import org.easitline.console.vo.JobLogModel;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;

import com.alibaba.fastjson.JSONObject;

/**
 * cron调度任务类(并行)
 * <AUTHOR>  2017-05-09
 */
public class QuartzCronTiggerJob implements Job {
	
	private Logger logger = LogEngine.getLogger("easitline-job");
	private static JobManager jobManager = new JobManager();
	private static JobLogManager jobLogManager = new JobLogManager();

	@Override
	public void execute(JobExecutionContext context) throws JobExecutionException {
		
		JobKey jobKey = context.getJobDetail().getKey();
		String jobId = jobKey.getName();   //jobName为数据库表EASI_JOB_STEP的ID
		JobLogModel jobLog = null;
		Date nextFireTime = context.getNextFireTime();
		String nextTime =  EasyCalendar.newInstance(nextFireTime).getDateTime("-");
		
		try {
			AppJobModel appJob = jobManager.getJobById(jobId);
			if(null == appJob) {
				System.out.println("调度任务不存在");
				return;
			}
			
			 //设置为调度中
			jobLog = jobLogManager.setDoingStateBeforeExec(jobId); 
			
            //调用服务
			JSONObject  jsonIn = new JSONObject();
			jsonIn.put("param", "test");
			IService service = ServiceContext.getService(appJob.getServiceId());
			JSONObject  result = service.invoke(jsonIn);
			System.out.println("调用成功，服务返回结果：" + result);
			
			//执行子任务
			/*String dependJobs = appJob.getDependJobs();
			if(!StringUtils.isBlank(dependJobs)) {
				String[] jobAry = dependJobs.split(",");
				for(String serviceId:jobAry) {
					 service = ServiceContext.getService(serviceId);
					 result = service.invoke(jsonIn);
					 System.out.println("调用成功，服务返回结果：" + result);
				}
			}*/

			//更新状态为成功
			jobLogManager.updateSuccessState(jobLog);
			
			jobManager.updateSuccessState(jobId, nextTime);
            
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("调度任务执行失败，原因：" + e.getMessage());
			
			//增加失败日志
			jobLogManager.updateFailState(jobLog, e.getMessage());
			
			jobManager.updateFailState(jobId, nextTime);
		} 
	}

}
