package org.easitline.console.servlet;
import javax.servlet.annotation.WebServlet;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.console.base.ConsoleBaseServlet;
import org.easitline.console.service.ConfigCryptorService;
import org.easitline.console.utils.AesUtils;
import com.alibaba.fastjson.JSONObject;
import java.sql.SQLException;

/*
import org.easitline.common.es.ESManager;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import org.easitline.common.es.exception.ElasticsearchOperationException;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestClient;
import com.alibaba.fastjson.JSON;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.core.InfoResponse;
import co.elastic.clients.elasticsearch.indices.GetIndexResponse;
import co.elastic.clients.elasticsearch.indices.IndexState;
*/

@WebServlet("/servlet/esConf/*")
public class EsConfServlet extends ConsoleBaseServlet {

	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForGetConf(){
		try {
			String v1 = getConsoleQuery().queryForString("select CONF_VALUE from EASI_CONF where CONF_KEY = ?", "ES_URL");
			String v2 = getConsoleQuery().queryForString("select CONF_VALUE from EASI_CONF where CONF_KEY = ?", "ES_USER");
			String v3 = getConsoleQuery().queryForString("select CONF_VALUE from EASI_CONF where CONF_KEY = ?", "ES_PASS");
			String v4 = getConsoleQuery().queryForString("select CONF_VALUE from EASI_CONF where CONF_KEY = ?", "ES_CONFIG");
			JSONObject object = new JSONObject();
			object.put("ES_URL",ConfigCryptorService.decryptString(v1, "GV"));
			object.put("ES_USER",ConfigCryptorService.decryptString(v2, "GV"));
			object.put("ES_PASS", ConfigCryptorService.decryptString(v3, "GV"));
			object.put("ES_CONFIG",ConfigCryptorService.decryptString(v4, "GV"));
			return EasyResult.ok(AesUtils.getInstance().encrypt(object.toJSONString()));
		} catch (SQLException e) {
			return EasyResult.fail();
		}
	}
	public EasyResult actionForSaveConf(){
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		JSONObject params = getJSONObject();
		String data = params.getString("encryptStr");
		data = AesUtils.getInstance().decrypt(data);
		JSONObject jsonObject = JsonKit.getJSONObject(data,null);
		
		EasyRecord record=new EasyRecord("EASI_CONF","CONF_KEY");
		record.set("CAN_DELETE", 3);
		record.set("CONF_KEY", "ES_URL");
		record.set("CONF_VALUE",jsonObject.getString("ES_URL"));
		try {
			if(getConsoleQuery().queryForExist("select count(1) from EASI_CONF where CONF_KEY = ?", "ES_URL")){
				getConsoleQuery().update(record);
			}else{
				getConsoleQuery().save(record);
			}
			record.set("CAN_DELETE", 3);
			record.set("CONF_KEY", "ES_USER");
			record.set("CONF_VALUE",jsonObject.getString("ES_USER"));
			
			if(getConsoleQuery().queryForExist("select count(1) from EASI_CONF where CONF_KEY = ?", "ES_USER")){
				getConsoleQuery().update(record);
			}else{
				getConsoleQuery().save(record);
			}
			
			record.set("CAN_DELETE", 3);
			record.set("CONF_KEY", "ES_PASS");
			record.set("CONF_VALUE",ConfigCryptorService.encryptString(jsonObject.getString("ES_PASS"), "GV"));
			
			if(getConsoleQuery().queryForExist("select count(1) from EASI_CONF where CONF_KEY = ?", "ES_PASS")){
				getConsoleQuery().update(record);
			}else{
				getConsoleQuery().save(record);
			}
			
			record.set("CAN_DELETE", 3);
			record.set("CONF_KEY", "ES_CONFIG");
			record.set("CONF_VALUE",jsonObject.getString("ES_CONFIG"));
			
			if(getConsoleQuery().queryForExist("select count(1) from EASI_CONF where CONF_KEY = ?", "ES_CONFIG")){
				getConsoleQuery().update(record);
			}else{
				getConsoleQuery().save(record);
			}
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		this.addOperateLog("修改ES配置参数",data);
		ServerContext.reload();
		return EasyResult.ok();
	}
	
	/*
	public EasyResult actionForEsInfo(){
		try {
			ElasticsearchClient client = ESManager.getInstance().getClient();
			InfoResponse response = client.info();
			return EasyResult.ok(response.toString());
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public EasyResult actionForEsReload(){
		 try {
			 ESManager.getInstance().reload();
			return EasyResult.ok();
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	private JSONObject getNodesStats() throws Exception {
	    try {
	    	RestClient restClient = ESManager.getInstance().getRestClient();
	        Request request = new Request("GET", "/_nodes/stats");
	        Response response = restClient.performRequest(request);
	        // 读取响应内容
	        StringBuilder sb = new StringBuilder();
	        try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), StandardCharsets.UTF_8))) {
	            String line;
	            while ((line = reader.readLine()) != null) {
	                sb.append(line);
	            }
	        }
	        return JSON.parseObject(sb.toString());
	    } catch (IOException e) {
	        this.error("获取节点统计信息失败", e);
	        throw new ElasticsearchOperationException("获取节点统计信息失败", e);
	    }
	}
	
	public EasyResult actionForGetNodesStats(){
		try {
			return EasyResult.ok(getNodesStats());
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public EasyResult actionForEsTest(){
		try {
			ElasticsearchClient client = ESManager.getInstance().getClient();
			InfoResponse response = client.info();
			this.getLogger().info(response);
			return EasyResult.ok();
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public EasyResult actionForAllIndices(){
		try {
			return EasyResult.ok(getAllIndices());
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public EasyResult actionForGetIndexInfo(){
		try {
			return EasyResult.ok(getIndexInfo(getPara("indexName")));
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	private String getAllIndices() {
	   try {
	       // 使用 _cat/indices API 获取所有索引
	       String response = ESManager.getInstance().getClient().cat().indices().toString();
	       // 解析响应并返回索引名称列表
	       return response;
	   } catch (IOException e) {
	       this.error("获取所有索引失败", e);
	       throw new ElasticsearchOperationException("获取所有索引失败", e);
	   }
	}
	
	private String getIndexInfo(String indexName) {
	   try {
	       // 使用 GET 索引名称 API 获取索引信息
	       GetIndexResponse response = ESManager.getInstance().getClient().indices().get(i -> i.index(indexName));
	       // 获取索引状态
	       IndexState indexState = response.get(indexName);
	       // 构建索引信息的JSON
	       Map<String, Object> indexInfo = new HashMap<>();
	       indexInfo.put("index", indexName);
	       indexInfo.put("number_of_shards", indexState.settings().numberOfShards()); // 获取分片数
	       indexInfo.put("number_of_replicas", indexState.settings().numberOfReplicas()); // 获取副本数
	       indexInfo.put("mappings", indexState.mappings()); // 获取映射
	       return JSON.toJSONString(indexInfo);
	   } catch (IOException e) {
	       this.error("获取索引信息失败: " + indexName, e);
	       throw new ElasticsearchOperationException("获取索引信息失败", e);
	   }
	}
	*/
	
	@Override
	protected String getResId() {
		return null;
	}

}
