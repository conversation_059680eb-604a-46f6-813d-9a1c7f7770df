<%@page pageEncoding="UTF-8"%>
<style>
	.layui-textarea {
    min-height: 100px;
    height: auto;
    line-height: 20px;
    padding: 6px 10px;
    resize: vertical;
    border-width: 1px;
    border-style: solid;
    background-color: #fff;
    border-radius: 2px;
</style>
	<div>
		<form id="easyform-app-his-list">
			<input type="hidden" name="appId" value="${param.appId}"/>
			<div style="padding:0 15px" id="appHisList">
				<table class="layui-table text-center" data-mars="app.hisList" data-container="#histData" data-template="hisListTemp">
					<thead>
						<tr>
							<th>序号</th>
							<th>应用名称</th>
							<th>版本号</th>
							<th>WAR名称</th>
							<th>WAR大小</th>
							<th>版本描述</th>
							<th>部署时间</th>
							<th>操作</th>
						</tr>
					</thead>
					<tbody id="histData">
					</tbody>
					<script id="hisListTemp" type="text/x-jsrender">
					{{for list}}
							<tr>
								<td>{{:#index+1}}</td>
								<td>{{:APP_NAME}}</td>
								<td>{{:APP_VERSION}}</td>
								<td>{{:WAR_NAME}}</td>
								<td>{{:WAR_SIZE}}</td>
								<td title="{{:APP_VERSION_DESC}}">
										<a href="javascript:void(0)" onclick="showDescContent($(this));">{{call:APP_VERSION_DESC fn='showDesc'}}</a>
										<div style="display:none"><textarea style="height:250px" class="layui-textarea">{{:APP_VERSION_DESC}}</textarea></div>
								</td>
								<td>{{:DEPLOY_TIME}}</td>
								<td>
									<a href="javascript:switchVersion('{{:ID}}');">切换版本</a>
								</td>
							</tr>
					{{/for}}					         
					</script>
				</table>
		    </div>
		</form>
	</div>

<script>
	$(function(){
		$("#easyform-app-his-list").render();
	});
	function showDescContent(obj){
		layer.open({area:['500px','400px'],content:obj.next().html()});
	}
	//切换版本
	function switchVersion(id){
		layer.confirm("确定切换此版本吗？",function(confIndex){
			layer.close(confIndex);
    		ajax.remoteCall("${ctxPath}/servlet/application?action=switchVersion",{id:id},function(result) { 
    			layer.msg(result.msg,{icon: 1,time:800},function(){
					layer.closeAll();
					loadPage("application/list.jsp",{});
				});
   			});
		});
	}
	function showDesc(str){
		if(str==''){return ''};
		return cutText(str,50);
	}
</script>

