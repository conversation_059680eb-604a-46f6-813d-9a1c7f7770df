<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
     <meta charset="utf-8">
     <meta name="viewport" content="width=device-width, initial-scale=1.0">
  	 <meta name="renderer" content="webkit|ie-comp|ie-stand">
     <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
     <title>Console登录认证</title>
	 <LINK rel="Shortcut Icon" href="favicon.ico" />
     <style type="text/css">
	    article,aside,details,figcaption,figure,footer,header,hgroup,nav,section{display:block}audio,canvas,video{display:inline-block;*display:inline;*zoom:1}audio:not([controls]){display:none}html{font-size:100%;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%}a:hover,a:active{outline:0}sub,sup{position:relative;font-size:75%;line-height:0;vertical-align:baseline}sup{top:-0.5em}sub{bottom:-0.25em}img{max-width:100%;width:auto\9;height:auto;vertical-align:middle;border:0;-ms-interpolation-mode:bicubic}#map_canvas img,.google-maps img{max-width:none}button,input,select,textarea{margin:0;font-size:100%;vertical-align:middle}button,input{*overflow:visible;line-height:normal}button::-moz-focus-inner,input::-moz-focus-inner{padding:0;border:0}button,html input[type="button"],input[type="reset"],input[type="submit"]{-webkit-appearance:button;cursor:pointer}label,select,button,input[type="button"],input[type="reset"],input[type="submit"],input[type="radio"],input[type="checkbox"]{cursor:pointer}input[type="search"]{-webkit-appearance:textfield}input[type="search"]::-webkit-search-decoration,input[type="search"]::-webkit-search-cancel-button{-webkit-appearance:none}textarea{overflow:auto;vertical-align:top}@media print{*{text-shadow:none!important;color:#000!important;background:transparent!important;box-shadow:none!important}a,a:visited{text-decoration:underline}a[href]:after{content:" ("attr(href) ")"}abbr[title]:after{content:" ("attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100%!important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h2,h3{page-break-after:avoid}}.clear{clear:both}.clear:after{content:"\0020";visibility:hidden;display:block;height:0;clear:both}
		body,html{margin:0;padding:0;width:100%;height:100%;position:relative;overflow:hidden;background-color:#F5F5F5;}
		.loginpage{width:100%;height:100%;background-color:#f2f2f2;background-position:center;background-repeat:no-repeat;background-size:cover;}
		.loginpage .loginImage{width:100%;height:100%;background-color:#f2f2f2;left:0;top:0;background-position:center;background-repeat:no-repeat;background-size:cover;position:absolute;}
		.login-panel{z-index:2;margin:150px auto 0;width:340px;border-radius:4px;overflow:hidden;}
		.login-panel .login-right{display:block;}
		.login-panel .login-right .login-box .login-title{line-height:1;font-size:20px;font-weight:bold;text-align:center;letter-spacing:1px;margin-bottom:30px;color:#17a6f0;}
		.login-form .form-input-box{border:1px solid #e8e8e8;position:relative;margin-bottom:16px;background:#fff;border-radius:2px;box-sizing:border-box;}
		.login-form .form-input-box.hasIcon{padding-left:30px;}
		.login-form .form-input-box.code-input{float:left;width:210px;margin-bottom:0;}
		.login-form .form-input-box input{font-size:14px;line-height:24px;height:40px;border:0;outline:none;width:100%;box-sizing:border-box;background:none;color:#b2b2b2;display:block;padding:8px 10px;}
		.login-form .form-input-box .form-input-box-icon{position:absolute;left:0;top:0;width:30px;height:100%;background-repeat:no-repeat;background-position:13px center;}
		.login-form .form-input-box .form-input-box-icon.username {
		  background-image: url(images/icon-user.png);
		}
		.login-form .form-input-box .form-input-box-icon.code {
		  background-image: url(images/icon-code.png);
		}
		.login-form .form-input-box .form-input-box-icon.psw {
		  background-image: url(images/icon-psw.png);
		}
		
		.login-form .sms-getcode{
		    outline: none;
		    background: #ffffff;
		    font-size: 14px;
		    border-radius: 4px;
		    line-height: 24px;
		    padding: 8px 10px;
		    color: #5f5f5f;
		    display: block;
		    width: 100%;
		    border:1px solid #d2d2d2;
		}
		.login-form .sms-getcode:hover{
			color: blue;
		}
		
		.reser-user-msg{
			font-size: 24px;
			text-align: center;
			margin-top: 50px;
		}
		
		.reser-user-msg-a{
			font-size: 18px;
			text-align: center;
			margin-top: 20px;
			color: #19a6e5;
			text-decoration: none;
		}
		
		.login-form .form-submit{border:0;outline:none;background:#17a6f0;font-size:16px;border-radius: 4px;line-height:24px;padding:8px 10px;color:#fff;display:block;width:100%;letter-spacing:8px;}
		.login-form .get-code-box{float:right;width:120px;height:40px;}
		.login-form .get-code-box img{display:block;width:100%;height:100%;}
		.row-code{margin-bottom:30px;display:table;width:100%;table-layout:fixed;}
		.row-code .get-code-box,
		.row-code .code-input{display:table-cell;}
		.copy-info{font-size:14px;color:#b2b2b2;width:100%;text-align:center;position:absolute;bottom:20px;left:0;}
		@media (max-width:375px){
		  .login-panel{width:320px;}
		  .login-panel .login-form .form-input-box.code-input{width:190px;}
		}
	    body,html{
	      padding: 0;
	      min-height: 500px;
	      overflow-y: auto;
	    }
      input:-webkit-autofill, 
	  textarea:-webkit-autofill, 
	  select:-webkit-autofill { 
	       -webkit-box-shadow: 0 0 0 1000px #fff inset; 
	  }
	  input[type=text]:focus, input[type=password]:focus, textarea:focus {
	      -webkit-box-shadow: 0 0 0 1000px #fff inset; 
	  }
	 .loginpage{
		background-image: linear-gradient(to top, #dfe9f3 0%, white 100%);
	  }
    </style>
  
  </head>
  <body class="loginpage">
      <div id="browserLayer" style="position: fixed; top: 0;left: 0;right: 0;bottom: 0;background: rgba(0,0,0,.2);z-index: 10000;display: none;">
		<div style="width: 700px;margin: 50px auto;background: #fff;padding: 25px 50px 50px;border-radius: 5px;">
			<div class="alert alert-warning">
				<div style="text-align: center;">
					<h3>温馨提示</h3>
				</div>
				<div class="alert-msg">您的浏览器版本为<span id="IEVersion"></span>，推荐使用 
					 <a class="download" title="下载谷歌浏览器" href="https://pc.qq.com/detail/1/detail_2661.html" target="_blank">谷歌浏览器</a>、
					 <a class="download" title="下载360极速浏览器" href="http://chrome.360.cn/" target="_blank">360极速浏览器</a>
					 <a class="download" title="下载搜狗高速浏览器" href="http://ie.sogou.com/" target="_blank">搜狗浏览器</a>、来访问本系统。
				</div>
				<div style="text-align: right;margin-top: 20px;">
					<span id="iknownBtn" class="btn btn-primary btn-sm" style="visibility: hidden;cursor: pointer;">知道了</span>
				</div>
			</div>
		</div>
	  </div>
      <div class="copy-info">
      <p style="display: none;">${platform} - ${versionInfo} - ${profileActive}</p>
      ©2025 Easitline-v.3.5#20250321, Inc. All rights reserved. </div>
      <div id="loginpanel" class="login-panel">
          <div class="login-right">
            <div class="login-box">
              <div class="login-title">
                  <img src="images/logo2.png" alt='Easitline管理控制台'>
              </div>
              <div class="login-content">
                  <div class="content-box">
                     <form action="" class="login-form">
                        <input type="hidden" name="callbackUrl" id="callbackUrl" value="">
                          <div class="form-input-box hasIcon">
                              <div class="form-input-box-icon username"></div>
                              <input type="text" id="j_username" placeholder="请输入账号" value="" autocomplete="off" class="form-input">
                          </div>
                          <div class="form-input-box hasIcon ">
                              <div class="form-input-box-icon psw"></div>
                              <input type="password" id="j_password" placeholder="请输入密码" value="" autocomplete="off" class="form-input">
                          </div>
                          <c:if test="${secondCodeCheck=='1'}">
	                          <div class="form-input-box hasIcon">
                                  <div class="form-input-box-icon code"></div>
                                  <input type="text" placeholder="二次校验码" id="secondCode" class="form-input">
	                          </div>
                          </c:if>
                          <div class="clear row-code">
                              <div class="form-input-box code-input hasIcon">
                                  <div class="form-input-box-icon code"></div>
                                  <input type="text" placeholder="请输入验证码" id="j_imagecode" class="form-input">
                              </div>
                              <div class="get-code-box">
                                  <img id="imageCode" style="cursor: pointer;" onclick="reloadImageCode();" title="点击刷新验证码" src="${ctxPath}/captcha/random.png?st=<%=System.currentTimeMillis()%>" alt="">
                              </div>
                          </div>
                          <c:if test="${smsLoginVerify=='1'}">
	                          <div class="clear row-code">
	                              <div class="form-input-box code-input hasIcon">
	                                  <div class="form-input-box-icon code"></div>
	                                  <input type="text" placeholder="短信验证码" id="smsCode" class="form-input">
	                              </div>
	                              <div class="get-code-box">
	                                  <button type="button" id="sms-getcode" class="sms-getcode" onclick="getSmsCode(this);">获取验证码</button>
	                              </div>
	                          </div>
                          </c:if>
                          <input type="button" class="form-submit"  value="登录">
                     </form>
                  </div>

              </div>
            </div>
          </div>
      </div>
      <script src="${staticPath}/js/jquery.min.js"></script>
      <script type="text/javascript">
      	  $(function(){
      		  
      		if($('.layui-main').length>0){
  	 		  top.location.href='${ctxPath}/login';  
  	 		}
  	 		
	 		var prefixPath = '${prefixPath}';
	 		localStorage.setItem("prefixPath",prefixPath);
	 	  });
      </script>
      <script type="text/javascript" src="${staticPath}/lib/layer/layer.js"></script>
      <script src="${ctxPath}/js/login.js?v=0522"></script>
	  <script>
		var _hmt = _hmt || [];
		(function() {
		  var hm = document.createElement("script");
		  hm.src = "https://hm.baidu.com/hm.js?b9e2d180a3f5f12f1c0498f881395280";
		  var s = document.getElementsByTagName("script")[0]; 
		  s.parentNode.insertBefore(hm, s);
		})();
	</script>
  </body>
</html>