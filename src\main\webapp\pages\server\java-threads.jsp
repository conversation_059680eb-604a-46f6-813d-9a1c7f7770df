<%@page pageEncoding="UTF-8"%>
<style type="text/css">
	.layui-table td {
  	 	height: auto;
        overflow:visible;
        text-overflow:inherit;
        white-space:normal;
        word-wrap: break-word;
	}
</style>
	<div>
		<blockquote class="layui-elem-quote">
			线程6种状态：1. 新建（New），2. 可运行（Runnable），3. 阻塞（Blocked），4. 等待（Waiting），5. 计时等待（Timed Waiting），6. 终止（Terminated） <a onclick="showThreadState();" href="javascript:;">流程图</a><br>
			JDK导出线程：jstack -l pid > thread_dump.txt ，https://fastthread.io/在线分析导出的日志
		</blockquote>
		<form id="easyform-app-list">
			<div class="layui-form" style="padding-left:15px" data-mars="server.javaTheads">
				<table class="layui-table text-l">
					<thead>
						<tr>
							<th>序号</th>
							<th>线程id</th>
							<th>线程组</th>
							<th>线程名称</th>
							<th>线程类</th>
							<th>守护线程</th>
							<th>优先级</th>
							<th>状态</th>
							<th class="layui-hide">操作</th>
						</tr>
					</thead>
					<tbody id="dataList">
					</tbody>
				</table>
		    </div>
		</form>
	</div>
					<script id="list-template" type="text/x-jsrender">
						{{for list}}
							<tr>
								<td>{{:#index+1}}</td>
								<td>{{:id}}</td>
								<td style="width:80px;" title="{{:group}}">{{cutText:group 20}}</td>
								<td style="width:120px;" title="{{:name}}">{{cutText:name 50}}</td>
								<td style="max-width: 300px;">{{:class}}</td>
								<td>{{:isBackground}}</td>
								<td>{{:priority}}</td>
								<td>{{:state}}</td>
								<td class="layui-hide">
									<a href="javascript:kill('{{:id}}');">kill</a>&nbsp;&nbsp;
								</td>
							</tr>
						{{/for}}					         
					</script>


<script>
	$(function(){
		$("#easyform-app-list").render();
	});

	function kill(id){
		ajax.remoteCall("${ctxPath}/servlet/serverInfo?action=killThead",{id:id},function(result) { 	
			if(result.state==1){
				layer.msg(result.msg,{icon: 1,time:800},function(){
					location.reload();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	
	}
	
	function showThreadState(){
		layer.open({type:1,content:'<img style="max-width:100%;" src="/easitline-console/images/thread-state.png">',area:['80%','80%']});
	}
	
</script>

