<%@page pageEncoding="UTF-8"%>
<style type="text/css">
	.layui-form-label{width: 250px;}
	.layui-input-block{margin-left: 280px;}
	.layui-form-item .layui-input-inline{width: 400px;}
</style>
<div>
	<fieldset class="layui-elem-field layui-field-title">
		<legend>登录安全配置</legend>
	</fieldset>
		 <form class="layui-form" method="post" id="easyform"  autocomplete="off"> 
		  <div class="layui-form-item">
		    <label class="layui-form-label">SSO登录校验验证码</label>
		    <div class="layui-input-block">
			      <input type="radio" name="G_LOGIN_AUTH" value="true" title="是">
			      <input type="radio" name="G_LOGIN_AUTH" value="false" title="否" checked="checked">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">密码国密认证加密</label>
		    <div class="layui-input-block">
			      <input type="radio" name="sm3Pwd" value="0" title="否" checked="checked">
			      <input type="radio" name="sm3Pwd" value="1" title="是">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">开启全局登录安全校验</label>
		    <div class="layui-input-block">
			      <input type="radio" name="verifyLogin" value="true" title="是">
			      <input type="radio" name="verifyLogin" value="false" title="否" checked="checked">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">首次登录修改密码跳转</label>
		    <div class="layui-input-block">
			      <input type="radio" name="firstLoginUpdatePwd" value="true" title="是">
			      <input type="radio" name="firstLoginUpdatePwd" value="false" title="否" checked="checked">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">间隔修改密码天数</label>
		    <div class="layui-input-block">
		      <input type="number" value="0" placeholder="若填写0表示没开启此校验"  name="updatePwdLimit" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">保存历史密码次数</label>
		    <div class="layui-input-block">
		      <input type="number" value="4" name="hisPwdCount" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">最大登录操作错误次数</label>
		    <div class="layui-input-block">
		      <input type="number" lay-verify="required" value="6" placeholder="" name="maxErrorLoginCount" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">间隔多少分钟解锁账号</label>
		    <div class="layui-input-block">
		      <input type="number" lay-verify="required" value="10" placeholder="" name="unLockTime" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">间隔多少分钟连续操作</label>
		    <div class="layui-input-block">
		      <input type="number" lay-verify="required" value="10" placeholder="" name="loginTimeLimit" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <div class="layui-input-block">
		      <button type="button" lay-submit lay-filter="submitBtn" class="layui-btn layui-btn-sm">保存</button>&nbsp;&nbsp;
		    </div>
		  </div>
	</form>
</div>

<script>
	$(function(){
		layui.use('form', function(){
			getConf();
	         var layuiform = layui.form;
	         layuiform.render('select');
	         layuiform.render();
			 layuiform.on('submit(submitBtn)', function(data){
				 doExcute(data.field);
			 });
	     });
		
	});

	function getConf(){
		ajax.remoteCall("${ctxPath}/servlet/serverInfo?action=getAllConfig",{}, function(result) { 
			var data=result.data;
			if(data){
				fillRecord(data);
				layui.use('form', function(){
			         var layuiform = layui.form;
			         layuiform.render('select');
			         layuiform.render('radio');
			         layuiform.render();
			     });
			}
		});
	}

	function doExcute() {
		var data = form.getJSONObject("easyform"); 
		ajax.remoteCall("${ctxPath}/servlet/serverInfo?action=saveLoginConfig",data,function(result) { 	
			if(result.state==1){
				layer.msg(result.msg+",需重复服务生效。",{icon: 1,time:800},function(){
					layer.closeAll();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
</script>