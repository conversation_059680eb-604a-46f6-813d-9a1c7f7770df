package org.easitline.console.servlet;

import javax.servlet.annotation.WebServlet;

import org.easitline.console.base.ConsoleBaseServlet;

@WebServlet("/servlet/pages/*")
public class PagesServlet extends ConsoleBaseServlet {

	private static final long serialVersionUID = 1L;

	public void actionForLoggerTail() {
		keepPara();
		renderJsp("/pages/logger/logger-tail.jsp");
	}
	public void actionForJsonView() {
		keepPara();
		renderJsp("/pages/json/json-parser.jsp");
	}
}
