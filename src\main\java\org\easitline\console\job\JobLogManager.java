package org.easitline.console.job;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.apache.commons.lang3.StringUtils;
import org.easitline.console.base.Constants;
import org.easitline.console.vo.JobLogModel;
import org.easitline.console.vo.JobLogRowMapper;

/**
 * 调度任务日志管理类
 * <AUTHOR>  2017-05-23
 *
 */
public class JobLogManager {
	
	private Logger logger = LogEngine.getLogger("easitline-job");

	protected EasyQuery getSysDefaultQuery() {
		return EasyQuery.getQuery(Constants.DEFAULT_DS_NAME);
	}
	
	/**
	 * 执行调度任务前执行更新状态
	 * @return
	 */
	public JobLogModel setDoingStateBeforeExec(String jobId) {
				
		String dateKey = EasyCalendar.newInstance().getDateString("");
		JobLogModel jobLog = this.findJobLogByDoubleId(dateKey, jobId);
        
        if(null == jobLog) {
        	jobLog = new JobLogModel();
        	jobLog.setDateKey(dateKey);
        	jobLog.setJobId(jobId);
        	jobLog.setJobState(String.valueOf(Constants.JobExecState.RUNNING.getKey()));  //表示调度中
        	jobLog.setDateId(dateKey); //调度日期
        	jobLog.setFirstTime(EasyCalendar.newInstance().getDateTime("-"));
        	jobLog.setLastTime(EasyCalendar.newInstance().getDateTime("-"));
        	jobLog.setExecCount("0");
        	
        	this.insertJobLog(jobLog);
        } else {
        	jobLog.setJobState(String.valueOf(Constants.JobExecState.RUNNING.getKey()));  //表示调度中
        	jobLog.setLastTime(EasyCalendar.newInstance().getDateTime("-"));
        	this.updateJobLogState(jobLog);
        }
		
		return jobLog;
	}
	
	/**
	 * 更新成功状态
	 */
	public void updateSuccessState(JobLogModel jobLog) {
		
		if(null == jobLog) {
			logger.error("更新成功状态失败，传入JobLog对象为空");
			return;
		}
		
		jobLog.setJobState(String.valueOf(Constants.JobExecState.SUCCESS.getKey()));  //表示成功
		jobLog.setLastTime(EasyCalendar.newInstance().getDateTime("-"));
		this.updateJobLog(jobLog);
	}
	
	/**
	 * 更新失败状态
	 */
	public void updateFailState(JobLogModel jobLog, String errorMsg) {
		
		if(null == jobLog) {
			logger.error("更新失败状态失败，传入JobLog对象为空");
			return;
		}
		
		jobLog.setJobState(String.valueOf(Constants.JobExecState.FAIL.getKey()));  //表示失败
		jobLog.setLastTime(EasyCalendar.newInstance().getDateTime("-"));
		jobLog.setExceptionInfo(errorMsg);
		this.updateJobLog(jobLog);
	}
	
	/**
	 * 增加任务日志
	 */
	public void insertJobLog(JobLogModel jobLog) {
		
		String sql = "insert into EASI_JOB_LOG(DATE_KEY,JOB_ID,DATE_ID,JOB_STATE,FIRST_TIME,LAST_TIME,EXEC_COUNT,EXCEPTION_INFO) values(?,?,?,?,?,?,?,?)";
		Object[] params = new Object[]{jobLog.getDateKey(), jobLog.getJobId(), jobLog.getDateId(), jobLog.getJobState(), jobLog.getFirstTime(), jobLog.getLastTime(), jobLog.getExecCount(), jobLog.getExceptionInfo()};

		try {
			getSysDefaultQuery().execute(sql, params);
		} catch(Exception e) {
			//e.printStackTrace();
			logger.error("JobLogManager.insertJobLog出错，原因：" + e.getMessage());
		}
	}
	
	/**
	 * 更新任务日志 
	 * @param jobLog
	 */
	public void updateJobLog(JobLogModel jobLog) {
		String sql = "update easi_job_log set date_id=?,job_state=?,first_time=?,last_time=?,exec_count=exec_count+1,exception_info=? where date_key=? and job_id=?";
		Object[] params = new Object[]{jobLog.getDateId(), jobLog.getJobState(), jobLog.getFirstTime(),  jobLog.getLastTime(), jobLog.getExceptionInfo(), jobLog.getDateKey(), jobLog.getJobId()};
		
		try {
			getSysDefaultQuery().execute(sql, params);
		} catch(Exception e) {
			//e.printStackTrace();
			logger.error("JobLogManager.updateJobLog出错，原因：" + e.getMessage());
		}
	}
	
	/**
	 * 更新任务日志 
	 * @param jobLog
	 */
	public void updateJobLogState(JobLogModel jobLog) {
		String sql = "update easi_job_log set date_id=?,job_state=?,first_time=?,last_time=?,exception_info=? where date_key=? and job_id=?";
		Object[] params = new Object[]{jobLog.getDateId(), jobLog.getJobState(), jobLog.getFirstTime(),  jobLog.getLastTime(), jobLog.getExceptionInfo(), jobLog.getDateKey(), jobLog.getJobId()};
		try {
			getSysDefaultQuery().execute(sql, params);
		} catch(Exception e) {
			//e.printStackTrace();
			logger.error("JobLogManager.updateJobLogState出错，原因：" + e.getMessage());
		}
	}
	
	/**
	 * 根据复合主键查询任务日志 
	 * @param jobLog
	 */
	public JobLogModel findJobLogByDoubleId(String dateKey, String jobId) {
		String sql = "select t.* from easi_job_log t where date_key=? and job_id=?";
		Object[] params = new Object[]{dateKey, jobId};
		JobLogModel jobLog = null; 
		try {
			jobLog = getSysDefaultQuery().queryForRow(sql, params, new JobLogRowMapper());
		} catch(Exception e) {
			logger.error("JobLogManager.findJobLogByDoubleId出错，原因：" + e.getMessage());
		}
		return jobLog;
	}
	
	/**
	 * 删除任务日志 
	 * @param jobLog
	 */
	public void deleteJobLogListByJobId(String jobId) {
		String sql = "delete from easi_job_log where job_id=?";
		Object[] params = new Object[]{jobId};
		try {
			getSysDefaultQuery().execute(sql, params);
		} catch(Exception e) {
			logger.error("JobLogManager.deleteJobLogListByJobId出错，原因：" + e.getMessage());
		}
	}
	
	/**
	 * 查询任务日志 
	 * @param jobLog
	 */
	public List<JobLogModel> getJobLogListByJobId(String jobId) {
		Object[] params = null;
		String sql = "select t.* from easi_job_log t";
		if(!StringUtils.isBlank(jobId)){
			sql = sql+" where t.job_id = ?";
			params = new Object[]{jobId};
		}
		sql = sql + " order by last_time desc";
		List<JobLogModel> jobLogList = new ArrayList<JobLogModel>();
		try {
			EasyQuery  query = getSysDefaultQuery();
			jobLogList = query.queryForList(sql, params,1,100,new JobLogRowMapper());
		} catch(Exception e) {
			logger.error("JobLogManager.getJobLogListByJobId出错，原因：" + e.getMessage());
		}
		return jobLogList;
	}

}
