package org.easitline.console.vo;

import java.sql.ResultSet;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.utils.lang.ResultSetUtil;
import org.easitline.console.base.Constants;

public class Job<PERSON>ogRowMapper implements EasyRowMapper<JobLogModel> {

	@Override
	public JobLogModel mapRow(ResultSet rs, int rowNum) {
		
		JobLogModel vo = new JobLogModel();

		vo.setDateKey(ResultSetUtil.getString(rs, "DATE_KEY", ""));
		vo.setJobId(ResultSetUtil.getString(rs, "JOB_ID", ""));
		vo.setDateId(ResultSetUtil.getString(rs, "DATE_ID", ""));

		String jobState = ResultSetUtil.getString(rs, "JOB_STATE", "");
		if(!StringUtils.isBlank(jobState)) {
			jobState = Constants.JobExecState.getValue(Integer.valueOf(jobState));
		}
		vo.setJobState(jobState);
		
		vo.setFirstTime(ResultSetUtil.getString(rs, "FIRST_TIME", ""));
		vo.setLastTime(ResultSetUtil.getString(rs, "LAST_TIME", ""));
		vo.setExecCount(ResultSetUtil.getString(rs, "EXEC_COUNT", ""));
		vo.setExceptionInfo(ResultSetUtil.getString(rs, "EXCEPTION_INFO", ""));

		return vo;
	}

}
