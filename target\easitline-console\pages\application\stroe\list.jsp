<%@page pageEncoding="UTF-8"%>
	<div>
		<blockquote class="layui-elem-quote">
			应用管理可选择应用商店的应用进行部署。
		</blockquote>
	
		<fieldset class="layui-elem-field layui-field-title">
			<legend>应用商店列表</legend>
		</fieldset>
		<form id="easyform-app-list">
			<div class="layui-form" style="padding-left:15px" data-mars="app.stroeList">
				<table class="layui-table text-l">
					<thead>
						<tr>
							<th>序号</th>
							<th>应用ID</th>
							<th>应用名称</th>
							<th>版本号</th>
							<th>WAR名称</th>
							<th>文件大小</th>
							<th>文件修改时间</th>
							<th>更新时间</th>
							<th>版本描述</th>
						</tr>
					</thead>
					<tbody id="dataList">
					</tbody>
				</table>
				<button type="button" style="margin-top:6px;margin-left:10px" class="layui-btn layui-btn-small layui-btn-normal" id="addbut" onclick="reloadApp()">一键读取</button>
		    </div>
		</form>
	</div>
					<script id="list-template" type="text/x-jsrender">
						{{for list}}
							<tr>
								<td>{{:#index+1}}</td>
								<td>{{:APP_ID}}</td>
								<td>{{:APP_NAME}}</td>
								<td>{{:APP_VERSION}}</td>
 								<td>{{:WAR_NAME}}</td>
								<td>{{:WAR_SIZE}}</td>
								<td>{{:LAST_MODIFIED}}</td>
								<td>{{:DEPLOY_TIME}}</td>
								<td>{{:APP_VERSION_DESC}}</td>
							</tr>
						{{/for}}					         
					</script>


<script>
	$(function(){
		$("#easyform-app-list").render();
	});
	
	
	function reloadApp(){
		ajax.remoteCall("${ctxPath}/servlet/application?action=reloadAppWar",{},function(result) { 	
			if(result.state == 1){
				layer.msg(result.msg,{icon: 1,time:800},function(){
					location.reload();
				});
			}else{
				layer.alert(result.msg);
			}
		}
	);
 }

</script>

