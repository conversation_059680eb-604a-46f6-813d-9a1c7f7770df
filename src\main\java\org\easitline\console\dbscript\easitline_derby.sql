--系统版本号
create table EASI_VERSION (
   VERSION             varchar(100)                 --版本号
)


;;


--配置
create table EASI_CONF (
   CONF_KEY           VARCHAR(100)               not null,  --配置key
   CONF_VALUE         VARCHAR(1000)                 , --配置值
   CAN_DELETE         INT                 		   , -- 0:自定义可以删 ，1：默认不可以 ,2 自定义不能删除的 3配置不显示隐藏调用的
   CONF_TITLE          VARCHAR(100)                 ,  -- 配置名称
   CONF_DESC          VARCHAR(500)                 , --配置详细描述
   ORDER_INDEX        INT DEFAULT 0                ,
   PRIMARY KEY(CONF_KEY)
)

;;


--控制台用户
create table EASI_USER (
   LOGIN_ACCT           VARCHAR(100)                   ,  --登录账号
   LOGIN_PWD            VARCHAR(100)                   ,  --登录密码
   OTP_SK               VARCHAR(255)                   ,  --OPT
   OTP_STATE            INT                   		   ,  --OPT启用状态 0 不使用 1使用
   ROLE_ID              INT                            ,  --1: 管理员  2：系统监控人员   3： 系统维护人员  4：系统配置 9:日志查看
   STATE                INT                            ,   --0:正常 ，1：停用
   USER_ID              VARCHAR(100)				   ,  -- 用户ID
   MOBILE               VARCHAR(100)				   ,  -- 用户手机号码
   AUTH_CODE            VARCHAR(100)                   ,  -- 验证码
   AUTH_CODE_TIME       VARCHAR(100)                   ,  -- 验证码获取时间
   CREATE_TIME          VARCHAR(20)                    ,  --  创建时间
   LAST_LOGIN_TIME      VARCHAR(20)                    ,  --  最后登录时间
   LAST_LOGIN_IP        VARCHAR(255)                   ,  -- 最后登录IP
   LAST_PWD_CHANGE_TIME VARCHAR(20)                    ,  -- 最后修改密码时间
   PRIMARY KEY(LOGIN_ACCT)
)

;;


--数据源信息
create table EASI_DS_INFO (
   SYS_DS_NAME          VARCHAR(100)                   not null,
   DB_TYPE              VARCHAR(100)                   ,
   DB_NAME              VARCHAR(100)                   ,
   IP_ADDR              VARCHAR(500)                   ,
   IP_PORT              VARCHAR(100)                   ,
   MAX_CONN             VARCHAR(100)                   ,
   MIN_CONN             VARCHAR(100)                   ,
   USERNAME             VARCHAR(100)                   ,
   PASSWORD             VARCHAR(100)                   ,
   JDBC_URL             VARCHAR(1000)                  ,
   DS_PROPERTIES        VARCHAR(1000)                  ,
   URL_PROPERTIES       VARCHAR(255)                   ,
   CONF_TYPE            VARCHAR(20) DEFAULT 'simple'   ,
   DRIVER_NAME          VARCHAR(50)                    ,
   STATE                INT                            , --0:正常 ，1：停用
   PRIMARY KEY(SYS_DS_NAME)
)

;;

INSERT INTO EASI_DS_INFO(SYS_DS_NAME) VALUES('default-ds')

;;


--应用信息
create table EASI_APP_INFO (
   APP_ID               varchar(100)                   not null,
   APP_NAME             varchar(100)                   ,
   APP_VERSION          varchar(100)                   ,
   APP_VERSION_DESC     varchar(1000)                  ,
   WAR_NAME             varchar(100)                   ,
   DEPLOY_TIME          varchar(100)                   ,
   APP_FILE_PATH        varchar(200)                   ,
   DEPOY_DIR            varchar(200)                   ,
   LAST_MODIFIED        varchar(30)                    ,
   WAR_SIZE             varchar(30)                    ,
   LAST_APP_VERSION     varchar(100)                   ,
   PRIMARY KEY(APP_ID)
)

;;

--应用配置信息
create table EASI_APP_CONF (
   APP_ID               varchar(100)                   not null,
   ITEM_KEY             VARCHAR(100)                   ,
   ITEM_NAME            VARCHAR(100)                   ,
   ITEM_DESC            VARCHAR(500)                   ,
   JSON_STRING          VARCHAR(1000)                  ,  --用于通过JOSN来配置选择内容，如果JSON内容存在，贼ITEM_TYPE没有用
   ITEM_VALUE           VARCHAR(1000)                  ,  
   ITEM_TYPE            VARCHAR(100)                   ,  --取值：number、string、date、time、datetime
   ORDER_INDEX          INT DEFAULT 0                  ,
   PRIMARY KEY(APP_ID, ITEM_KEY)
)

;;

--应用数据源信息
create table EASI_APP_DS (
   APP_ID              VARCHAR(100)                  not null,
   DS_NAME              VARCHAR(100)                   not null,
   DS_DESC              VARCHAR(100)                   ,
   SYS_DS_NAME          VARCHAR(100)                   ,
   PRIMARY KEY(APP_ID, DS_NAME)
)

;;

--应用信息升级记录表
create table EASI_APP_INFO_DEPLOY_HIS (
   ID                   varchar(100)                   not null,
   APP_ID               varchar(100)                   ,
   APP_NAME             varchar(100)                   ,
   APP_VERSION          varchar(100)                   ,
   APP_VERSION_DESC     varchar(1000)                  ,
   WAR_NAME             varchar(100)                   ,
   DEPLOY_TIME          varchar(100)                   ,
   APP_FILE_PATH        varchar(200)                   ,
   DEPOY_DIR            varchar(200)                   ,
   LAST_MODIFIED        varchar(30)                    ,
   WAR_SIZE             varchar(30)                    ,
   LAST_APP_VERSION     varchar(100)                   ,
   PRIMARY KEY(ID)
)
;;


--应用配置历史
create table EASI_APP_CONF_HIS (
   APP_ID               VARCHAR(100)                   not null,
   APP_VERSIOIN         VARCHAR(100)                   not null,
   ITEM_KEY             VARCHAR(100)                   ,
   ITEM_NAME            VARCHAR(100)                   ,
   ITEM_DESC            VARCHAR(500)                   ,
   JSON_STRING          VARCHAR(500)                   ,
   ITEM_VALUE           VARCHAR(500)                   ,
   ITEM_TYPE            VARCHAR(100)                   ,
   BACKUP_TIME          VARCHAR(100)                   ,
   PRIMARY KEY(APP_ID, APP_VERSIOIN, ITEM_KEY)
)

;;

--应用数据源历史
create table EASI_APP_DS_HIS (
   APP_ID               VARCHAR(100)                   not null,
   APP_VERSIOIN         VARCHAR(100)                   not null,
   DS_NAME              VARCHAR(100)                   not null,
   DS_DESC              VARCHAR(100)                   ,
   SYS_DS_NAME          VARCHAR(100)                   ,
   BACKUP_TIME          VARCHAR(100)                   ,
   PRIMARY KEY(APP_ID, APP_VERSIOIN, DS_NAME)
)
;;

--应用市场
create table EASI_APP_STROE (
   APP_ID               varchar(100)                   not null,
   APP_NAME             varchar(100)                   ,
   APP_VERSION          varchar(100)                   ,
   APP_VERSION_DESC     varchar(1000)                  ,
   WAR_NAME             varchar(100)                   ,
   DEPLOY_TIME          varchar(100)                   ,
   APP_FILE_PATH        varchar(200)                   ,
   LAST_MODIFIED        varchar(30)                    ,
   WAR_SIZE             varchar(30)                    ,
   LAST_APP_VERSION     varchar(100)                   ,
   PRIMARY KEY(APP_ID)
)
;;

--登录日志表
create table EASI_LOGIN_LOG (
   LOG_ID           VARCHAR(32)      ,
   USERNAME         VARCHAR(30)	     ,
   IP         		VARCHAR(255)	 ,
   LOGIN_TIME  		VARCHAR(19)      ,
   MSG     			VARCHAR(100)     ,
   PRIMARY KEY(LOG_ID)
);;

--操作日志表
create table EASI_OPERATE_LOG (
   LOG_ID           VARCHAR(32)      ,
   USERNAME         VARCHAR(30)	     ,
   IP         		VARCHAR(255)	 ,
   OPERATE_TIME  	VARCHAR(19)      ,
   URL  			VARCHAR(500)     ,
   PARAMS     		VARCHAR(1000)    ,
   MSG     			VARCHAR(500)     ,
   PRIMARY KEY(LOG_ID)
);;

--应用日志表
create table EASI_APP_LOG (
   LOG_ID           VARCHAR(32)      ,
   CREATE_TIME  	VARCHAR(19)      ,
   DATE_ID  	    VARCHAR(10)      ,
   OPERATE_TIME  	VARCHAR(30)      ,
   OPERATE_TYPE  	VARCHAR(50)      ,
   MSG     			VARCHAR(800)     ,
   APP_ID     		VARCHAR(100)     ,
   PRIMARY KEY(LOG_ID)
);;

--服务器节点表
create table EASI_NODE (
   NODE_ID          VARCHAR(32)      ,
   NODE_NAME        VARCHAR(100)     ,
   CREATE_TIME  	VARCHAR(19)      ,
   NODE_IP  	    VARCHAR(100)     ,
   NODE_PORT  	    VARCHAR(10)      ,
   NODE_URL  	    VARCHAR(200)     ,
   NODE_KEY  	    VARCHAR(50)      ,
   REMARK     		VARCHAR(500)     ,
   PRIMARY KEY(NODE_ID)
);;


--系统调度配置
create table EASI_APP_JOB (
   JOB_ID           INT,
   APP_ID           VARCHAR(100)      not null,
   JOB_NAME         VARCHAR(100)      not null,
   JOB_ITEM         VARCHAR(100)      not null,
   JOB_BEGIN_TIME   VARCHAR(100)      ,
   JOB_END_TIME     VARCHAR(100)      ,
   JOB_EXP          VARCHAR(100)      not null,
   JOB_DESC         VARCHAR(100)      ,
   JOB_STATUS       INT               ,
   PRIMARY KEY(JOB_ID)
)
;;
--系统服务监控
create table EASI_SERVER_MONITOR (
   MONITOR_ID       VARCHAR(32)     ,
   MONITOR_NAME     VARCHAR(100)    ,
   MONITOR_IP       VARCHAR(30)     ,
   MONITOR_PORT     VARCHAR(10)     ,
   MONITOR_URL      VARCHAR(200)    ,
   LAST_TIME        VARCHAR(500)    ,
   LAST_WARN_LOG    VARCHAR(100)    ,
   MONITOR_STATUS       INT         ,
   PRIMARY KEY(MONITOR_ID)
)
;;