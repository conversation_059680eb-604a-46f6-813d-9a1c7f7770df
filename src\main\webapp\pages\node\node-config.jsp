<%@page pageEncoding="UTF-8"%>
<div>
	<blockquote class="layui-elem-quote">
		服务节点列表
	</blockquote>
	<form id="easyform-job-list">
		<div class="layui-form" style="padding-left:15px">
			<table class="layui-table text-center" data-mars="NodeDao.list">
				<thead>
					<tr>
						<th>服务名称</th>
						<th>服务访问地址</th>
						<th>IP</th>
						<th>PORT</th>
						<th>创建时间</th>
						<th>备注</th>
						<th>操作</th>
					</tr>
				</thead>
				<tbody id="dataList">
			    </tbody>
			</table>
			<button type="button" style="margin-top:6px;margin-left:10px" class="layui-btn layui-btn-small layui-btn-normal" onclick="addNode()">新增节点</button>
	    </div>
    </form>
</div>
<script id="list-template" type="text/x-jsrender">
				{{for list}}
				<tr>
					<td class="td-textcut">{{:NODE_NAME}}</td>
					<td class="td-textcut">{{:NODE_URL}}</td>
					<td class="td-textcut">{{:NODE_IP}}</td>
					<td class="td-textcut">{{:NODE_PORT}}</td>
					<td>{{:CREATE_TIME}}</td>
					<td>{{:REMARK}}</td>
					<td>
						<a target="_blank" href="javascript:;" onclick="gotoUrl('{{:NODE_URL}}');">Console</a>&nbsp;
						<a href="javascript:addNode('{{:NODE_ID}}')">edit</a>&nbsp;
					</td>
				</tr>
				{{/for}}
</script>

<script type="text/javascript">
  
	var g_securityKey = '';
	
	$(function(){
		$("#easyform-job-list").render({success:function(result){
			 g_securityKey  = result['NodeDao.list']['securityKey'];
		}});
	});
	
	function gotoUrl(url){
		window.open(url+"${ctxPath}/node/server?action=auth&key="+g_securityKey);
	}
	
	function addNode(nodeId){
		loadPage("node/node-edit.jsp",{nodeId:nodeId});
	}
	
</script>
