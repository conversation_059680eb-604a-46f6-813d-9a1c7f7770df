package org.easitline.console.monitor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.console.base.Constants;
import org.easitline.console.listener.GlobalContextListener;

import com.alibaba.druid.stat.DruidStatService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * 为了防止数据源链接被在长期不用的情况下，被防火墙切断导致数据源无法使用的问题。
 */
public class MarsDSMonitor implements Runnable {
	
	private static Logger logger = LogEngine.getLogger("easitline-console");
	
	private static  long timer = 0;
	
	public void run() {
		//延时启动
		try {
			Thread.sleep(60*1000);
		} catch (Exception ex) {
			logger.error(ex,ex);
		}
		
		while(GlobalContextListener.runState){
			//每3分钟检查一次
			if(System.currentTimeMillis() - timer < 180*1000){
				try {
					Thread.sleep(5*1000);
				} catch (Exception ex) {
					logger.error(ex,ex);
				}
				continue;
			}
			timer = System.currentTimeMillis();
			Map<String,JSONObject>  dsInfo = null;
			try {
				dsInfo = doDatasourceAlarm();
			} catch (Exception ex) {
				logger.error(ex,ex);
			}
			try {
				logger.info("[MarsDSMonitor]执行Mars数据源链接有效性检查检查...");
				this.doDatasourceTest(dsInfo);
			} catch (Exception ex) {
				logger.error(ex,ex);
			}
			
		}
		
	}
	
	/**
	 * 统计数据源的使用情况
	 * @param json
	 */
	private Map<String,JSONObject>  doDatasourceAlarm(){
		Map<String,JSONObject>  dsInfo = new HashMap<String,JSONObject>();
		DruidStatService stat = DruidStatService.getInstance();
		String resp = stat.service("/datasource.json");   //"/datasource.json"
		if(StringUtils.isBlank(resp)) {
			return dsInfo;
		}
		JSONObject respObj = JSONObject.parseObject(resp);
		JSONArray datasources = respObj.getJSONArray("Content");
		if(datasources == null){
			return dsInfo;
		}
		
		for(int i = 0 ;i<datasources.size();i++){
			JSONObject dsObj = datasources.getJSONObject(i);
			
			String Identity = dsObj.getString("Identity");  //数据源唯一标识
			
			String Name = dsObj.getString("Name");
			dsInfo.put(Name, dsObj);
			logger.info("[MarsDSMonitor]DS("+Name+") monitor info ->"+dsObj);
			String UserName = dsObj.getString("UserName");
			String URL = dsObj.getString("URL");
			
			int MaxActive = dsObj.getIntValue("MaxActive");//配置的最大连接数
			int MinIdle = dsObj.getIntValue("MinIdle");//配置最少链接数
			
			String ActivePeakTime = dsObj.getString("ActivePeakTime");  //池中连接数峰值时间
			int	   ActiveCount = dsObj.getIntValue("ActiveCount");   //当前活跃连接数
			int ActivePeak = dsObj.getIntValue("ActivePeak");    //活跃连接数峰值
			
			int PoolingCount = dsObj.getIntValue("PoolingCount"); //池中连接数
			int PoolingPeak = dsObj.getIntValue("PoolingPeak"); //池中连接数峰值
			String PoolingPeakTime = dsObj.getString("PoolingPeakTime");  //池中连接数峰值时间
			JSONObject cacheDsObj = new JSONObject();
			cacheDsObj.put("Name", Name);  //数据源名称
			cacheDsObj.put("UserName",UserName);  //用户名
			cacheDsObj.put("URL", URL);  //URL
			cacheDsObj.put("MaxActive", MaxActive);  //配置的最大连接数
			cacheDsObj.put("MinIdle", MinIdle);  //配置最少链接数
			cacheDsObj.put("ActivePeakTime", ActivePeakTime);   //池中连接数峰值时间
			cacheDsObj.put("ActiveCount", ActiveCount);  //当前活跃连接数
			cacheDsObj.put("ActivePeak", ActivePeak);   //活跃连接数峰值
			cacheDsObj.put("PoolingCount", PoolingCount);  //池中连接数
			cacheDsObj.put("PoolingPeak", PoolingPeak);   //池中连接数峰值
			cacheDsObj.put("PoolingPeakTime", PoolingPeakTime);  //池中连接数峰值时间
			//如果连接池连接数=0，则代表连接池链接数据库失败，无法创建连接。
			if(MaxActive>0){
				//如果活跃连接数==最大配置链接数，则代表可能出现性能问题。
				if(ActiveCount == MaxActive){
					MarsAlarm.ds0connAlarm(Name, MaxActive, cacheDsObj);
				}
				else{
					//如果活跃连接数/最大配置链接数>0.7，则代表可能出现性能问题。
					if(ActiveCount*1.0f/MaxActive>0.7) {
						MarsAlarm.ds70percentAlarm(Name, ActiveCount, MaxActive, cacheDsObj);
					}
				}
			}
		}
		return dsInfo;
	}
	
	
	private  void doDatasourceTest(Map<String,JSONObject>  dsInfo){
		String sql = "select * from EASI_DS_INFO";
		EasyQuery query = Constants.getDb();
		try {
			List<EasyRow> rows = query.queryForList(sql,new Object[]{});
			for(EasyRow row:rows){
				String datasourceName = row.getColumnValue("SYS_DS_NAME");
				//logger.info(row.toJSONObject());
				int minCount = Integer.parseInt(row.getColumnValue("MIN_CONN"));
				JSONObject dsObj = dsInfo.get(datasourceName);
				if(dsObj!=null){
					int	   ActiveCount = dsObj.getIntValue("ActiveCount");   //当前活跃连接数
					//如果当前连接池存在活跃连接数，则只需要检查一次即可，否则需要全部检测。
					if(ActiveCount>0) minCount = 1;
					else{
						int PoolingCount = dsObj.getIntValue("PoolingCount"); //池中连接数
						if(PoolingCount>0) minCount = PoolingCount;
					}
				}
				if(!testConnection(datasourceName,minCount)){
					JSONObject json = new JSONObject();
					json.put("原因", "尝试执行数据源SQL查询测试失败");
					MarsAlarm.dsFailAlarm(datasourceName, json);
				}
			}
		} catch (Exception ex) {
			logger.error(ex,ex);
		}
	}
	
	
	private static boolean testConnection(String sysDatasourceName,int minCount){
		try {
			EasyQuery query =  EasyQuery.getQuery(sysDatasourceName);
			query.setTimeout(1);
			String sql  = "";
			if(DBTypes.ORACLE.equals(query.getTypes())){
				sql = "SELECT 1 FROM DUAL";
			}else{
				sql =  "SELECT 1 ";
			}
			logger.info("[MarsDSMonitor]testConnection(dsName:"+sysDatasourceName+",minCount:"+minCount+")->"+sql);
			long timer = System.currentTimeMillis();
			for(int i = 0 ;i< minCount ;i++){
				query.queryForRow(sql, new Object[]{});
			}
			timer = System.currentTimeMillis() - timer;
			logger.info("[MarsDSMonitor]testConnection(dsName:"+sysDatasourceName+",minCount:"+minCount+") run time:"+timer+"ms");
			return true;
		} catch (Exception ex) {
			logger.info("[MarsDSMonitor]testConnection(dsName:"+sysDatasourceName+",minCount:"+minCount+") error->"+ex.getMessage());
			logger.error(ex,ex);
		}
		return false;
	}
	
}
