<?xml version="1.0" encoding="UTF-8"?>
<config>
	 <param index="0" key="ip" name="登录IP白名单（已弃用，挪到平台参数配置）" type="string" description="console允许登录ip,多个用逗号隔开,不填写不限制IP" value=""></param> 
	 <param index="1" key="updateConfMobile" name="修改配置短信号码" type="string" description="" value=""></param> 
	 <!-- <param index="2" key="updateConfSmsSwitch" name="修改配置短信校验" type="radio" items="1:开启,0:关闭"  description="" value="0"></param>  -->
	 <param index="3" key="marsMonitor" name="Mars监控" type="radio" items="1:开启,0:关闭"  description="" value="0"></param> 
	 <param index="4" key="allocAccessIp" name="console允许访问IP（已弃用，挪到平台参数配置）" type="string" description="例如：172,192,10 逗号隔开" value=""></param> 
	 <!-- <param index="4" key="pubNetLogin" name="公网登录" type="radio" items="1:开启,0:关闭"  description="目前只支持172,192,10开头识别是内网标识,若非此段请勿开启此开关" value="1"></param> --> 
	 <!-- <param key="dbType" name="默认使用哪种数据库" type="string" description="数据库:sqlite:true,derby" value="derby"></param> -->
	 <param index="5" key="loginAccountSource" name="登录账号来源" type="radio" items="0:Console,1:Mars"  description="" value="0"></param> 
	 <param index="6" key="smsLoginVerify" name="双因子短信登录" type="radio" items="0:关闭,1:开启"  description="" value="0"></param> 
	 <param index="7" key="smsLoginMustHasMobile" name="是否必须配置短信号码" type="radio" items="0:否,1:是"  description="双因子是否必须配置短信号码" value="0"></param> 
	 <param index="9" key="notAllocMultipleLogin" name="支持同时登录" type="radio" items="1:关闭,0:开启"  description="" value="0"></param> 
	 <param index="10" key="resUpdateSwitch" name="更新资源剔除字段" type="string" description="剔除更新的字段 RES_NAME,IDX_ORDER,RES_ICON,RES_URL;多个逗号隔开,0 不执行更新"  value=""></param> 
	 <param index="11" key="updateConfTime" name="升级和修改配置时间" type="string" description="示例：0-7,12-13,20-23 多个逗号隔开" value=""></param> 
	 <param index="12" key="upgradeBakWar" name="备份升级WAR文件" type="radio" items="0:是,1:否"  description="开发或测试频繁升级会导致磁盘满爆可以不备份" value="0"></param> 
	 <param index="13" key="fileTamperedCheck" name="日志文件防篡改监控" type="radio" items="0:否,1:是"  description="" value="0"></param> 
	 <param index="14" key="changePwdlimitDay" name="修改密码间隔提醒/天" type="number" description="默认是0不进行校验" value="0"></param> 
	 <param index="15" key="autoDeployDir" name="自动部署War来源目录"  type="string" description="为空默认目录easyserver/autoDeploy" value=""></param> 
	 <param index="16" key="warAutoDeploy" name="自动部署War开关" type="radio" items="0:关闭,1:开启" value="0"></param> 
</config>
