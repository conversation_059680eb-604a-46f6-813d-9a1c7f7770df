package org.easitline.console.servlet;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletResponse;

import org.easitline.common.core.Globals;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.FileKit;
import org.apache.commons.lang3.StringUtils;
import org.easitline.console.base.ConsoleBaseServlet;
import org.easitline.console.log.LogIntegrityChecker;
import org.easitline.console.service.LogBakService;
import org.easitline.console.vo.SystemLoggerModel;

import com.alibaba.fastjson.JSONObject;


@WebServlet("/servlet/logger/*")
public class LoggerServlet extends ConsoleBaseServlet {
	private static final long serialVersionUID = 1L;
	
	 private static final List<String> COMPRESSED_EXTENSIONS = Arrays.asList(".zip", ".7z", ".gz", ".rar", ".tar", ".bz2");

	 /**
	  * 格式化时间显示
	  */
	 private String formatTime(long timeMillis) {
		 return EasyDate.dateToString(new Date(timeMillis), "yyyy-MM-dd HH:mm:ss");
	 }

	 /**
	  * 计算时间间隔描述
	  */
	 private String calculateTimeInterval(long createTimeMillis) {
		 long currentTime = System.currentTimeMillis();
		 long diffMillis = currentTime - createTimeMillis;

		 if (diffMillis < 0) {
			 return "未来时间";
		 }

		 long seconds = diffMillis / 1000;
		 long minutes = seconds / 60;
		 long hours = minutes / 60;
		 long days = hours / 24;
		 long months = days / 30;
		 long years = months / 12;

		 if (years > 0) {
			 return years + "年前";
		 } else if (months > 0) {
			 return months + "个月前";
		 } else if (days > 0) {
			 return days + "天前";
		 } else if (hours > 0) {
			 return hours + "小时前";
		 } else if (minutes > 0) {
			 return minutes + "分钟前";
		 } else if (seconds > 0) {
			 return seconds + "秒前";
		 } else {
			 return "刚刚";
		 }
	 }

	 /**
	  * 获取文件创建时间 - 兼容多种文件系统
	  */
	 private long getFileCreationTime(File file) {
		 long modifyTime = file.lastModified();

		 // 检测文件系统类型，优化获取策略
		 String fsType = detectFileSystemType(file);

		 // 方法1: 根据文件系统类型优化获取创建时间
		 long createTime = getOptimizedCreationTime(file, fsType);
		 if (createTime > 0 && createTime != modifyTime) {
			 return createTime;
		 }

		 // 方法2: 尝试其他系统命令获取时间
		 createTime = getCreationTimeViaSystemCommand(file);
		 if (createTime > 0 && createTime != modifyTime) {
			 return createTime;
		 }

		 // 方法3: 使用文件的inode变化时间作为近似创建时间
		 createTime = getInodeChangeTime(file);
		 if (createTime > 0 && createTime != modifyTime) {
			 return createTime;
		 }

		 // 方法4: 从文件名中提取时间信息（仅当无法获取真实创建时间时使用）
		 long nameTime = extractTimeFromFileName(file.getName());
		 if (nameTime > 0 && nameTime != modifyTime) {
			 return nameTime;
		 }

		 // 最后fallback：返回修改时间（会被标记为"--"）
		 return modifyTime;
	 }

	 /**
	  * 使用Java NIO获取创建时间
	  */
	 private long getCreationTimeViaNIO(File file) {
		 try {
			 Path path = Paths.get(file.getAbsolutePath());
			 BasicFileAttributes attrs = Files.readAttributes(path, BasicFileAttributes.class);
			 return attrs.creationTime().toMillis();
		 } catch (Exception e) {
			 return 0;
		 }
	 }

	 /**
	  * 使用系统命令获取创建时间（Linux stat命令）
	  */
	 private long getCreationTimeViaSystemCommand(File file) {
		 try {
			 // 检查是否为Linux系统
			 String osName = System.getProperty("os.name").toLowerCase();
			 if (!osName.contains("linux")) {
				 return 0;
			 }

			 // 使用stat命令获取文件的birth time（创建时间）
			 ProcessBuilder pb = new ProcessBuilder("stat", "-c", "%W", file.getAbsolutePath());
			 Process process = pb.start();
			 process.waitFor();

			 java.io.BufferedReader reader = new java.io.BufferedReader(
				 new java.io.InputStreamReader(process.getInputStream()));
			 String result = reader.readLine();
			 reader.close();

			 if (result != null && !result.trim().equals("0")) {
				 // stat返回的是秒，需要转换为毫秒
				 return Long.parseLong(result.trim()) * 1000;
			 }
		 } catch (Exception e) {
			 // 忽略错误，继续尝试其他方法
		 }
		 return 0;
	 }

	 /**
	  * 获取inode变化时间作为近似创建时间
	  */
	 private long getInodeChangeTime(File file) {
		 try {
			 String osName = System.getProperty("os.name").toLowerCase();
			 if (!osName.contains("linux")) {
				 return 0;
			 }

			 // 使用stat命令获取ctime（inode变化时间）
			 ProcessBuilder pb = new ProcessBuilder("stat", "-c", "%Z", file.getAbsolutePath());
			 Process process = pb.start();
			 process.waitFor();

			 java.io.BufferedReader reader = new java.io.BufferedReader(
				 new java.io.InputStreamReader(process.getInputStream()));
			 String result = reader.readLine();
			 reader.close();

			 if (result != null) {
				 // stat返回的是秒，需要转换为毫秒
				 long ctimeMillis = Long.parseLong(result.trim()) * 1000;
				 // 只有当ctime早于mtime时才使用（表示文件创建早于最后修改）
				 if (ctimeMillis < file.lastModified()) {
					 return ctimeMillis;
				 }
			 }
		 } catch (Exception e) {
			 // 忽略错误
		 }
		 return 0;
	 }

	 /**
	  * 从文件名中提取时间信息（适用于包含日期的日志文件）
	  * 支持多种常见的日志文件命名格式
	  */
	 private long extractTimeFromFileName(String fileName) {
		 try {
			 // 格式1: yyyy-MM-dd (如: app.2024-01-15.log, catalina.2024-01-15.out)
			 java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("(\\d{4}-\\d{2}-\\d{2})");
			 java.util.regex.Matcher matcher = pattern.matcher(fileName);
			 if (matcher.find()) {
				 String dateStr = matcher.group(1);
				 java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd");
				 return sdf.parse(dateStr).getTime();
			 }

			 // 格式2: yyyyMMdd (如: log20240115.txt, app20240115.log)
			 pattern = java.util.regex.Pattern.compile("(\\d{8})");
			 matcher = pattern.matcher(fileName);
			 if (matcher.find()) {
				 String dateStr = matcher.group(1);
				 java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMdd");
				 return sdf.parse(dateStr).getTime();
			 }

			 // 格式3: yyyy-MM-dd-HH (如: app.2024-01-15-14.log)
			 pattern = java.util.regex.Pattern.compile("(\\d{4}-\\d{2}-\\d{2}-\\d{2})");
			 matcher = pattern.matcher(fileName);
			 if (matcher.find()) {
				 String dateStr = matcher.group(1);
				 java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd-HH");
				 return sdf.parse(dateStr).getTime();
			 }

			 // 格式4: yyyy.MM.dd (如: app.2024.01.15.log)
			 pattern = java.util.regex.Pattern.compile("(\\d{4}\\.\\d{2}\\.\\d{2})");
			 matcher = pattern.matcher(fileName);
			 if (matcher.find()) {
				 String dateStr = matcher.group(1);
				 java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy.MM.dd");
				 return sdf.parse(dateStr).getTime();
			 }

			 // 格式5: yyyy_MM_dd (如: app_2024_01_15.log)
			 pattern = java.util.regex.Pattern.compile("(\\d{4}_\\d{2}_\\d{2})");
			 matcher = pattern.matcher(fileName);
			 if (matcher.find()) {
				 String dateStr = matcher.group(1);
				 java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy_MM_dd");
				 return sdf.parse(dateStr).getTime();
			 }

			 // 格式6: yyyyMMddHH (如: app2024011514.log)
			 pattern = java.util.regex.Pattern.compile("(\\d{10})");
			 matcher = pattern.matcher(fileName);
			 if (matcher.find()) {
				 String dateStr = matcher.group(1);
				 java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMddHH");
				 return sdf.parse(dateStr).getTime();
			 }

		 } catch (Exception e) {
			 // 忽略解析错误，继续尝试其他格式
		 }
		 return 0;
	 }

	 /**
	  * 检测文件系统类型，用于优化创建时间获取策略
	  */
	 private String detectFileSystemType(File file) {
		 try {
			 String osName = System.getProperty("os.name").toLowerCase();
			 if (!osName.contains("linux")) {
				 return "unknown";
			 }

			 // 使用df命令获取文件系统类型
			 ProcessBuilder pb = new ProcessBuilder("df", "-T", file.getAbsolutePath());
			 Process process = pb.start();
			 process.waitFor();

			 java.io.BufferedReader reader = new java.io.BufferedReader(
				 new java.io.InputStreamReader(process.getInputStream()));
			 String line;
			 while ((line = reader.readLine()) != null) {
				 if (!line.startsWith("Filesystem")) {
					 String[] parts = line.trim().split("\\s+");
					 if (parts.length >= 2) {
						 String fsType = parts[1].toLowerCase();
						 reader.close();
						 return fsType;
					 }
				 }
			 }
			 reader.close();
		 } catch (Exception e) {
			 // 忽略错误
		 }
		 return "unknown";
	 }

	 /**
	  * 根据文件系统类型优化创建时间获取
	  */
	 private long getOptimizedCreationTime(File file, String fsType) {
		 // 对于支持birth time的文件系统，优先使用系统命令
		 if (fsType.contains("ext4") || fsType.contains("xfs") || fsType.contains("btrfs")) {
			 long birthTime = getCreationTimeViaSystemCommand(file);
			 if (birthTime > 0) {
				 return birthTime;
			 }
		 }

		 // 对于较老的文件系统，使用inode change time作为近似值
		 if (fsType.contains("ext2") || fsType.contains("ext3")) {
			 long ctimeMillis = getInodeChangeTime(file);
			 if (ctimeMillis > 0) {
				 return ctimeMillis;
			 }
		 }

		 // 默认使用Java NIO
		 return getCreationTimeViaNIO(file);
	 }
	 
	public EasyResult actionForBakApp() {
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		String name = getJsonPara("name");
		if(StringUtils.isBlank(name)) {
			LogBakService.getService().bakAppLogs();
		}else {
			LogBakService.getService().bakAppLogs(name);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForBakCatalina() {
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		LogBakService.getService().bakCatalina();
		return EasyResult.ok();
	}
	
	private String deployDir() {
		return System.getProperty("deployDir", Globals.WEBAPPS_DIR);
	}
	
	private File[] getAllLib() {
		File baseFile = new File(Globals.SERVER_DIR+File.separator+"lib");
		List<File> allFileJar = new ArrayList<File>();
		if(baseFile.exists()) {
			File[] baseFiles = baseFile.listFiles();
			for(File jarFile:baseFiles) {
				if(jarFile.isFile()) {
					allFileJar.add(jarFile);
				}else {
					File[] folderJars = jarFile.listFiles();
					for(File jarFile2:folderJars) {
						if(jarFile2.isFile()) {
							allFileJar.add(jarFile2);
						}
					}
				}
			}
		}
		return  allFileJar.toArray(new File[0]);
	}
	
	public EasyResult actionForLibList() throws SQLException {
		List<SystemLoggerModel> list = new ArrayList<SystemLoggerModel>();
		String orderType=getJsonPara("orderType");
		List<File> jarfiles = null;
		if("1".equals(orderType)){
			jarfiles = orderByName(getAllLib());
		}else if("2".equals(orderType)){
			jarfiles = orderByLength(getAllLib());
		}else if("3".equals(orderType)){
			jarfiles = orderByDate(getAllLib());
		}else{
			jarfiles = orderByDate(getAllLib());
		}
		long allSize = 0;
		for(File jarFile:jarfiles){
			SystemLoggerModel  jar = new SystemLoggerModel();
			String basePath = jarFile.getAbsolutePath();
			jar.setName(jarFile.getName());
			jar.setSize(jarFile.length()+"");
			jar.setBasePath(getRelativePath(basePath));
			jar.setModifyDate(new Date(jarFile.lastModified()).toLocaleString());
			list.add(jar);
			allSize = allSize + jarFile.length();
		}
		EasyResult result  = EasyResult.ok(list);
		result.put("allSize", allSize);
		return result;
	}
	
	/**
	 * 列表
	 * @param req
	 * @param resp
	 * @throws SQLException 
	 * @throws ServletException 
	 * @throws IOException
	 */
	public EasyResult actionForSystemList() throws SQLException {
		 List<SystemLoggerModel> list = new ArrayList<SystemLoggerModel>();
		 String orderType=getJsonPara("orderType");
		 String orderDirection=getJsonPara("orderDirection"); // asc/desc
		 List<File> logfiles = null;
		 if("1".equals(orderType)){
			 logfiles = orderByName(appLogDir(), orderDirection);
		 }else if("2".equals(orderType)){
			 logfiles = orderByLength(appLogDir(), orderDirection);
		 }else if("3".equals(orderType)){
			 logfiles = orderByDate(appLogDir(), orderDirection);
		 }else if("4".equals(orderType)){
			 logfiles = orderByCreateTime(appLogDir(), orderDirection);
		 }else if("5".equals(orderType)){
			 logfiles = orderByUpdateTime(appLogDir(), orderDirection);
		 }else{
			logfiles = orderByDate(appLogDir(), orderDirection);
		 }
		 long allSize = 0;
	     for(File logFile:logfiles){
	    	 SystemLoggerModel  log = new SystemLoggerModel();
	    	 String basePath = logFile.getAbsolutePath();
			 log.setBasePath(getRelativePath(basePath));
	    	 log.setName(logFile.getName());
	    	 log.setSize(logFile.length()+"");

	    	 // 获取文件时间信息
	    	 long modifyTimeMillis = logFile.lastModified();
	    	 long createTimeMillis = getFileCreationTime(logFile);

	    	 // 格式化时间字符串
	    	 String createTimeStr = formatTime(createTimeMillis);
	    	 String updateTimeStr = formatTime(modifyTimeMillis);

	    	 // 对比格式化后的时间字符串是否相同
	    	 if (createTimeStr.equals(updateTimeStr)) {
	    		 log.setCreateTime("--");
	    		 log.setCreateInterval("--");
	    		 this.debug("文件 " + logFile.getName() + " 的创建时间和更新时间字符串相同，显示为--", null);
	    	 } else {
	    		 log.setCreateTime(createTimeStr);
	    		 log.setCreateInterval(calculateTimeInterval(createTimeMillis));
	    		 this.debug("文件 " + logFile.getName() + " 有不同的创建时间: " + createTimeStr, null);
	    	 }

	    	 log.setUpdateTime(updateTimeStr);
	    	 log.setModifyDate(updateTimeStr); // 保持兼容性

	    	 list.add(log);
	    	 allSize = allSize + logFile.length();
	     }
	     EasyResult result  = EasyResult.ok(list);
		 result.put("allSize", allSize);
		 return result;
	}
	
	public EasyResult actionForBakLogList() throws SQLException {
		List<SystemLoggerModel> list = new ArrayList<SystemLoggerModel>();
		String orderType=getJsonPara("orderType");
		String orderDirection=getJsonPara("orderDirection"); // asc/desc
		String bakPath = ServerContext.getProperties("bakToDir", Globals.SERVER_DIR+File.separator+"logbak");
		List<File> logfiles = null;
		if("1".equals(orderType)){
			logfiles = orderByName(bakPath, orderDirection);
		}else if("2".equals(orderType)){
			logfiles = orderByLength(bakPath, orderDirection);
		}else if("3".equals(orderType)){
			logfiles = orderByDate(bakPath, orderDirection);
		}else if("4".equals(orderType)){
			logfiles = orderByCreateTime(bakPath, orderDirection);
		}else if("5".equals(orderType)){
			logfiles = orderByUpdateTime(bakPath, orderDirection);
		}else{
			logfiles = orderByDate(bakPath, orderDirection);
		}
		long allSize = 0;
		for(File logFile:logfiles){
			SystemLoggerModel  log = new SystemLoggerModel();
			String basePath = logFile.getAbsolutePath();
			log.setBasePath(getRelativePath(basePath));
			log.setName(logFile.getName());
			log.setSize(logFile.length()+"");

			// 获取文件时间信息
			long modifyTimeMillis = logFile.lastModified();
			long createTimeMillis = getFileCreationTime(logFile);

			// 格式化时间字符串
			String createTimeStr = formatTime(createTimeMillis);
			String updateTimeStr = formatTime(modifyTimeMillis);

			// 对比格式化后的时间字符串是否相同
			if (createTimeStr.equals(updateTimeStr)) {
				log.setCreateTime("--");
				log.setCreateInterval("--");
				this.debug("备份日志文件 " + logFile.getName() + " 的创建时间和更新时间字符串相同，显示为--", null);
			} else {
				log.setCreateTime(createTimeStr);
				log.setCreateInterval(calculateTimeInterval(createTimeMillis));
				this.debug("备份日志文件 " + logFile.getName() + " 有不同的创建时间: " + createTimeStr, null);
			}

			log.setUpdateTime(updateTimeStr);
			log.setModifyDate(updateTimeStr); // 保持兼容性

			list.add(log);
			allSize = allSize + logFile.length();
		}
		EasyResult result  = EasyResult.ok(list);
		result.put("allSize", allSize);
		return result;
	}
	
	public EasyResult actionForWarList() throws SQLException {
		List<JSONObject> appList = this.getConsoleQuery().queryForList("select * from EASI_APP_INFO",new Object[] {}, new JSONMapperImpl());
		Map<String,String> appMap= new HashMap<String,String>();
		for(JSONObject app:appList) {
			appMap.put(app.getString("WAR_NAME"), app.getString("APP_ID"));
		}
		List<SystemLoggerModel> list = new ArrayList<SystemLoggerModel>();
		String diskPath = deployDir();
		List<File> logfiles = orderByDate(diskPath);
		long allSize = 0;
		for(File logFile:logfiles){
			SystemLoggerModel  log = new SystemLoggerModel();
			String basePath = logFile.getAbsolutePath();
			log.setBasePath(getRelativePath(basePath));
			log.setName(logFile.getName());
			log.setSize(logFile.length()+"");
			log.setModifyDate(new Date(logFile.lastModified()).toLocaleString());
			if(log.getName().indexOf(".war")>-1) {
				list.add(log);
				allSize = allSize + logFile.length();
			}
			log.setData1(appMap.get(log.getName()));
		}
		EasyResult result  = EasyResult.ok(list);
		result.put("allSize", allSize);
		return result;
	}
	
	private static List<File> orderByName(String filePath) {
		return orderByName(filePath, null);
	}

	private static List<File> orderByName(String filePath, String direction) {
		File file = new File(filePath);
        File[] files = file.listFiles();
        return orderByName(files, direction);
	}
	
	@SuppressWarnings("unchecked")
	private static List<File> orderByName( File[] files) {
		return orderByName(files, null);
	}

	@SuppressWarnings("unchecked")
	private static List<File> orderByName( File[] files, String direction) {
        if(files!=null&&files.length>0) {
        	List fileList = Arrays.asList(files);
        	final boolean isDesc = "desc".equalsIgnoreCase(direction);
        	Collections.sort(fileList, new Comparator<File>() {
        		@Override
        		public int compare(File o1, File o2) {
        			if (o1.isDirectory() && o2.isFile())
        				return -1;
        			if (o1.isFile() && o2.isDirectory())
        				return 1;
        			int result = o1.getName().compareTo(o2.getName());
        			return isDesc ? -result : result;
        		}
        	});
        	List<File> list=new ArrayList<File>();
        	for (File file1 : files) {
        		list.add(file1);
        	}
        	return list;
        }else {
        	return new ArrayList<File>();
        }
    }
	
	private static List<File> orderByDate(String filePath) {
		return orderByDate(filePath, null);
	}

	private static List<File> orderByDate(String filePath, String direction) {
		File file = new File(filePath);
		File[] files = file.listFiles();
		return orderByDate(files, direction);
	}
	
	private static List<File> orderByDate(File[] files) {
		return orderByDate(files, null);
	}

	private static List<File> orderByDate(File[] files, String direction) {
	    if (files!= null && files.length > 0) {
	    	final boolean isDesc = "desc".equalsIgnoreCase(direction);
	        Arrays.sort(files, new Comparator<File>() {
	            public int compare(File f1, File f2) {
	                long diff = f1.lastModified() - f2.lastModified();
	                int result;
	                if (diff > 0)
	                    result = -1;
	                else if (diff == 0)
	                    result = 0;
	                else
	                    result = 1;
	                return isDesc ? result : -result;
	            }

	            public boolean equals(Object obj) {
	                return true;
	            }
	        });
	        List<File> list = new ArrayList<File>();
	        for (int i = 0; i < files.length; i++) {
	            list.add(files[i]);
	        }
	        return list;
	    } else {
	        return new ArrayList<File>();
	    }
	}
	
	
	private static List<File> orderByLength(String filePath) {
		return orderByLength(filePath, null);
	}

	private static List<File> orderByLength(String filePath, String direction) {
		File file = new File(filePath);
        File[] files = file.listFiles();
        return orderByLength(files, direction);
	}
	
	private static List<File> orderByLength(File[] files) {
		return orderByLength(files, null);
	}

	private static List<File> orderByLength(File[] files, String direction) {
        if(files!=null&&files.length>0) {
	        List<File> fileList = Arrays.asList(files);
	        final boolean isDesc = "desc".equalsIgnoreCase(direction);
	        Collections.sort(fileList, new Comparator<File>() {
	            public int compare(File f1, File f2) {
	                long diff = f1.length() - f2.length();
	                int result;
	                if (diff > 0)
	                    result = 1;
	                else if (diff == 0)
	                    result = 0;
	                else
	                    result = -1;
	                return isDesc ? -result : result;
	            }

	            public boolean equals(Object obj) {
	                return true;
	            }
	        });
	        List<File> list=new ArrayList<File>();
	        for (File file1 : files) {
	            if (file1.isDirectory()) continue;
	            list.add(file1);
	        }
	        return list;
        }else {
        	return new ArrayList<File>();
        }
    }

    /**
     * 按创建时间排序
     */
    private static List<File> orderByCreateTime(String filePath, String direction) {
    	File file = new File(filePath);
        File[] files = file.listFiles();
        return orderByCreateTime(files, direction);
    }

    private static List<File> orderByCreateTime(File[] files, String direction) {
        if(files!=null&&files.length>0) {
	        List<File> fileList = Arrays.asList(files);
	        final boolean isDesc = "desc".equalsIgnoreCase(direction);
	        Collections.sort(fileList, new Comparator<File>() {
	            public int compare(File f1, File f2) {
	                try {
	                    Path path1 = Paths.get(f1.getAbsolutePath());
	                    Path path2 = Paths.get(f2.getAbsolutePath());
	                    BasicFileAttributes attrs1 = Files.readAttributes(path1, BasicFileAttributes.class);
	                    BasicFileAttributes attrs2 = Files.readAttributes(path2, BasicFileAttributes.class);

	                    long createTime1 = attrs1.creationTime().toMillis();
	                    long createTime2 = attrs2.creationTime().toMillis();
	                    long modifyTime1 = f1.lastModified();
	                    long modifyTime2 = f2.lastModified();

	                    // 如果创建时间等于修改时间，则该文件排在后面（视为无效创建时间）
	                    boolean invalid1 = (createTime1 == modifyTime1);
	                    boolean invalid2 = (createTime2 == modifyTime2);

	                    if (invalid1 && invalid2) {
	                        // 两个都无效，按修改时间排序
	                        long diff = modifyTime1 - modifyTime2;
	                        int result = (diff > 0) ? -1 : (diff == 0) ? 0 : 1;
	                        return isDesc ? result : -result;
	                    } else if (invalid1) {
	                        // f1无效，f1排在后面
	                        return isDesc ? -1 : 1;
	                    } else if (invalid2) {
	                        // f2无效，f2排在后面
	                        return isDesc ? 1 : -1;
	                    } else {
	                        // 两个都有效，按创建时间排序
	                        long diff = createTime1 - createTime2;
	                        int result = (diff > 0) ? -1 : (diff == 0) ? 0 : 1;
	                        return isDesc ? result : -result;
	                    }
	                } catch (Exception e) {
	                    // 如果获取创建时间失败，使用修改时间
	                    long diff = f1.lastModified() - f2.lastModified();
	                    int result = (diff > 0) ? -1 : (diff == 0) ? 0 : 1;
	                    return isDesc ? result : -result;
	                }
	            }
	        });
	        List<File> list=new ArrayList<File>();
	        for (File file1 : files) {
	            if (file1.isDirectory()) continue;
	            list.add(file1);
	        }
	        return list;
        }else {
        	return new ArrayList<File>();
        }
    }

    /**
     * 按更新时间排序（与orderByDate相同，但为了语义清晰单独定义）
     */
    private static List<File> orderByUpdateTime(String filePath, String direction) {
    	return orderByDate(filePath, direction);
    }
	
	private void addBaseLog(List<File> logfiles) {
		File baseFile = new File(Globals.BASE_DIR);
        File[] files = baseFile.listFiles();
        if(files!=null&&files.length>0) {
        	for(File file:files) {
        		if(file.isFile()) {
        		   String name = file.getName();
        		   if(name.contains("log")) {
        			   logfiles.add(file);
        		   }
        		}
        	}
        }
	}
	
	public EasyResult actionForTomcatList() throws SQLException {
		List<SystemLoggerModel> list = new ArrayList<SystemLoggerModel>();
		String orderType=getJsonPara("orderType");
		String orderDirection=getJsonPara("orderDirection"); // asc/desc
		List<File> logfiles = null;
		if("1".equals(orderType)){
			logfiles = orderByName(systemLogDir(), orderDirection);
		}else if("2".equals(orderType)){
			logfiles = orderByLength(systemLogDir(), orderDirection);
		}else if("3".equals(orderType)){
			logfiles = orderByDate(systemLogDir(), orderDirection);
		}else if("4".equals(orderType)){
			logfiles = orderByCreateTime(systemLogDir(), orderDirection);
		}else if("5".equals(orderType)){
			logfiles = orderByUpdateTime(systemLogDir(), orderDirection);
		}else{
			logfiles = orderByDate(systemLogDir(), orderDirection);
		}
		this.addBaseLog(logfiles);

		long allSize = 0;
		for(File logFile:logfiles){
			SystemLoggerModel  log = new SystemLoggerModel();
			String basePath = logFile.getAbsolutePath();
		    log.setBasePath(getRelativePath(basePath));
			log.setName(logFile.getName());
			log.setSize(logFile.length()+"");

			// 获取文件时间信息
			long modifyTimeMillis = logFile.lastModified();
			long createTimeMillis = getFileCreationTime(logFile);

			// 格式化时间字符串
			String createTimeStr = formatTime(createTimeMillis);
			String updateTimeStr = formatTime(modifyTimeMillis);

			// 对比格式化后的时间字符串是否相同
			if (createTimeStr.equals(updateTimeStr)) {
				log.setCreateTime("--");
				log.setCreateInterval("--");
				this.debug("Tomcat日志文件 " + logFile.getName() + " 的创建时间和更新时间字符串相同，显示为--", null);
			} else {
				log.setCreateTime(createTimeStr);
				log.setCreateInterval(calculateTimeInterval(createTimeMillis));
				this.debug("Tomcat日志文件 " + logFile.getName() + " 有不同的创建时间: " + createTimeStr, null);
			}

			log.setUpdateTime(updateTimeStr);
			log.setModifyDate(updateTimeStr); // 保持兼容性

			list.add(log);
			allSize = allSize + logFile.length();
		}
		EasyResult result  = EasyResult.ok(list);
		result.put("allSize", allSize);
		return result;
	}
	
	private static String systemLogDir() {
		String path = null;
		String sysLogDir = System.getProperty("sysLogDir","");
		if(StringUtils.isNotBlank(sysLogDir)) {
			return sysLogDir;
		}
		String tongwebBase  = System.getProperty("tongweb.base","");
		if(StringUtils.isNotBlank(tongwebBase)) {
			path = tongwebBase + File.separator +"server"+File.separator+"logs";
		}else {
			path = Globals.BASE_DIR+File.separator+"logs";
		}
		return path;
	}
	
	private static String appLogDir() {
		return System.getProperty("appLogDir", Globals.LOG_DIR);
	}
	
	private static String getRelativePath(String basePath) {
		String baseDir = Globals.BASE_DIR.replace('\\', File.separatorChar);
		StringBuilder basePathBuilder = new StringBuilder(basePath);
		int startIndex = basePathBuilder.indexOf(baseDir);
		while (startIndex!= -1) {
		    basePathBuilder.delete(startIndex, startIndex + baseDir.length());
		    startIndex = basePathBuilder.indexOf(baseDir);
		}
		basePath = basePathBuilder.toString();
		return basePath;
	}
	
	 public void actionForTomcatDownload() {
        String basePath = systemLogDir() ;
        String logFileName = this.getRequest().getParameter("name");
        
        String logFilePath = basePath+File.separator+logFileName;
        try {
            boolean isTampered = LogIntegrityChecker.isFileTampered(logFilePath);
            if (!isTampered) {
                this.info("日志文件完整性验证通过：" + logFilePath,null);
                downloadLog(basePath,logFileName);
            } else {
                this.info("警告：日志文件可能被篡改：" + logFilePath,null);
                renderHtml("警告：日志文件可能被篡改：" + logFilePath);
            }
        } catch (Exception e) {
            e.printStackTrace();
            renderHtml(e.getMessage());
        }
        
     }
	 
	 private void downloadLog(String basePath,String fileName) {
	    HttpServletResponse response = this.getResponse();
        try {
            if (validateLogFileName(fileName)) {
                String path = basePath +  File.separator + fileName;
                File file = new File(path);
                if (!file.exists()) {
                	this.error("downloadLog>文件不存在>"+file.getAbsolutePath(),null);
                    renderHtml("文件不存在!");
                    return;
                }
                if(isCompressedFile(fileName)) {
                	renderFile(file, fileName, false);
                	return;
                }else {
                	// 压缩文件
                	compressAndDownloadFile(path,fileName,response);
                }
            } else {
                renderHtml("非法访问");
                return;
            }
        } catch (Exception ex) {
            error(ex.getMessage(), ex);
        }
	 }

	public static boolean isCompressedFile(String fileName) {
	     String extension = FileKit.getHouzui(fileName);
	     return COMPRESSED_EXTENSIONS.contains(extension.toLowerCase());
	}
	 
    private boolean validateLogFileName(String logFileName) {
        if (logFileName == null || logFileName.indexOf("/")!= -1 || logFileName.indexOf("..")!= -1) {
            return false;
        }
        return true;
    }

    private void compressAndDownloadFile(String path, String logFileName, HttpServletResponse response) throws IOException {
        response.setHeader("Content-disposition", "attachment;filename=" + EasyDate.getCurrentDateString("yyyyMMddHHmmss")+"-"+ logFileName + ".zip");
        OutputStream outputStream = response.getOutputStream();

        try (ZipOutputStream zipOutputStream = new ZipOutputStream(outputStream)) {
            File file = new File(path);
            FileInputStream fileInputStream = new FileInputStream(file);

            ZipEntry zipEntry = new ZipEntry(file.getName());
            zipOutputStream.putNextEntry(zipEntry);

            byte[] buffer = new byte[1024];
            int len;
            while ((len = fileInputStream.read(buffer)) > 0) {
                zipOutputStream.write(buffer, 0, len);
            }

            fileInputStream.close();
            zipOutputStream.closeEntry();
        }catch (Exception ex) {
        	this.error(ex.getMessage(), ex);
		}finally {
            outputStream.close();
        }
    }
	
	public void actionForSystemDownload(){
		String logFileName = this.getRequest().getParameter("name");
		String basePath = appLogDir();
		if(logFileName.startsWith("bak_")) {
			logFileName = logFileName.replaceAll("bak_", "");
			basePath = ServerContext.getProperties("bakToDir", Globals.SERVER_DIR+File.separator+"logbak");
		}
		String logFilePath = basePath+File.separator+logFileName;
		try {
			boolean isTampered = LogIntegrityChecker.isFileTampered(logFilePath);
			if (!isTampered) {
				this.info("日志文件完整性验证通过：" + logFilePath,null);
				downloadLog(basePath,logFileName);
			} else {
				this.info("警告：日志文件可能被篡改：" + logFilePath,null);
				renderHtml("警告：日志文件可能被篡改：" + logFilePath);
			}
		} catch (Exception e) {
			e.printStackTrace();
			renderHtml(e.getMessage());
		}
	}
	
	
	@Override
	protected String getResId() {
		return null;
	}
	
}
