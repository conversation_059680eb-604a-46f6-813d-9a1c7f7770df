package org.easitline.console.log;

import java.util.concurrent.BlockingQueue;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;
import org.easitline.console.listener.GlobalContextListener;
import org.easitline.console.mqclient.LogTopicBroker;

/**
 * Mars统一日志服务，提供把模块的日志放到solor中。
 * <AUTHOR>
 *
 */
public class LogServer implements Runnable{
	
	
	private  static BlockingQueue<String> messageQueue = new LinkedBlockingQueue<String>();
	
	private static Logger logger = LogEngine.getLogger("easitline-console","mars-log");

	@Override
	public void run() {
		
		try {
			Thread.sleep(20*1000);
		} catch (Exception ex) {
			// TODO: handle exception
		}
		
		logger.info("Start logserver[MQ:"+LogTopicBroker.BROKER_NAME+"] client ... ");
		
		while(GlobalContextListener.runState){
			try {
				String logString =  messageQueue.poll(5000, TimeUnit.MILLISECONDS);
				if(StringUtils.isNotBlank(logString)){
					LogTopicBroker.sendMessage(logString);
				}
			} catch (Exception ex) {
				logger.error(ex,ex);
				try {
					Thread.sleep(1000);
				} catch (Exception ex1) {
					logger.error(ex1,ex1);
				}
			}
		}
		LogTopicBroker.close();
	}
	
	public static  void addLog(String logString){
		logger.info("[LogServer] << "+logString);
		if(messageQueue.size()>5000){
			logger.warn("日志服务：messageQueue.size()>5000,丢弃日志,logString->"+logString);
			return;
		}
		messageQueue.add(logString);
	}

}
