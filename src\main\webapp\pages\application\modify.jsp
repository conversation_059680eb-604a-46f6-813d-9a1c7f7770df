<%@page pageEncoding="UTF-8"%>
<div>
	<blockquote class="layui-elem-quote">
		这里进行应用数据源和系统数据源对应关系的配置，如果应用有配置数据库脚本，则可以选择执行数据库初始化脚本，应用数据库脚本的执行通过应用的缺省数据源来完成！
	</blockquote>

	<fieldset class="layui-elem-field layui-field-title">
		<legend>应用管理</legend>
	</fieldset>

	<form class="layui-form" id="easyform" data-mars="app.record" data-mars-prefix="app.">
		<input name="appId" type="hidden" value="${param.appId}"/>
		  <div class="layui-form-item">
		    <label class="layui-form-label">应用名称</label>
		    <div class="layui-input-block" style="width:360px;">
		      <input type="text" name="app.APP_NAME" readonly="readonly" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">应用文件名称</label>
		    <div class="layui-input-block" style="width:360px;">
		      <input type="text" name="app.WAR_NAME" readonly="readonly" class="layui-input">
		    </div>
		  </div>
		  <div id="dataList" data-mars-top="true" data-mars="app.appDsList">
			  <script id="list-template" type="text/x-jsrender">
				{{for list}}
				<div class="layui-form-item">
		    		<label class="layui-form-label">{{if DS_NAME=="_default_ds"}}default-ds{{else}}{{:DS_NAME}}{{/if}}</label>
		    		<div class="layui-input-inline" style="margin-left:20px">
		        		<select id="{{:SYS_DS_NAME}}" name="dsKey_{{:#index+1}}" class="layui-select" data-mars="datasource.dsDict">
							<option value="">--请选择--</option>
						</select>
		    		</div>
		    		<div class="layui-form-mid layui-word-aux">{{:DS_DESC}}</div>
		  		</div>
				{{/for}}
			</script>
		  </div>
		  <div class="layui-form-item">
		    <div class="layui-input-block">
		      <button type="button" class="layui-btn layui-btn-small layui-btn-normal" onclick="saveApp()">保存修改</button>
		      <button type="button" class="layui-btn layui-btn-small layui-btn-danger" onclick="deleteApp()">卸载应用</button>
		    </div>
		  </div>
	</form>
</div>
<script>
	
	var appId = "${param.appId}";
	$(function(){
		$("#easyform").render({success:function(result){
			if(result&&result["app.appDsList"]){
				renderDs();
			}
    	}}); 
	});
	
	function renderDs(){
		$("select").render({success:function(){
			$("select").each(function(){
				var thisName = $(this)[0].id;
				$(this).val(thisName);
			});
			//开启表单渲染
			layui.use('form', function(){
			    var layuiForm = layui.form;
			    layuiForm.render('select'); //刷新select选择框渲染
			});
		}});
	}
	
	function saveApp() {
		var data = form.getJSONObject("easyform");
		ajax.remoteCall("${ctxPath}/servlet/application?action=updateApp",data,function(result) { 
			if(result.state==1){
				layer.msg(result.msg,{icon: 1,time:800},function(){
					layer.closeAll();
					loadPage("application/list.jsp",{});
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
	
	function deleteApp() {
		layer.confirm('应用将要被卸载，是否继续?',{icon: 3, title:'删除提示'},  function(index){
			layer.close(index);
			var data = form.getJSONObject("easyform");
			ajax.remoteCall("${ctxPath}/servlet/application?action=undeploy",data,function(result) { 	
				if(result.state==1){
					layer.msg(result.msg,{icon: 1,time:800},function(){
						layer.closeAll();
						loadPage("application/list.jsp",{});
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		});
	}
	
	
</script>