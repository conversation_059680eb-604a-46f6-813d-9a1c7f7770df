package org.easitline.console.base;

import org.easitline.common.core.dao.DaoContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.console.service.ConfigCryptorService;

import com.alibaba.fastjson.JSONObject;
/**
 * dao base class
 * <AUTHOR>
 */

public class AppDaoContext extends DaoContext {
	
	private EasyQuery query; 
	
	@Override
	protected void setQuery(EasyQuery query){
		this.query=query;
	}
	
	@Override
	protected String getAppDatasourceName() {
		return "default-ds";
	}

	@Override
	protected String getAppName() {
		return "easitline-console";
	}

	@Override
	protected String getLoggerName() {
		return "easitline-console";
	}

	@Override
	protected boolean loginCheck() {
		return false;
	}
	@Override
	protected EasyQuery getQuery() {
		if(query!=null){
			return query;
		}
		return Constants.getDb();
	}
	
	public EasyQuery getConsoleQuery(){
		return Constants.getDb();
	}
	
	public EasyQuery getSysDefaultQuery() {
		return EasyQuery.getQuery(getAppDatasourceName());
	}
	
	@Override
	protected JSONObject getJsonResult(Object data){
		this.getLogger().info(data);
		return super.getJsonResult(data);
	}
	
	protected boolean checkState() {
		if(!ConfigCryptorService.checkState()) {
			this.error("当前加密不可用，console不允许操作，请启动加密服务机器。", null);
			return false;
		}
		return true;
	}
}
