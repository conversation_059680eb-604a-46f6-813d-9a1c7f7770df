<%@page pageEncoding="UTF-8"%>
<style>
	.layui-form-label{width: 70px;}
	.layui-input-block{width: 90%;}
	.layui-input-block{margin-left: 100px;}
</style>
<div>
	<fieldset class="layui-elem-field layui-field-title">
		<legend>linux服务查看(<span onclick="layer.alert('top -b -n 1,pgrep java,cat,ps,free,find,tail,chmod,tar,service,cd,yum,pgrep');">仅支持cat和ps,free,netstat等命令</span>)</legend>
	</fieldset>
	<form class="layui-form" method="post" id="easyform"  autocomplete="off">
		  <div class="layui-form-item">
		    <label class="layui-form-label">shell</label>
		    <div class="layui-input-block">
		      <input type="text" name="codeKey" value="ps -ef|grep java" class="layui-input">
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <label class="layui-form-label">result</label>
		    <div class="layui-input-block">
		    	<pre  lay-height="400px" lay-title="代码" class="layui-code" lay-skin="notepad">
				 </pre>
		    </div>
		  </div>
		  <div class="layui-form-item">
		    <div class="layui-input-block">
		      <button type="button" onclick="doExcute('search')" class="layui-btn layui-btn-sm">执行</button>&nbsp;&nbsp;
		    </div>
		  </div>
	</form>
</div>

<script type="text/javascript" src="${staticPath}/lib/jquery/jquery.base64.min.js"></script>
<script>
	
	$(function(){
		doExcute();
	});
	
	function doExcute() {
		var data = form.getJSONObject("easyform"); 
		data['codeKey'] = $.base64.encode(data['codeKey']);
		ajax.remoteCall("${ctxPath}/servlet/serverInfo?action=getServerInfo",data,function(result) { 	
			if(result.state==1){
				layer.msg(result.msg,{icon: 1,time:800},function(){
					var str = result.data || '执行完毕';
					$(".layui-code").html(str);
					
					layui.use(['code'], function(){
						layui.code({about:true});
						
						}
					);
					
				});
			}else{
				layer.msg(result.msg,{icon: 5});
			}
		},{timeout:2000});
	}
	
	
</script>