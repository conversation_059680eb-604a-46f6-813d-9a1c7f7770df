<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib uri="http://www.easitline.org/easitline-taglib" prefix="EasyTag"%>
<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<title>修改密码</title>
		<LINK rel="Shortcut Icon" href="${ctxPath}/favicon.ico" />
		<link rel="stylesheet" href="${staticPath}/lib/layui/css/layui.css">
		<link rel="stylesheet" href="${ctxPath}/static/css/site.css">
		<script type="text/javascript" src="${staticPath}/js/jquery.min.js"></script> 
	</head>
<body>
<div class="layui-container" style="padding: 20px;">
	<blockquote class="layui-elem-quote">
		当前登录密码已超过${changePwdlimitDay}天没修改，请修改密码。
	</blockquote>

	<fieldset class="layui-elem-field layui-field-title">
		<legend>修改密码</legend>
	</fieldset>
	
	<form class="layui-form" autocomplete="off">
	  <div class="layui-form-item layui-hide">
	    <label class="layui-form-label">新登录帐号</label>
	    <div class="layui-input-block">
	      <input type="text" name="acct" class="layui-input" placeholder="新登录账号">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">旧密码</label>
	    <div class="layui-input-block">
	       <input type="text" name="oldpwd" lay-verify="required"  class="layui-input" placeholder="当前登录密码">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">新密码</label>
	    <div class="layui-input-block">
	      <input type="password" name="newpwd" lay-verify="required" class="layui-input" placeholder="新登录密码">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">重复新密码</label>
	    <div class="layui-input-block">
	      <input type="password" name="renewpwd" lay-verify="required"  class="layui-input" placeholder="重复新密码">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <div class="layui-input-block">
	      <button type="button" class="layui-btn layui-btn-small layui-btn-normal" lay-submit lay-filter="submitBtn" style="padding:0 20px"> 保存 </button>
	    </div>
	  </div>
	</form>
</div>

<script type="text/javascript" src="${staticPath}/lib/layer/layer.js"></script>
<script type="text/javascript" src="${staticPath}/js/jsrender.min.js"></script>
<script type="text/javascript" src="${staticPath}/lib/layui/layui.js"></script>
<script type="text/javascript" src="${staticPath}/js/easitline.core-2.0.0.js"></script>
<script type="text/javascript" src="${staticPath}/js/requreLib.js"></script>
<script type="text/javascript" src="${ctxPath}/js/aes.js"></script>

<script type="text/javascript">
	layui.use('form', function(){
		  var form = layui.form;
		  //监听提交
		  form.on('submit(submitBtn)', function(data){
			  docheck(data.field);
		  });
	});
	
   function docheck(data){
	  delete data['renewpwd'];
	  var dataStr = aesEncrypt(JSON.stringify(data));
	  ajax.remoteCall("${ctxPath}/servlet/user?action=modifyLoginInfo",{encryptStr:dataStr},function(result) { 	
			if(result.state == 1){
				layer.msg(result.msg,{icon:1,time:800},function(){
					layer.closeAll();
					location.href = "${ctxPath}/login";
				});
			}else{
				layer.alert(result.msg);
			}
		}
	 );

    }
</script>
</body>
</html>

