package org.easitline.console.log;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.easitline.common.core.Globals;
import org.easitline.common.core.log.LogEngine;
import org.easitline.console.base.Constants;

public class LogWatcher {
    private static final String LOG_NAME = Constants.EASITLINE_CONSOLE;
    private static LogWatcher instance;
    private final Map<String, DirectoryWatcher> directoryWatchers = new ConcurrentHashMap<>();
    private ScheduledExecutorService scheduler;
    
    private LogWatcher() {
        // 私有构造函数
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized LogWatcher getInstance() {
        if (instance == null) {
            instance = new LogWatcher();
        }
        return instance;
    }
    
    /**
     * 初始化日志监控
     */
    public void init() {
        try {
            // 确保不会重复初始化
            if (scheduler != null && !scheduler.isShutdown()) {
                return;
            }
            
            scheduler = Executors.newScheduledThreadPool(1);
            
            // 加载所有需要监控的目录
            loadWatchDirectories();
            
            // 每1分钟检查一次目录变化
            scheduler.scheduleAtFixedRate(this::checkAllDirectories, 0, 1, TimeUnit.MINUTES);
            
            LogEngine.getLogger(LOG_NAME).info("日志监控器已启动，监控目录：" + directoryWatchers.keySet());
        } catch (Exception e) {
            LogEngine.getLogger(LOG_NAME).error("初始化日志监控器失败", e);
        }
    }
    
    /**
     * 加载需要监控的目录
     */
    private void loadWatchDirectories() {
        // 1. 添加默认日志目录
        addWatchDirectory(Globals.LOG_DIR);
        addWatchDirectory(Globals.BASE_DIR+File.separator+"logs");
    }
    
    /**
     * 添加监控目录
     */
    public void addWatchDirectory(String directory) {
        try {
            File dir = new File(directory);
            if (!dir.exists() || !dir.isDirectory()) {
                LogEngine.getLogger(LOG_NAME).warn("目录不存在或不是有效目录：" + directory);
                return;
            }
            
            directoryWatchers.computeIfAbsent(directory, key -> {
                LogEngine.getLogger(LOG_NAME).info("添加监控目录：" + directory);
                return new DirectoryWatcher(directory);
            });
        } catch (Exception e) {
            LogEngine.getLogger(LOG_NAME).error("添加监控目录失败：" + directory, e);
        }
    }
    
    /**
     * 移除监控目录
     */
    public void removeWatchDirectory(String directory) {
        DirectoryWatcher watcher = directoryWatchers.remove(directory);
        if (watcher != null) {
            LogEngine.getLogger(LOG_NAME).info("移除监控目录：" + directory);
        }
    }
    
    /**
     * 检查所有目录
     */
    private void checkAllDirectories() {
        directoryWatchers.values().forEach(watcher -> {
            try {
                watcher.checkDirectory();
            } catch (Exception e) {
                LogEngine.getLogger(LOG_NAME).error("检查目录失败：" + watcher.getDirectory(), e);
            }
        });
    }
    
    /**
     * 停止监控
     */
    public void stop() {
        if (scheduler != null && !scheduler.isShutdown()) {
            try {
                scheduler.shutdown();
                if (!scheduler.awaitTermination(60, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
                LogEngine.getLogger(LOG_NAME).info("日志监控器已停止");
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
                LogEngine.getLogger(LOG_NAME).error("停止日志监控器时被中断", e);
            }
        }
        directoryWatchers.clear();
    }
    
    /**
     * 目录监控器内部类
     */
    private class DirectoryWatcher {
        private final String directory;
        private final Map<String, Long> fileLastModifiedTimes = new HashMap<>();
        private final Map<String, Long> fileLastSizes = new HashMap<>();
        
        public DirectoryWatcher(String directory) {
            this.directory = directory;
        }
        
        public String getDirectory() {
            return directory;
        }
        
        public void checkDirectory() {
            File dir = new File(directory);
            File[] logFiles = dir.listFiles((d, name) -> 
                name.endsWith(".log") || name.matches(".*\\.(log|out)\\.\\d+$"));

            if (logFiles == null) return;

            for (File file : logFiles) {
                processLogFile(file);
            }
        }
        
        private void processLogFile(File file) {
            String fileName = file.getName();
            long currentModifiedTime = file.lastModified();
            long currentSize = file.length();

            if (isBackupLogFile(file, currentModifiedTime)) {
                boolean isNewFile = !fileLastModifiedTimes.containsKey(fileName);
                boolean isSizeChanged = isNewFile || fileLastSizes.get(fileName) != currentSize;
                
                if (isSizeChanged) {
                    LogIntegrityChecker.saveOrUpdateLogHash(file.getAbsolutePath());
                    fileLastModifiedTimes.put(fileName, currentModifiedTime);
                    fileLastSizes.put(fileName, currentSize);
                }
            }
        }
        
        private boolean isBackupLogFile(File file, long currentModifiedTime) {
            String fileName = file.getName();
            boolean isNumberedBackup = fileName.matches(".*\\.(log|out)\\.\\d+$");
            long oneDayInMillis = TimeUnit.DAYS.toMillis(1);
            boolean isOldFile = (System.currentTimeMillis() - currentModifiedTime) > oneDayInMillis;
            return isNumberedBackup || isOldFile;
        }
    }
}
