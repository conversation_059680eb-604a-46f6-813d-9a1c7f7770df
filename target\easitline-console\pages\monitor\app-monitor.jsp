<%@ page language="java" contentType="text/html; charset=UTF-8"  pageEncoding="UTF-8"%>
<div>
	<blockquote class="layui-elem-quote">
		<a href="javascript:void(0)" onclick="getRemoteAppList()">获取本机应用</a>
		<span style="font-size:14px;color:#e02929;margin-left:10px;" id="start-tips">如启动失败，请查看日志文件logs/localhost.2025-03-20.log,catalina.2025-03-25.log（Tomcat）</span>
	 </blockquote>
	<fieldset class="layui-elem-field layui-field-title">
		<legend>Mars应用监控</legend>
	</fieldset>

	<div class="layui-form" style="padding-left:15px">
		<table class="layui-table text-center">
			<thead>
				<tr>
					<th>序号</th>
					<th>应用路径</th>
					<th>应用名称</th>
					<th>应用部署目录</th>
					<th>session超时时间（分钟）</th>
					<th>运行状态</th>
					<th>操作</th>
				</tr>
			</thead>
			<tbody id="remote-app-data"></tbody>
			<tbody id="app-data"></tbody>
		</table>
				<script id="hisListTemp" type="text/x-jsrender">
						{{for list}}
							<tr {{if !state}}style="background-color:#e9cdcd;"{{/if}}>
								<td>{{:#index+1}}</td>
								<td><a href="javascript:void(0);">{{:contextPath}}</a></td>
								<td>{{:appName}}</td>
								<td>{{:warName}}</td>
								<td>{{:sessionTimeout}}</td>
								<td>{{:stateName}}</td>
								<td>
									{{if contextPath != '/' and contextPath != '${ctxPath}'}}
										{{if !state}}
											<button type="button" class="layui-btn layui-btn-sm" onclick="todo('{{:startUrl}}','0')">启动</button>&nbsp;&nbsp;
										{{else}}
											<button type="button" class="layui-btn layui-btn-warm layui-btn-sm" onclick="todo('{{:stopUrl}}'),'0'">停止</button>&nbsp;&nbsp;
											<button type="button" class="layui-btn layui-btn-danger layui-btn-sm" onclick="todo('{{:reloadUrl}}','0')">重新加载</button>
										{{/if}}
									{{/if}}
								</td>
							</tr>
						{{/for}}					         
				</script>
				<script id="remoteAppListTemp" type="text/x-jsrender">
						{{for list}}
							<tr {{if !state}}style="background-color:#e9cdcd;"{{/if}}>
								<td>{{:#index+1}}</td>
								<td><a href="javascript:void(0);">{{:contextPath}}</a></td>
								<td>{{:appName}}</td>
								<td>{{:warName}}</td>
								<td>{{:sessionTimeout}}</td>
								<td>{{:stateName}}</td>
								<td>
									{{if contextPath != '/' and contextPath != '${ctxPath}'}}
										{{if !state}}
											<button type="button" class="layui-btn layui-btn-sm" onclick="todo('{{:startUrl}}','1')">启动</button>&nbsp;&nbsp;
										{{else}}
											<button type="button" class="layui-btn layui-btn-warm layui-btn-sm" onclick="todo('{{:stopUrl}}','1')">停止</button>&nbsp;&nbsp;
											<button type="button" class="layui-btn layui-btn-danger layui-btn-sm" onclick="todo('{{:reloadUrl}}','1')">重新加载</button>
										{{/if}}
									{{/if}}
								</td>
							</tr>
						{{/for}}					         
				</script>
	</div>	
</div>

<script>
$(function(){
	getData();

	//start-tips 自动获取今日的日期替换
	var today = new Date();
	var year = today.getFullYear();
	var month = today.getMonth() + 1;
	var day = today.getDate();
	var dateStr = year + '-' + month + '-' + day;
	$("#start-tips").text("如启动失败，请查看日志文件logs/localhost." + dateStr + ".log，logs/catalina." + dateStr + ".log（Tomcat）");	

});

function getData(){
	$.ajax({
		 dataType:'json',
		 url:"${ctxPath}/servlet/monitor/app",
		 data:{},
		 success:function(result){
			//模板渲染数据
			var jsRenderTpl = $.templates("#hisListTemp");
	       	var html = jsRenderTpl({list:result.data});
			$("#app-data").html(html);
		},beforeSend:function(result){
		},complete:function(result){
			
		}
	});
}
var _uuid = '';

function todo(url,flag){
	if(flag=='1'){
		layer.confirm('请稍后点击按钮刷新',{icon:7,time:5000,offset:'20px'},function(){
			var jxAppReqUrl = localStorage.getItem("jkAppReqUrl"); 
			url = jxAppReqUrl + url+"&uuid="+_uuid;
			window.open(url);
			return;
		});
		return;
	}
	if(!url||url.trim() == ""){
		url = "${ctxPath}/servlet/monitor/app";
	}
	var index = layer.load(1, {
	  shade: [0.1,'#fff'] //0.1透明度的白色背景
	});
	$.ajax({
		 dataType:'json',
		 url:url,
		 data:{},
		 success:function(result){
			 layer.msg("操作成功！",{icon:1},function(){
				 layer.closeAll();
				 location.reload();
			 });
		},beforeSend:function(result){
			
		},complete:function(result){
			
		},error:function(e){
			
		}
	});
}


function getRemoteAppList(){
	var jxAppReqUrl = localStorage.getItem("jkAppReqUrl"); 
	if(jxAppReqUrl==''||jxAppReqUrl==null){
		jxAppReqUrl = 'http://'+location.hostname+':9060'
	}
	if(_uuid==''){
		layer.prompt({title:'请输入本机业务节点地址，默认：http://127.0.0.1:9060',offset:'20px',formType:0,value:jxAppReqUrl},function(value, index, elem){
			doExcute(value);
		});
	}else{
		doExcute(jxAppReqUrl);
	}
	
	function doExcute(value){
		ajax.remoteCall("${ctxPath}/index?action=getRemoteAppList",{reqUrl:value},function(result) {
			localStorage.setItem("jkAppReqUrl", value); 
			if(result.state==1){
				_uuid = result.uuid;
				layer.msg(result.msg,{icon: 1,time:800},function(){
					layer.closeAll();
					var tmp =  $.templates("#remoteAppListTemp");
					var html = tmp.render({list:result.data});
					$("#remote-app-data").html(html);
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
}

</script>
