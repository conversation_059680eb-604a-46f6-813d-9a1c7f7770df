package org.easitline.console.base;

import java.util.Calendar;
import java.util.GregorianCalendar;

/**
 * 服务运行时信息
 * 
 * <AUTHOR>
 * 
 */
public class RuntimeInfo {

	/**
	 * 系统启动时间
	 */
	private static  String boottime = getCurrentDateTime();
	/**
	 * 系统启动的当前毫秒
	 */
	private static  long initTimeMillis = System.currentTimeMillis();


	/**
	 * 获得系统运行时间，返回运行了多少天、多少小时，多少分钟，多少秒。
	 * 
	 * @return String
	 */
	public static  String getRunime() {
		long diff = System.currentTimeMillis() - initTimeMillis;
		long day = diff / (24 * 60 * 60 * 1000l);
		long hour = (diff % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000l);
		long minute = (diff % (60 * 60 * 1000)) / (60 * 1000l);
		long second = (diff % (60 * 1000)) / 1000l;
		String str = "";
		if (day > 0)
			str += day + "天";
		if (hour > 0)
			str += hour + "小时";
		if (minute > 0)
			str += minute + "分钟";
		if (second > 0)
			str += second + "秒";
		return str;
	}
	
	
	public static String getBootTime(){
		return boottime;
	}
	
	/**
	 * 获得当前时间 ,格式：yyyy-mm-dd hh:mm:ss
	 * @return 
	 */
	public static  String getCurrentDateTime() {
		StringBuffer buf = new StringBuffer("");
		Calendar calendar = new GregorianCalendar();
		buf.append(calendar.get(calendar.YEAR));
		buf.append("-");
		buf.append(calendar.get(calendar.MONTH) + 1 > 9 ? calendar.get(calendar.MONTH) + 1 + ""
				: "0" + (calendar.get(calendar.MONTH) + 1));
		buf.append("-");
		buf.append(calendar.get(calendar.DAY_OF_MONTH) > 9 ? calendar.get(calendar.DAY_OF_MONTH) + ""
				: "0" + calendar.get(calendar.DAY_OF_MONTH));
		buf.append(" ");
		buf.append(calendar.get(calendar.HOUR_OF_DAY) > 9 ? calendar.get(calendar.HOUR_OF_DAY) + ""
				: "0" + calendar.get(calendar.HOUR_OF_DAY));
		buf.append(":");
		buf.append(calendar.get(calendar.MINUTE) > 9 ? calendar.get(calendar.MINUTE) + ""
				: "0" + calendar.get(calendar.MINUTE));
		buf.append(":");
		buf.append(calendar.get(calendar.SECOND) > 9 ? calendar.get(calendar.SECOND) + ""
				: "0" + calendar.get(calendar.SECOND));
		return buf.toString();
	}


}
