package org.easitline.console.servlet;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.console.base.ConsoleBaseServlet;
import org.easitline.console.service.ConfigCryptorService;
import org.easitline.console.utils.AesUtils;

import com.alibaba.fastjson.JSONObject;

@WebServlet("/servlet/mqConf/*")
public class MqConfServlet extends ConsoleBaseServlet {

	private static final long serialVersionUID = 7201718476039020390L;

	
	public EasyResult actionForGetConf(){
		try {
			String v1 = getConsoleQuery().queryForString("select CONF_VALUE from EASI_CONF where CONF_KEY = ?", "MQ_ADDR");
			String v2 = getConsoleQuery().queryForString("select CONF_VALUE from EASI_CONF where CONF_KEY = ?", "MQ_USERNAME");
//			String v3 = getConsoleQuery().queryForString("select CONF_VALUE from EASI_CONF where CONF_KEY = ?", "MQ_PASSWORD");
			String v4 = getConsoleQuery().queryForString("select CONF_VALUE from EASI_CONF where CONF_KEY = ?", "MQ_TYPE");
			String v5 = getConsoleQuery().queryForString("select CONF_VALUE from EASI_CONF where CONF_KEY = ?", "MQ_CLUSTER_NAME");
			String v6 = getConsoleQuery().queryForString("select CONF_VALUE from EASI_CONF where CONF_KEY = ?", "MQ_TENANTID");
			String v7 = getConsoleQuery().queryForString("select CONF_VALUE from EASI_CONF where CONF_KEY = ?", "MQ_CONSUMER_GROUP");
			String v8 = getConsoleQuery().queryForString("select CONF_VALUE from EASI_CONF where CONF_KEY = ?", "MQ_PREFIX");
			JSONObject object=new JSONObject();
			object.put("MQ_ADDR", decryptStr(v1));
			object.put("MQ_USERNAME", decryptStr(v2));
			object.put("MQ_PASSWORD", "");
			object.put("MQ_TYPE", decryptStr(v4));
			object.put("MQ_CLUSTER_NAME", decryptStr(v5));
			object.put("MQ_TENANTID", decryptStr(v6));
			object.put("MQ_CONSUMER_GROUP",decryptStr(v7));
			object.put("MQ_PREFIX", decryptStr(v8));
			return EasyResult.ok(AesUtils.getInstance().encrypt(object.toJSONString()));
		} catch (SQLException e) {
			return EasyResult.fail();
		}
	}
	
	private String decryptStr(String str) {
		return ConfigCryptorService.decryptString(str,"GV");
	}
	
	private String encryptStr(String str) {
		return ConfigCryptorService.encryptString(str, "GV");
	}
	
	public EasyResult actionForSaveConf(){
		if(!checkOperateTime().isOk()) {
			return checkOperateTime();
		}
		JSONObject params = getJSONObject();
		String data = params.getString("encryptStr");
		data = AesUtils.getInstance().decrypt(data);
		JSONObject jsonObject = JsonKit.getJSONObject(data,null);
		
		EasyRecord record=new EasyRecord("EASI_CONF","CONF_KEY");
		record.set("CAN_DELETE", 3);
		record.set("CONF_KEY", "MQ_ADDR");
		record.set("CONF_VALUE",encryptStr(jsonObject.getString("MQ_ADDR")));
		try {
			if(getConsoleQuery().queryForExist("select count(1) from EASI_CONF where CONF_KEY = ?", "MQ_ADDR")){
				getConsoleQuery().update(record);
			}else{
				getConsoleQuery().save(record);
			}
			record.set("CAN_DELETE", 3);
			record.set("CONF_KEY", "MQ_USERNAME");
			record.set("CONF_VALUE",encryptStr(jsonObject.getString("MQ_USERNAME")));
			
			if(getConsoleQuery().queryForExist("select count(1) from EASI_CONF where CONF_KEY = ?", "MQ_USERNAME")){
				getConsoleQuery().update(record);
			}else{
				getConsoleQuery().save(record);
			}
			
			record.set("CAN_DELETE", 3);
			record.set("CONF_KEY", "MQ_PASSWORD");
			record.set("CONF_VALUE",encryptStr(jsonObject.getString("MQ_PASSWORD")));
			
			if(getConsoleQuery().queryForExist("select count(1) from EASI_CONF where CONF_KEY = ?", "MQ_PASSWORD")){
				getConsoleQuery().update(record);
			}else{
				getConsoleQuery().save(record);
			}
			
			record.set("CAN_DELETE", 3);
			record.set("CONF_KEY", "MQ_TYPE");
			record.set("CONF_VALUE",jsonObject.getString("MQ_TYPE"));
			
			if(getConsoleQuery().queryForExist("select count(1) from EASI_CONF where CONF_KEY = ?", "MQ_TYPE")){
				getConsoleQuery().update(record);
			}else{
				getConsoleQuery().save(record);
			}
			
			record.set("CAN_DELETE", 3);
			record.set("CONF_KEY", "MQ_CLUSTER_NAME");
			record.set("CONF_VALUE", jsonObject.getString("MQ_CLUSTER_NAME"));
			
			if(getConsoleQuery().queryForExist("select count(1) from EASI_CONF where CONF_KEY = ?", "MQ_CLUSTER_NAME")){
				getConsoleQuery().update(record);
			}else{
				getConsoleQuery().save(record);
			}
			
			record.set("CAN_DELETE", 3);
			record.set("CONF_KEY", "MQ_TENANTID");
			record.set("CONF_VALUE", jsonObject.getString("MQ_TENANTID"));
			
			if(getConsoleQuery().queryForExist("select count(1) from EASI_CONF where CONF_KEY = ?", "MQ_TENANTID")){
				getConsoleQuery().update(record);
			}else{
				getConsoleQuery().save(record);
			}
			
			record.set("CAN_DELETE", 3);
			record.set("CONF_KEY", "MQ_CONSUMER_GROUP");
			record.set("CONF_VALUE",jsonObject.getString("MQ_CONSUMER_GROUP"));
			
			if(getConsoleQuery().queryForExist("select count(1) from EASI_CONF where CONF_KEY = ?", "MQ_CONSUMER_GROUP")){
				getConsoleQuery().update(record);
			}else{
				getConsoleQuery().save(record);
			}
			
			record.set("CAN_DELETE", 3);
			record.set("CONF_KEY", "MQ_PREFIX");
			record.set("CONF_VALUE",jsonObject.getString("MQ_PREFIX"));
			
			if(getConsoleQuery().queryForExist("select count(1) from EASI_CONF where CONF_KEY = ?", "MQ_PREFIX")){
				getConsoleQuery().update(record);
			}else{
				getConsoleQuery().save(record);
			}
			this.addOperateLog("修改MQ参数配置",data);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		ServerContext.reload();
		return EasyResult.ok();
	}
	
	
	
	@Override
	protected String getResId() {
		return null;
	}

}
