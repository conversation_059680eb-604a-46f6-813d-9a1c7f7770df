package org.easitline.console.servlet;

import java.io.IOException;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceRegistor;
import org.easitline.common.core.web.EasyResult;
import org.easitline.console.base.ConsoleBaseServlet;

import com.alibaba.fastjson.JSONObject;

/**
 * 系统数据源管理类
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/soa")
public class SoaServlet extends ConsoleBaseServlet {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//org.easitline.console.servlet.DatasourceServlet.actionForlist
	/**
	 * 列表
	 * @param req
	 * @param resp
	 * @throws SQLException 
	 * @throws ServletException 
	 * @throws IOException
	 */
	public EasyResult actionForList() throws SQLException {
		List<ServiceResource> services = ServiceRegistor.getRegistor().listAllServices();
		List<JSONObject>  list = new ArrayList<JSONObject>();
		for(ServiceResource service: services){
			JSONObject obj = new JSONObject();
			obj.put("appName", service.appName);
			obj.put("serviceId", service.serviceId);
			obj.put("serviceName", service.serviceName);
			obj.put("description", service.description);
			obj.put("className", service.className);
			list.add(obj);
		}
		return EasyResult.ok(list);
	}
	@Override
	protected String getResId() {
		// TODO Auto-generated method stub
		return null;
	}
	
	
	
}
