package org.easitline.console.servlet;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceRegistor;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.console.base.ConsoleBaseServlet;
import org.easitline.console.base.Constants;
import org.easitline.console.job.JobLogManager;
import org.easitline.console.job.JobManager;
import org.easitline.console.job.QuartzSchedulerEngine;
import org.easitline.console.vo.AppJobModel;
import org.easitline.console.vo.AppJobRowMapper;
import org.easitline.console.vo.EsbServiceModel;
import org.easitline.console.vo.JobLogModel;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.SchedulerFactory;
import org.quartz.TriggerKey;
import org.quartz.impl.StdSchedulerFactory;

import com.alibaba.fastjson.JSONObject;


/**
 * 调度任务请求处理类
 */
@WebServlet("/servlet/job/*")
public class JobServlet extends ConsoleBaseServlet {

	private static final long serialVersionUID = 1L;
	private JobManager jobManager = new JobManager();
	private JobLogManager jobLogManager = new JobLogManager();
	
	
	
	/**
	 * 打开选择服务页面
	 * @return
	 * @throws Exception
	 */
	public String actionForSelectSubServicePage() throws Exception {
		 
		List<ServiceResource> list = ServiceRegistor.getRegistor().listAllServices();
		this.getRequest().setAttribute("list", list);
		return "/pages/job/select-sub-service.jsp";
	}
	
	/**
	 * 打开选择服务页面
	 * @return
	 * @throws Exception
	 */
	public String actionForServicePage() throws Exception {
		List<ServiceResource> list = ServiceRegistor.getRegistor().listAllServices();
		List<ServiceResource> cronList = new ArrayList<ServiceResource>();
		for(ServiceResource service:list) {
			String serviceId = service.serviceId;
			if(null != serviceId && serviceId.toUpperCase().startsWith(Constants.CRON_SERVICE_PREFIX)) {
				cronList.add(service);
			}
		}
		this.getRequest().setAttribute("list", cronList);
		return "/pages/job/select-service.jsp";
	}
	
	/**
	 * 打开任务日志
	 * @return
	 * @throws Exception
	 */
	public EasyResult actionForViewJobLog() throws Exception {
		JSONObject jsonObject = this.getJSONObject();
		String jobId = jsonObject.getString("jobId");
		List<JobLogModel> jobLogList = jobLogManager.getJobLogListByJobId(jobId);
		this.getRequest().setAttribute("list", jobLogList);
		return EasyResult.ok(jobLogList);
	}
	
	 public EasyResult actionForQuartzJobPause() {
		 JSONObject params = this.getJSONObject();
		 String name = params.getString("triggerName");
		 String groupName = params.getString("triggerGroup");
		 try {
			 SchedulerFactory schedulerFactory = new StdSchedulerFactory();
			 Scheduler scheduler = schedulerFactory.getScheduler();
			 TriggerKey triggerKey = TriggerKey.triggerKey(name, groupName);
			 scheduler.pauseTrigger(triggerKey);
			 JobKey jobKey = new JobKey(name, groupName);
			scheduler.pauseJob(jobKey);
		} catch (SchedulerException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	 }
	 
	 public EasyResult actionForQuartzJobStart(){
		 JSONObject params = this.getJSONObject();
		 String name = params.getString("triggerName");
		 String groupName = params.getString("triggerGroup");
		 try {
			 SchedulerFactory schedulerFactory = new StdSchedulerFactory();
			 Scheduler scheduler = schedulerFactory.getScheduler();
			 TriggerKey triggerKey = TriggerKey.triggerKey(name, groupName);
			 scheduler.resumeTrigger(triggerKey);
			 JobKey jobKey = new JobKey(name, groupName);
			scheduler.resumeJob(jobKey);
		} catch (SchedulerException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	 }
	 
	/**
	 * 暂停调试任务
	 * @return
	 * @throws Exception
	 */
    public EasyResult actionForPause() throws Exception {
		JSONObject jsonObject = this.getJSONObject();
        String jobId = jsonObject.getString("jobId");
        if(StringUtils.isBlank(jobId)) {
			return EasyResult.error(501,"暂停失败，原因：任务标识不能为空");
        }
		try {
			String sql = "select * from EASI_JOB_STEP where JOB_ID=?";
			Object[] params = {jobId};
			AppJobModel job = this.getSysDefaultQuery().queryForRow(sql, params, new AppJobRowMapper());
			
			QuartzSchedulerEngine.pauseJob(job.getJobId(), job.getAppName());  //暂停调度任务
					
			jobManager.updateJobState(jobId, Constants.JobState.PAUSE.getKey());   //更新调度任务状态
			
			return EasyResult.ok(null,"暂停成功");
		} catch (Exception ex) {
			this.error(ex.getMessage(), ex);
			return EasyResult.error(501,"JobServlet.actionForPause暂停失败，原因：" + ex.getMessage() );
		}
	}
	
	/**
	 * 启动调试任务
	 * @return
	 * @throws Exception
	 */
    public EasyResult actionForStart() throws Exception {
		
		JSONObject jsonObject = this.getJSONObject();
        String jobId = jsonObject.getString("jobId");
        
        String isJobServer = ServerContext.getProperties("G_JOB_SERVER", "false");
		if("false".equals(isJobServer)){
			return EasyResult.error(501,"调度模块尚未启用");
		}
        
        if(StringUtils.isBlank(jobId)) {
        	return EasyResult.error(501,"启动失败，原因：任务标识不能为空");
        }

		try {
			AppJobModel job = jobManager.getJobById(jobId);
			
			QuartzSchedulerEngine.resumeJob(job.getJobId(), job.getAppName(), job.getCron());  //启动调度任务
			
			jobManager.updateJobState(jobId, Constants.JobState.NORMAL.getKey());   //更新调度任务状态
			
			return EasyResult.ok(null,"启动成功");
		} catch (Exception ex) {
			this.error(ex.getMessage(), ex);
			return EasyResult.error(501,"JobServlet.actionForStart暂停失败，原因：" + ex.getMessage() );
		}
	}
	
	/**
	 * 删除调试任务
	 * @return
	 * @throws Exception
	 */
	public EasyResult actionForDelete() throws Exception {
		
		JSONObject jsonObject = this.getJSONObject();
        String jobId = jsonObject.getString("jobId");
        
        if(StringUtils.isBlank(jobId)) {
			return EasyResult.error(501,"删除失败，原因：任务标识不能为空");
        }
		try {
			
			AppJobModel job = jobManager.getJobById(jobId);
			
			QuartzSchedulerEngine.removeJob(job.getJobId(), job.getAppName());  //从schedule对象中去掉job
			
			jobManager.deleteJob(jobId);  //从数据库表中删除调度任务
			
			return EasyResult.ok(null,"删除成功");
		} catch (SQLException ex) {
			this.error(ex.getMessage(), ex);
			return EasyResult.error(501,"删除失败，原因："+ex.getMessage());
		}
	}
	
	/**
	 * 保存调度任务
	 * @param req
	 * @param resp
	 * @throws Exception 
	 */
	public EasyResult actionForSave() throws Exception {
				
		String jobId = this.getJSONObject().getString("jobIdInput");
		JSONObject jsonObject = this.getJSONObject("job");
		EasyResult result = new EasyResult();
        String jobName = jsonObject.getString("jobName");
        String esbServiceId = jsonObject.getString("serviceId");
        String esbServiceName = "";
        String cron = jsonObject.getString("cron");
        String appName = jsonObject.getString("appName");
        String jobDesc = jsonObject.getString("jobDesc");
        String beginDate = jsonObject.getString("beginDate");
        String endDate = jsonObject.getString("endDate");
        String beginTime = jsonObject.getString("beginTime");
        String endTime = jsonObject.getString("endTime");
	
		String nodeName = ServerContext.getNodeName();       //节点名称
		
		String isJobServer = ServerContext.getProperties("G_JOB_SERVER", "false");
		if("false".equals(isJobServer)){
			return EasyResult.error(501,"调度模块尚未启用");
		}
		
		//获取对应的esb服务名
		List<ServiceResource> list = ServiceRegistor.getRegistor().listAllServices();
		for(ServiceResource service:list) {
			String serviceId = service.serviceId;
			if(null != serviceId && serviceId.toUpperCase().equals(esbServiceId)) {
				esbServiceName = service.serviceName;
			}
		}
		
		if(StringUtils.isBlank(jobId)) {
			jobId = RandomKit.randomStr();
			QuartzSchedulerEngine.addJob(appName, jobId, cron);		//创建调度任务
			result = jobManager.addJobInfo(jobId, jobName, appName, cron, nodeName, esbServiceId, esbServiceName, jobDesc, beginDate, endDate, beginTime, endTime);
		} else {
			
			AppJobModel job = jobManager.getJobById(jobId);
			
			if(!job.getCron().equals(cron)) {   //只有修改了cron表达式才需要更新调度任务
				QuartzSchedulerEngine.updateJob(appName, jobId, cron);	
			}

			result = jobManager.modifyJobInfo(jobId, jobName, appName, cron, esbServiceId, esbServiceName, jobDesc);
		}
		
		return result;
	}
	
	/**
	 * 获取调度任务的ESB服务列表
	 */
    private List<EsbServiceModel> getCronEsbServiceList() {
    	
    	List<ServiceResource> serviceList = ServiceRegistor.getRegistor().listAllServices();
		List<EsbServiceModel> esbServiceList = new ArrayList<EsbServiceModel>();
		
		for(ServiceResource service : serviceList) {
			String serviceId = service.serviceId;
			if(null != serviceId && serviceId.toUpperCase().startsWith(Constants.CRON_SERVICE_PREFIX)) {
				
				EsbServiceModel model = new EsbServiceModel();
				model.setServiceId(service.serviceId);
				model.setServiceName(service.serviceName);
				esbServiceList.add(model);
			}
		}

		return esbServiceList;
    }
	
	/**
	 * 系统调度列表 
	 * @param req
	 * @param resp
	 * @throws SQLException 
	 * @throws ServletException 
	 * @throws IOException
	 */
	public EasyResult actionForList() throws SQLException {

		String sql = "select t1.* from EASI_JOB_STEP t1 ";
		
		Object[] params = new Object[]{};
		/*String nodeName = ServerContext.getNodeName();
		if(null != nodeName && !"".equals(nodeName)) {
			sql += " where t1.server_name=?";
			params = new Object[]{nodeName};
		}*/

		List<AppJobModel> list = this.getSysDefaultQuery().queryForList(sql, params,new AppJobRowMapper());
		return EasyResult.ok(list);
	}

	@Override
	protected String getResId() {
		return null;
	}
}
