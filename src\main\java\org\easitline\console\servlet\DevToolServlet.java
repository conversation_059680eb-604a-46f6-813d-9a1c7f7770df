package org.easitline.console.servlet;

import java.io.File;
import java.util.Collection;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.Part;

import org.easitline.common.core.Globals;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.console.base.ConsoleBaseServlet;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
/**
 *文件上传辅助类
 *
 */
//@WebServlet(urlPatterns = {"/devTool/*"})
//@MultipartConfig(maxFileSize=1024*1024*50)
public class DevToolServlet extends ConsoleBaseServlet{
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForUpload(){
		File devLockFile = new File(Globals.BASE_DIR+File.separator+"dev.lock");
		if(!devLockFile.exists()) {
			return EasyResult.fail("非开发环境");
		}
		
		
		HttpServletRequest request = getRequest();
		try {
			Collection<Part> fileList = request.getParts();
			String filedDirectory = getPara("filedDirectory");
			String toServerPath = getPara("dir");
			String fileName = getPara("fileName");
			if(fileList==null||fileList.size()<=0){
				return EasyResult.fail("未选择文件.");
			}
			
			JSONArray list = new JSONArray();
			
			for (Part part:fileList) {
				if(part.getSubmittedFileName()==null){
					part.delete();
					continue;
			    }
				
				File newFile = null;
				String basePath = Globals.WEBAPPS_DIR;
				
				if(fileName.endsWith(".war")) {
					newFile = new File(basePath +File.separator + fileName);
				}else {
					File dirFile = new File(basePath +File.separator + toServerPath);
					if(!dirFile.exists()) {
						dirFile.mkdirs();
					}
					newFile = new File(basePath +File.separator + toServerPath+File.separator + fileName);
					newFile.deleteOnExit();
				}
				this.info("上传文件至>"+newFile.getAbsolutePath(), null);
				
				FileKit.saveToFile(part.getInputStream(), newFile.getAbsolutePath());
				part.delete();
				
				list.add(new JSONObject());
				
			}
			if(list.size()>0) {
				return EasyResult.ok(list, "上传成功!");
			}else {
				return EasyResult.fail("上传失败!");
			}
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
	    }
	}
	
	private void processFolder(File folder, String destinationPath) {
        File[] files = folder.listFiles();
        for (File file : files) {
            if (file.isDirectory()) {
                // 如果是子文件夹，递归处理
                processFolder(file, destinationPath + file.getName() + "/");
            } else {
                // 如果是文件，移动到目标位置
                file.renameTo(new File(destinationPath + file.getName()));
            }
        }
    }
    
    private void deleteFolder(File folder) {
        File[] files = folder.listFiles();
        for (File file : files) {
            if (file.isDirectory()) {
                // 如果是子文件夹，递归删除
                deleteFolder(file);
            } else {
                // 如果是文件，删除
                file.delete();
            }
        }
        // 删除空文件夹
        folder.delete();
    }
		
}
