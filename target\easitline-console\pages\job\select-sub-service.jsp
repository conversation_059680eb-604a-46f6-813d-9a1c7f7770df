<%@page import="org.easitline.common.core.resource.ServiceResource"%>
<%@page import="java.util.List"%>
<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE HTML>
 <%
   List<ServiceResource> list = (List<ServiceResource>)request.getAttribute("list");
 %>
<div class="modal-header">
	<button class="sui-close" aria-hidden="true" type="button"
		data-dismiss="modal">×</button>
	<h4 class="modal-title" id="mgrs_label">选择子服务</h4>
</div>
<div class="modal-body">
 <!-- 面板1 -->
 <div class="panel mb-20">
   <div class="easitline-panel">
         <div class="easitline-panel-content">
           <ul class="sui-tag tag-bordered" id="tagUl" style="height:30px">
			  <!-- <li>Normal</li>
			  <li class="tag-selected with-x">logo设计<i>×</i></li>
			  <li>店铺装修达人</li>
			  <li class="tag-selected with-x">艺术<i>×</i></li>
			  <li>公sdf告</li>
			  <li class="tag-selected with-x">注意afs<i>×</i></li>
			  <li>店铺装修达人</li>
			  <li class="tag-selected with-x">艺术<i>×</i></li>
			  <li>公sdf告</li> -->
			</ul>
           <!-- table添加table-zebra2 class即可实现隔行换色 -->
           <table class="sui-table table-zebra2">
             <thead>
               <tr> 
                 <th class="text-center">选择</th>
                 <th width="150" class="text-center">服务ID</th>
                 <th class="text-center">服务名称</th>
                 <th class="text-center">服务实现类</th>
               </tr>
             </thead>
             <tbody>
             <%for(ServiceResource resource:list){ %>
               <tr class="">
                 <td class="text-center">
                     <input name="servieId" type="checkbox"  serviceId='<%=resource.serviceId%>' serviceName='<%=resource.serviceName%>'  onclick='selectService(this);' />
                 </td>
                 <td class="text-center"><%=resource.serviceId%></td>
                 <td class="text-center"><%=resource.serviceName%></td>
                 <td class="text-center"><%=resource.className%></td>
                </tr>
            <%}%>
             </tbody>
           </table>
         </div>
   </div>
 </div>
 </div>
<input type="hidden" id="serviceIdHide" value="" />
<input type="hidden" id="serviceNameHide" value="" />
<div class="modal-footer">					
	<button class="sui-btn btn-primary btn-large" type="button" id="mgrs_ok">确定</button>
	<button class="sui-btn btn-default btn-large" type="button" data-dismiss="modal">关闭</button>
</div>
<script type="text/javascript">
$(function(){
	
	 //确定按钮事件
	 $("#mgrs_ok").click(function(){	
		 
		    var subServiceIdAry = new Array();
		   $("#tagUl li i").each(function(index, item){
		    	var serviceId = $(item).attr("serviceId");
		    	subServiceIdAry.push(serviceId);
		    }); 
	    	callbackSubService(subServiceIdAry.join("|"));
	    	$("#lookup_modal").modal("hide");
	  });
});

//选择服务
function selectService(e) {
	
	var serviceId = $(e).attr("serviceId");
	var serviceName = $(e).attr("serviceName");
    var html = "<li class='tag-selected with-x'>" + serviceName + "<i serviceId='" + serviceId + "'>×</i></li>";
	$("#tagUl").append(html);
	
	$("#tagUl li i").click(function() {
		 $(this).parent().remove();
		 
		 $("input[type=checkbox]").each(function(index, item) {
			 var chkServiceId = $(item).attr("serviceId");
			 if(chkServiceId == serviceId) {
				 $(item).prop("checked", false);
			 }
		 })
	 });
}
</script>