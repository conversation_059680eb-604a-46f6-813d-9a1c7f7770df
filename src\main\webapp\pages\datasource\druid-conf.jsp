<%@page pageEncoding="UTF-8"%>
<form class="layui-form" method="post" id="confEasyform">
	  <div class="layui-form-item" style="margin-top: 10px;">
	    <label class="layui-form-label">获取链接最大等待时间</label>
	    <div class="layui-input-block">
	      <input type="text" name="maxWait" placeholder="默认：10000，单位：毫秒" class="layui-input">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">限制的等待线程个数</label>
	    <div class="layui-input-block">
	      <input type="number" name="maxWaitThreadCount" placeholder="默认：-1"  class="layui-input">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">回收空闲连接</label>
	    <div class="layui-input-block">
	      <input type="number" name="minEvictableIdleTimeMillis" readonly="readonly" placeholder="默认：30000，单位：毫秒"  class="layui-input">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">检查空闲连接的频率</label>
	    <div class="layui-input-block">
	      <input type="number" name="timeBetweenEvictionRunsMillis" readonly="readonly" placeholder="默认：60000，单位：毫秒" class="layui-input">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">检查空闲连接的频率</label>
	    <div class="layui-input-block">
	      <input type="number" name="timeBetweenEvictionRunsMillis" readonly="readonly" placeholder="默认：60000，单位：毫秒" class="layui-input">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">数据库连接超时时间</label>
	    <div class="layui-input-block">
	      <input type="number" name="connectTimeout" value="60000" placeholder="默认：60000，单位：毫秒" class="layui-input">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">数据库socket的超时时间</label>
	    <div class="layui-input-block">
	      <input type="number" name="socketTimeout" placeholder="默认：60000，单位：毫秒" class="layui-input">
	    </div>
	  </div>
	 <!--  <div class="layui-form-item">
	    <label class="layui-form-label">测试链接</label>
	    <div class="layui-input-block">
	      <input type="text" name="validationQuery" placeholder="默认：SELECT 1" class="layui-input">
	    </div>
	  </div> -->
	  <div class="layui-form-item">
	    <label class="layui-form-label">防火墙</label>
	    <div class="layui-input-block">
	      <input type="text" name="filters" placeholder="默认：stat;如支持wall可设置为stat,wall" class="layui-input">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">强制回收连接的时限</label>
	    <div class="layui-input-block">
	      <input type="number" name="removeAbandonedTimeout" readonly="readonly" placeholder="默认：180 单位：秒" class="layui-input">
	    </div>
	  </div>
	  <div class="layui-form-item">
	    <label class="layui-form-label">超过时间限制是否回收</label>
	    <div class="layui-input-block">
	      <input type="text" name="removeAbandoned" placeholder="默认：true" class="layui-input">
	    </div>
	  </div>
</form>

<script>
    $(function(){
    	layui.use('form', function(){
	         var layuiform = layui.form;
		 });
	});
	
</script>